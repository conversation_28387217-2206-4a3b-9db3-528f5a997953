"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const index_1 = require("../../../common/utils/index");
const export_1 = require("../../../common/utils/export");
const index_2 = require("../../../common/enum/index");
const role_entity_1 = require("./entities/role.entity");
const role_width_menu_entity_1 = require("./entities/role-width-menu.entity");
const role_width_dept_entity_1 = require("./entities/role-width-dept.entity");
const dept_entity_1 = require("../dept/entities/dept.entity");
const menu_service_1 = require("../menu/menu.service");
let RoleService = class RoleService {
    constructor(sysRoleEntityRep, sysRoleWithMenuEntityRep, sysRoleWithDeptEntityRep, sysDeptEntityRep, menuService) {
        this.sysRoleEntityRep = sysRoleEntityRep;
        this.sysRoleWithMenuEntityRep = sysRoleWithMenuEntityRep;
        this.sysRoleWithDeptEntityRep = sysRoleWithDeptEntityRep;
        this.sysDeptEntityRep = sysDeptEntityRep;
        this.menuService = menuService;
    }
    async create(createRoleDto) {
        const res = await this.sysRoleEntityRep.save(createRoleDto);
        const entity = this.sysRoleWithMenuEntityRep.createQueryBuilder('entity');
        const values = createRoleDto.menuIds.map((id) => {
            return {
                roleId: res.roleId,
                menuId: id,
            };
        });
        entity.insert().values(values).execute();
        return result_1.ResultData.ok(res);
    }
    async findAll(query) {
        const entity = this.sysRoleEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.roleName) {
            entity.andWhere(`entity.roleName LIKE "%${query.roleName}%"`);
        }
        if (query.roleKey) {
            entity.andWhere(`entity.roleKey LIKE "%${query.roleKey}%"`);
        }
        if (query.roleId) {
            entity.andWhere('entity.roleId = :roleId', { roleId: query.roleId });
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOne(roleId) {
        const res = await this.sysRoleEntityRep.findOne({
            where: {
                roleId: roleId,
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(res);
    }
    async update(updateRoleDto) {
        const hasId = await this.sysRoleWithMenuEntityRep.findOne({
            where: {
                roleId: updateRoleDto.roleId,
            },
            select: ['roleId'],
        });
        if (hasId) {
            await this.sysRoleWithMenuEntityRep.delete({
                roleId: updateRoleDto.roleId,
            });
        }
        const entity = this.sysRoleWithMenuEntityRep.createQueryBuilder('entity');
        const values = updateRoleDto.menuIds.map((id) => {
            return {
                roleId: updateRoleDto.roleId,
                menuId: id,
            };
        });
        delete updateRoleDto.menuIds;
        entity.insert().values(values).execute();
        const res = await this.sysRoleEntityRep.update({ roleId: updateRoleDto.roleId }, updateRoleDto);
        return result_1.ResultData.ok(res);
    }
    async dataScope(updateRoleDto) {
        const hasId = await this.sysRoleWithDeptEntityRep.findOne({
            where: {
                roleId: updateRoleDto.roleId,
            },
            select: ['roleId'],
        });
        if (hasId || updateRoleDto.dataScope !== index_2.DataScopeEnum.DATA_SCOPE_CUSTOM) {
            await this.sysRoleWithDeptEntityRep.delete({
                roleId: updateRoleDto.roleId,
            });
        }
        const entity = this.sysRoleWithDeptEntityRep.createQueryBuilder('entity');
        const values = updateRoleDto.deptIds.map((id) => {
            return {
                roleId: updateRoleDto.roleId,
                deptId: id,
            };
        });
        entity.insert().values(values).execute();
        delete updateRoleDto.deptIds;
        const res = await this.sysRoleEntityRep.update({ roleId: updateRoleDto.roleId }, updateRoleDto);
        return result_1.ResultData.ok(res);
    }
    async changeStatus(changeStatusDto) {
        const res = await this.sysRoleEntityRep.update({ roleId: changeStatusDto.roleId }, {
            status: changeStatusDto.status,
        });
        return result_1.ResultData.ok(res);
    }
    async remove(roleIds) {
        const data = await this.sysRoleEntityRep.update({ roleId: (0, typeorm_2.In)(roleIds) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async deptTree(roleId) {
        const res = await this.sysDeptEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        const tree = (0, index_1.ListToTree)(res, (m) => +m.deptId, (m) => m.deptName);
        const deptIds = await this.sysRoleWithDeptEntityRep.find({
            where: { roleId: roleId },
            select: ['deptId'],
        });
        const checkedKeys = deptIds.map((item) => {
            return item.deptId;
        });
        return result_1.ResultData.ok({
            depts: tree,
            checkedKeys: checkedKeys,
        });
    }
    async findRoles(where) {
        return await this.sysRoleEntityRep.find(where);
    }
    async getPermissionsByRoleIds(roleIds) {
        if (roleIds.includes(1))
            return [{ perms: '*:*:*' }];
        const list = await this.sysRoleWithMenuEntityRep.find({
            where: {
                roleId: (0, typeorm_2.In)(roleIds),
            },
            select: ['menuId'],
        });
        const menuIds = list.map((item) => item.menuId);
        const permission = await this.menuService.findMany({
            where: { delFlag: '0', status: '0', menuId: (0, typeorm_2.In)(menuIds) },
        });
        return permission;
    }
    async findRoleWithDeptIds(roleId) {
        const res = await this.sysRoleWithDeptEntityRep.find({
            select: ['deptId'],
            where: {
                roleId: roleId,
            },
        });
        return res.map((item) => item.deptId);
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAll(body);
        const options = {
            sheetName: '角色数据',
            data: list.data.list,
            header: [
                { title: '角色编号', dataIndex: 'roleId' },
                { title: '角色名称', dataIndex: 'roleName', width: 15 },
                { title: '权限字符', dataIndex: 'roleKey' },
                { title: '显示顺序', dataIndex: 'roleSort' },
                { title: '状态', dataIndex: 'status' },
                { title: '创建时间', dataIndex: 'createTime', width: 15 },
            ],
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.RoleService = RoleService;
exports.RoleService = RoleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.SysRoleEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(role_width_menu_entity_1.SysRoleWithMenuEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(role_width_dept_entity_1.SysRoleWithDeptEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(dept_entity_1.SysDeptEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        menu_service_1.MenuService])
], RoleService);
//# sourceMappingURL=role.service.js.map