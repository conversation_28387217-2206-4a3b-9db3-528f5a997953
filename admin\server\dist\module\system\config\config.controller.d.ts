import { Response } from 'express';
import { ConfigService } from './config.service';
import { CreateConfigDto, UpdateConfigDto, ListConfigDto } from './dto/index';
export declare class ConfigController {
    private readonly configService;
    constructor(configService: ConfigService);
    create(createConfigDto: CreateConfigDto, req: any): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListConfigDto): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): Promise<import("../../../common/utils/result").ResultData>;
    findOneByconfigKey(configKey: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updateConfigDto: UpdateConfigDto): Promise<import("../../../common/utils/result").ResultData>;
    refreshCache(): Promise<import("../../../common/utils/result").ResultData>;
    remove(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListConfigDto): Promise<void>;
}
