import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare enum RoleTypeEnum {
    M = "M",
    C = "C",
    F = "F"
}
export declare class CreateRoleDto {
    roleName: string;
    roleKey: string;
    menuIds?: Array<number>;
    deptIds?: Array<number>;
    roleSort?: number;
    status?: string;
    dataScope: string;
    remark?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
}
export declare class UpdateRoleDto extends CreateRoleDto {
    roleId: number;
}
export declare class ChangeStatusDto {
    roleId: number;
    status: string;
}
export declare class ListRoleDto extends PagingDto {
    roleName?: string;
    roleKey?: string;
    status?: string;
    roleId?: string;
}
export declare class AuthUserCancelDto {
    roleId: number;
    userId: number;
}
export declare class AuthUserCancelAllDto {
    roleId: number;
    userIds: string;
}
export declare class AuthUserSelectAllDto {
    roleId: number;
    userIds: string;
}
