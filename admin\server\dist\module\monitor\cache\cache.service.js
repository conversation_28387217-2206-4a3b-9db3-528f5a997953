"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../common/redis/redis.service");
const index_1 = require("../../../common/utils/index");
const result_1 = require("../../../common/utils/result");
let CacheService = class CacheService {
    constructor(redisService) {
        this.redisService = redisService;
        this.caches = [
            {
                cacheName: 'login_tokens:',
                cacheKey: '',
                cacheValue: '',
                remark: '用户信息',
            },
            {
                cacheName: 'sys_config:',
                cacheKey: '',
                cacheValue: '',
                remark: '配置信息',
            },
            {
                cacheName: 'sys_dict:',
                cacheKey: '',
                cacheValue: '',
                remark: '数据字典',
            },
            {
                cacheName: 'captcha_codes:',
                cacheKey: '',
                cacheValue: '',
                remark: '验证码',
            },
            {
                cacheName: 'repeat_submit:',
                cacheKey: '',
                cacheValue: '',
                remark: '防重提交',
            },
            {
                cacheName: 'rate_limit:',
                cacheKey: '',
                cacheValue: '',
                remark: '限流处理',
            },
            {
                cacheName: 'pwd_err_cnt:',
                cacheKey: '',
                cacheValue: '',
                remark: '密码错误次数',
            },
        ];
    }
    async getNames() {
        return result_1.ResultData.ok(this.caches);
    }
    async getKeys(id) {
        const data = await this.redisService.keys(id + '*');
        return result_1.ResultData.ok(data);
    }
    async clearCacheKey(id) {
        const data = await this.redisService.del(id);
        return result_1.ResultData.ok(data);
    }
    async clearCacheName(id) {
        const keys = await this.redisService.keys(id + '*');
        const data = await this.redisService.del(keys);
        return result_1.ResultData.ok(data);
    }
    async clearCacheAll() {
        const data = await this.redisService.reset();
        return result_1.ResultData.ok(data);
    }
    async getValue(params) {
        const list = (0, index_1.DeepClone)(this.caches);
        const data = list.find((item) => item.cacheName === params.cacheName);
        const cacheValue = await this.redisService.get(params.cacheKey);
        data.cacheValue = JSON.stringify(cacheValue);
        data.cacheKey = params.cacheKey;
        return result_1.ResultData.ok(data);
    }
    async getInfo() {
        const info = await this.redisService.getInfo();
        const dbSize = await this.redisService.getDbSize();
        const commandStats = await this.redisService.commandStats();
        return result_1.ResultData.ok({
            dbSize: dbSize,
            info: info,
            commandStats: commandStats,
        });
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], CacheService);
//# sourceMappingURL=cache.service.js.map