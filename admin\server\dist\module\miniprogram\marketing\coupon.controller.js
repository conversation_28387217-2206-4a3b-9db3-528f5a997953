"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CouponController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const coupon_service_1 = require("./coupon.service");
const create_coupon_dto_1 = require("./dto/coupon/create-coupon.dto");
const update_coupon_dto_1 = require("./dto/coupon/update-coupon.dto");
const query_coupon_dto_1 = require("./dto/coupon/query-coupon.dto");
const user_coupon_dto_1 = require("./dto/coupon/user-coupon.dto");
const distribute_coupon_dto_1 = require("./dto/coupon/distribute-coupon.dto");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("../../system/user/user.decorator");
const coupon_response_dto_1 = require("./dto/coupon/coupon-response.dto");
let CouponController = CouponController_1 = class CouponController {
    constructor(couponService) {
        this.couponService = couponService;
        this.logger = new common_1.Logger(CouponController_1.name);
    }
    async createCoupon(createCouponDto) {
        const adminUserId = 1;
        const result = await this.couponService.create(createCouponDto, adminUserId);
        return {
            code: 200,
            message: '创建优惠券成功',
            data: result,
        };
    }
    async updateCoupon(updateCouponDto) {
        const adminUserId = 1;
        const result = await this.couponService.update(updateCouponDto, adminUserId);
        return {
            code: 200,
            message: '更新优惠券成功',
            data: result,
        };
    }
    async getCouponList(queryParams) {
        const result = await this.couponService.findAll(queryParams);
        return {
            code: result.code,
            message: result.msg,
            data: result.data,
        };
    }
    async getCouponDetail(couponId) {
        const result = await this.couponService.findOne(couponId);
        return {
            code: 200,
            message: '查询优惠券详情成功',
            data: result,
        };
    }
    async deleteCoupon(couponId) {
        const adminUserId = 1;
        await this.couponService.remove(couponId, adminUserId);
        return {
            code: 200,
            message: '删除优惠券成功',
            data: null,
        };
    }
    async receiveCoupon(receiveCouponDto) {
        const { userId, couponId } = receiveCouponDto;
        return await this.couponService.userReceiveCoupon(userId, couponId);
    }
    async getUserCouponList(queryParams) {
        const { userId, status } = queryParams;
        const result = await this.couponService.getUserCoupons(userId, status);
        return {
            code: 200,
            message: '查询用户优惠券列表成功',
            data: result,
        };
    }
    async useCoupon(useCouponDto) {
        const { userId, userCouponId, orderId } = useCouponDto;
        await this.couponService.useCoupon(userCouponId, orderId, userId);
        return {
            code: 200,
            message: '使用优惠券成功',
            data: null,
        };
    }
    async getAvailableCoupons(userId, orderAmount) {
        const result = await this.couponService.findUserAvailableCoupons(userId, orderAmount);
        return {
            code: 200,
            message: '获取用户可用优惠券成功',
            data: result,
        };
    }
    async getAvailableToReceiveCoupons(query) {
        try {
            this.logger.log(`获取用户${query.userId}可领取优惠券列表`);
            const result = await this.couponService.findAvailableToReceive(query.userId);
            return result_1.ResultData.ok(result);
        }
        catch (error) {
            this.logger.error(`获取用户${query.userId}可领取优惠券列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取可领取优惠券列表失败');
        }
    }
    async testConcurrentReceive(params) {
        try {
            const startTime = Date.now();
            const { userCount, couponId } = params;
            if (userCount <= 0 || userCount > 100) {
                return result_1.ResultData.fail(400, '用户数量必须在1-100之间');
            }
            const tasks = [];
            for (let i = 1; i <= userCount; i++) {
                const userId = 10000 + i;
                tasks.push(this.couponService.userReceiveCoupon(userId, couponId).catch((err) => {
                    return { error: err.message, userId };
                }));
            }
            const results = await Promise.all(tasks);
            const successCount = results.filter((r) => !r.error).length;
            const errorCount = results.filter((r) => r.error).length;
            const endTime = Date.now();
            return result_1.ResultData.ok({
                totalRequests: userCount,
                successCount,
                errorCount,
                errors: results.filter((r) => r.error).map((r) => r.error),
                timeUsed: `${(endTime - startTime) / 1000}秒`,
            });
        }
        catch (error) {
            this.logger.error(`模拟高并发领券失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '模拟高并发领券失败');
        }
    }
    async distributeCoupon(distributeCouponDto) {
        try {
            const adminUserId = 1;
            this.logger.log(`管理员发放优惠券: ${JSON.stringify(distributeCouponDto)}`);
            const result = await this.couponService.distributeCouponToUsers(distributeCouponDto, adminUserId);
            return result;
        }
        catch (error) {
            this.logger.error(`发放优惠券失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '发放优惠券失败');
        }
    }
    async getUserList(queryParams) {
        try {
            this.logger.log(`查询用户列表: ${JSON.stringify(queryParams)}`);
            const result = await this.couponService.getUserList(queryParams);
            return result;
        }
        catch (error) {
            this.logger.error(`查询用户列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询用户列表失败');
        }
    }
    async getDistributeRecords(couponId, queryParams) {
        try {
            this.logger.log(`查询优惠券${couponId}发放记录`);
            const result = await this.couponService.getDistributeRecords(couponId, queryParams);
            return result;
        }
        catch (error) {
            this.logger.error(`查询发放记录失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询发放记录失败');
        }
    }
};
exports.CouponController = CouponController;
__decorate([
    (0, common_1.Post)('admin/create'),
    (0, swagger_1.ApiOperation)({ summary: '创建优惠券', description: '管理员创建新的优惠券' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_coupon_dto_1.CreateCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "createCoupon", null);
__decorate([
    (0, common_1.Put)('admin/update'),
    (0, swagger_1.ApiOperation)({ summary: '更新优惠券', description: '管理员更新优惠券信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_coupon_dto_1.UpdateCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "updateCoupon", null);
__decorate([
    (0, common_1.Get)('admin/list'),
    (0, swagger_1.ApiOperation)({ summary: '查询优惠券列表', description: '管理员查询优惠券列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_coupon_dto_1.QueryCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getCouponList", null);
__decorate([
    (0, common_1.Get)('admin/detail/:couponId'),
    (0, swagger_1.ApiOperation)({ summary: '获取优惠券详情', description: '根据ID获取优惠券详情' }),
    (0, swagger_1.ApiParam)({ name: 'couponId', description: '优惠券ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Param)('couponId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getCouponDetail", null);
__decorate([
    (0, common_1.Delete)('admin/delete/:couponId'),
    (0, swagger_1.ApiOperation)({ summary: '删除优惠券', description: '管理员删除优惠券' }),
    (0, swagger_1.ApiParam)({ name: 'couponId', description: '优惠券ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Param)('couponId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "deleteCoupon", null);
__decorate([
    (0, common_1.Post)('receive'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '领取优惠券', description: '用户领取优惠券' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '领取成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_coupon_dto_1.ReceiveCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "receiveCoupon", null);
__decorate([
    (0, common_1.Get)('user/list'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户优惠券列表', description: '查询用户的优惠券列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_coupon_dto_1.UserCouponQueryDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getUserCouponList", null);
__decorate([
    (0, common_1.Post)('use'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '使用优惠券', description: '用户使用优惠券' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '使用成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_coupon_dto_1.UseCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "useCoupon", null);
__decorate([
    (0, common_1.Get)('user/available'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户可用优惠券', description: '获取用户可用于指定订单金额的优惠券列表' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiQuery)({ name: 'orderAmount', description: '订单金额' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: coupon_response_dto_1.CouponResponse }),
    __param(0, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('orderAmount', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getAvailableCoupons", null);
__decorate([
    (0, common_1.Get)('available-to-receive'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户可领取优惠券列表', description: '获取当前可领取的优惠券列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_coupon_dto_1.AvailableCouponQueryDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getAvailableToReceiveCoupons", null);
__decorate([
    (0, common_1.Post)('admin/test-concurrency'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '模拟高并发领券（仅用于测试）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '模拟高并发领券成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "testConcurrentReceive", null);
__decorate([
    (0, common_1.Post)('admin/distribute'),
    (0, swagger_1.ApiOperation)({ summary: '发放优惠券给指定用户', description: '管理员将优惠券发放给指定用户' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '发放成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [distribute_coupon_dto_1.DistributeCouponDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "distributeCoupon", null);
__decorate([
    (0, common_1.Get)('admin/users'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户列表', description: '获取用户列表供发放优惠券时选择' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [distribute_coupon_dto_1.QueryUserListDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getUserList", null);
__decorate([
    (0, common_1.Get)('admin/distribute-records/:couponId'),
    (0, swagger_1.ApiOperation)({ summary: '查看优惠券发放记录', description: '查看指定优惠券的发放记录' }),
    (0, swagger_1.ApiParam)({ name: 'couponId', description: '优惠券ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Param)('couponId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, distribute_coupon_dto_1.QueryUserListDto]),
    __metadata("design:returntype", Promise)
], CouponController.prototype, "getDistributeRecords", null);
exports.CouponController = CouponController = CouponController_1 = __decorate([
    (0, swagger_1.ApiTags)('营销模块-优惠券'),
    (0, common_1.Controller)('miniprogram/marketing/coupon'),
    __metadata("design:paramtypes", [coupon_service_1.CouponService])
], CouponController);
//# sourceMappingURL=coupon.controller.js.map