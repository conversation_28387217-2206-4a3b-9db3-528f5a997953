"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const tool_service_1 = require("./tool.service");
const create_genTable_dto_1 = require("./dto/create-genTable-dto");
const user_decorator_1 = require("../user/user.decorator");
let ToolController = class ToolController {
    constructor(toolService) {
        this.toolService = toolService;
    }
    findAll(query) {
        return this.toolService.findAll(query);
    }
    genDbList(query) {
        return this.toolService.genDbList(query);
    }
    genImportTable(table, user) {
        return this.toolService.importTable(table, user);
    }
    synchDb(tableName) {
        return this.toolService.synchDb(tableName);
    }
    gen(id) {
        return this.toolService.findOne(+id);
    }
    genUpdate(genTableUpdate) {
        return this.toolService.genUpdate(genTableUpdate);
    }
    remove(id) {
        return this.toolService.remove(+id);
    }
    batchGenCode(tables, res) {
        return this.toolService.batchGenCode(tables, res);
    }
    preview(id) {
        return this.toolService.preview(+id);
    }
};
exports.ToolController = ToolController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '数据表列表' }),
    (0, common_1.Get)('/gen/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_genTable_dto_1.GenTableList]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '查询数据库列表' }),
    (0, common_1.Get)('/gen/db/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_genTable_dto_1.GenDbTableList]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "genDbList", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导入表' }),
    (0, common_1.Post)('/gen/importTable'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_genTable_dto_1.TableName, Object]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "genImportTable", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '同步表' }),
    (0, common_1.Get)('/gen/synchDb/:tableName'),
    __param(0, (0, common_1.Param)('tableName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "synchDb", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '查询表详细信息' }),
    (0, common_1.Get)('/gen/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "gen", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '修改代码生成信息' }),
    (0, common_1.Put)('/gen'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_genTable_dto_1.GenTableUpdate]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "genUpdate", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除表数据' }),
    (0, common_1.Delete)('/gen/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '生成代码' }),
    (0, common_1.Get)('/gen/batchGenCode/zip'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_genTable_dto_1.TableName, Object]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "batchGenCode", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '查看代码' }),
    (0, common_1.Get)('/gen/preview/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ToolController.prototype, "preview", null);
exports.ToolController = ToolController = __decorate([
    (0, swagger_1.ApiTags)('系统工具'),
    (0, common_1.Controller)('tool'),
    __metadata("design:paramtypes", [tool_service_1.ToolService])
], ToolController);
//# sourceMappingURL=tool.controller.js.map