import { BannerService } from './banner.service';
import { CreateBannerDto } from './dto/create-banner.dto';
import { UpdateBannerDto } from './dto/update-banner.dto';
import { QueryBannerDto } from './dto/query-banner.dto';
import { ResultData } from '../../../common/utils/result';
export declare class BannerController {
    private readonly bannerService;
    constructor(bannerService: BannerService);
    create(createBannerDto: CreateBannerDto, user: any): Promise<ResultData>;
    findAll(queryParams: QueryBannerDto): Promise<ResultData>;
    findOne(id: string): Promise<ResultData>;
    update(id: string, updateBannerDto: UpdateBannerDto, user: any): Promise<ResultData>;
    remove(id: string, user: any): Promise<ResultData>;
    getMiniBanners(): Promise<ResultData>;
}
