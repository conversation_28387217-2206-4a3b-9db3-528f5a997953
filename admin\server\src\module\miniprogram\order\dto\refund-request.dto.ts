import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, Min, IsIn, IsInt } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * 创建退款申请DTO
 */
export class CreateRefundRequestDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  @IsNumber({}, { message: '用户ID必须为数字' })
  @Type(() => Number)
  userId: number;

  @ApiProperty({ description: '订单ID', example: 'ORD20240101001' })
  @IsString({ message: '订单ID必须为字符串' })
  orderId: string;

  @ApiProperty({ description: '退款原因', example: '商品质量问题' })
  @IsString({ message: '退款原因必须为字符串' })
  refundReason: string;

  @ApiProperty({ description: '申请退款金额', example: 99.99 })
  @IsNumber({}, { message: '退款金额必须为数字' })
  @Min(0.01, { message: '退款金额必须大于0.01' })
  @Transform(({ value }) => Number(value))
  refundAmount: number;
}

/**
 * 退款申请查询DTO（管理端）
 */
export class RefundRequestQueryDto {
  @ApiProperty({ description: '页码', example: 1 })
  @IsInt({ message: '页码必须为整数' })
  @Min(1, { message: '页码必须大于0' })
  @Type(() => Number)
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  @IsInt({ message: '每页数量必须为整数' })
  @Min(1, { message: '每页数量必须大于0' })
  @Type(() => Number)
  pageSize: number;

  @ApiPropertyOptional({ description: '申请状态', example: '1' })
  @IsOptional()
  @IsString({ message: '状态必须为字符串' })
  @IsIn(['1', '2', '3'], { message: '状态只能是：1待审核 2已退款 3已拒绝' })
  status?: string;

  @ApiPropertyOptional({ description: '订单ID', example: 'ORDER123' })
  @IsOptional()
  @IsString({ message: '订单ID必须为字符串' })
  orderId?: string;

  @ApiPropertyOptional({ description: '用户ID', example: 1 })
  @IsOptional()
  @IsInt({ message: '用户ID必须为整数' })
  @Type(() => Number)
  userId?: number;
}

/**
 * 处理退款申请DTO（管理端）
 */
export class ProcessRefundRequestDto {
  @ApiProperty({ description: '退款申请ID', example: 1 })
  @IsInt({ message: '申请ID必须为整数' })
  @Type(() => Number)
  requestId: number;

  @ApiProperty({ description: '处理动作', example: 'approve', enum: ['approve', 'reject'] })
  @IsString({ message: '处理动作必须为字符串' })
  @IsIn(['approve', 'reject'], { message: '处理动作只能是：approve或reject' })
  action: 'approve' | 'reject';

  @ApiPropertyOptional({ description: '管理员处理意见', example: '同意退款' })
  @IsOptional()
  @IsString({ message: '处理意见必须为字符串' })
  adminComment?: string;
}

/**
 * 退款申请列表响应DTO
 */
export class RefundRequestListResponseDto {
  @ApiProperty({ description: '申请ID' })
  id: number;

  @ApiProperty({ description: '订单ID' })
  orderId: string;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '用户昵称' })
  userNickname?: string;

  @ApiProperty({ description: '退款原因' })
  refundReason: string;

  @ApiProperty({ description: '申请退款金额' })
  refundAmount: number;

  @ApiProperty({ description: '申请状态', example: '1' })
  status: string;

  @ApiProperty({ description: '状态文本' })
  statusText: string;

  @ApiProperty({ description: '管理员处理意见', required: false })
  adminComment?: string;

  @ApiProperty({ description: '申请时间' })
  requestTime: Date;

  @ApiProperty({ description: '处理时间', required: false })
  processTime?: Date;

  @ApiProperty({ description: '订单总金额' })
  orderTotalAmount?: number;

  @ApiProperty({ description: '订单状态' })
  orderStatus?: string;
}

/**
 * 退款申请状态枚举
 */
export enum RefundRequestStatus {
  /** 待审核 */
  PENDING = '1',
  /** 已退款 */
  REFUNDED = '2',
  /** 已拒绝 */
  REJECTED = '3',
}

/**
 * 退款申请状态中文映射
 */
export const RefundRequestStatusText = {
  [RefundRequestStatus.PENDING]: '待审核',
  [RefundRequestStatus.REFUNDED]: '已退款',
  [RefundRequestStatus.REJECTED]: '已拒绝',
};

/**
 * 配送员确认取货DTO
 */
export class ConfirmRefundPickupDto {
  @ApiProperty({ description: '订单ID', example: 'ORD20240101001' })
  @IsString({ message: '订单ID必须为字符串' })
  orderId: string;

  @ApiPropertyOptional({ description: '配送员ID', example: 1 })
  @IsOptional()
  @IsNumber({}, { message: '配送员ID必须为数字' })
  @Type(() => Number)
  deliveryStaffId?: number;
}