"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtAuthGuard = void 0;
const core_1 = require("@nestjs/core");
const config_1 = require("@nestjs/config");
const passport_1 = require("@nestjs/passport");
const path_to_regexp_1 = require("path-to-regexp");
const common_1 = require("@nestjs/common");
const user_service_1 = require("../../module/system/user/user.service");
let JwtAuthGuard = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
    constructor(reflector, userService, config) {
        super();
        this.reflector = reflector;
        this.userService = userService;
        this.config = config;
        this.globalWhiteList = [];
        this.globalWhiteList = [].concat(this.config.get('perm.router.whitelist') || []);
    }
    async canActivate(ctx) {
        const notRequireAuth = this.reflector.getAllAndOverride('notRequireAuth', [ctx.getClass(), ctx.getHandler()]);
        if (notRequireAuth) {
            await this.jumpActivate(ctx);
            return true;
        }
        const isInWhiteList = this.checkWhiteList(ctx);
        if (isInWhiteList) {
            await this.jumpActivate(ctx);
            return true;
        }
        const req = ctx.switchToHttp().getRequest();
        const accessToken = req.get('Authorization');
        if (!accessToken)
            throw new common_1.ForbiddenException('请重新登录');
        const atUserId = await this.userService.parseToken(accessToken);
        if (!atUserId)
            throw new common_1.UnauthorizedException('当前登录已过期，请重新登录');
        return await this.activate(ctx);
    }
    async activate(ctx) {
        return super.canActivate(ctx);
    }
    async jumpActivate(ctx) {
        try {
            await this.activate(ctx);
        }
        catch (e) {
        }
        return true;
    }
    checkWhiteList(ctx) {
        const req = ctx.switchToHttp().getRequest();
        const i = this.globalWhiteList.findIndex((route) => {
            if (!route.method || req.method.toUpperCase() === route.method.toUpperCase()) {
                return !!(0, path_to_regexp_1.pathToRegexp)(route.path).exec(req.route.path);
            }
            return false;
        });
        return i > -1;
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)(user_service_1.UserService)),
    __metadata("design:paramtypes", [core_1.Reflector,
        user_service_1.UserService,
        config_1.ConfigService])
], JwtAuthGuard);
//# sourceMappingURL=auth.guard.js.map