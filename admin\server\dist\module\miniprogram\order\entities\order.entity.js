"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const order_item_entity_1 = require("./order-item.entity");
const address_entity_1 = require("../../address/entities/address.entity");
const user_entity_1 = require("../../user/entities/user.entity");
let OrderEntity = class OrderEntity extends base_1.BaseEntity {
};
exports.OrderEntity = OrderEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', name: 'order_id', length: 32, comment: '订单ID主键' }),
    __metadata("design:type", String)
], OrderEntity.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID外键' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], OrderEntity.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货地址ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'address_id', comment: '收货地址ID外键' }),
    __metadata("design:type", Number)
], OrderEntity.prototype, "addressId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户拼团ID', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'user_group_buy_id',
        length: 32,
        nullable: true,
        comment: '拼团组ID，用于标识具体的拼团组，所有参与同一拼团的订单共享相同的ID',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "userGroupBuyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为拼团发起人', required: false }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        name: 'is_group_initiator',
        default: 0,
        comment: '是否为拼团发起人：0否1是',
    }),
    __metadata("design:type", Number)
], OrderEntity.prototype, "isGroupInitiator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单类型', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'order_type',
        length: 1,
        default: '1',
        comment: '订单类型：1普通订单2团购订单',
    }),
    (0, typeorm_1.Index)('idx_order_type'),
    __metadata("design:type", String)
], OrderEntity.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单总金额', example: 99.99 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'total_amount',
        precision: 10,
        scale: 2,
        comment: '订单总金额',
    }),
    __metadata("design:type", Number)
], OrderEntity.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠金额', example: 10.0 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'discount_amount',
        precision: 10,
        scale: 2,
        default: 0,
        comment: '优惠金额',
    }),
    __metadata("design:type", Number)
], OrderEntity.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '实付金额', example: 89.99 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'final_amount',
        precision: 10,
        scale: 2,
        comment: '实付金额',
    }),
    __metadata("design:type", Number)
], OrderEntity.prototype, "finalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送方式', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'delivery_type',
        length: 1,
        default: '1',
        comment: '配送方式：1配送2自提',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "deliveryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预约配送时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'delivery_time',
        nullable: true,
        comment: '预约配送时间',
    }),
    __metadata("design:type", Date)
], OrderEntity.prototype, "deliveryTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'status',
        length: 1,
        default: '1',
        comment: '订单状态：1待支付 2待发货 3配送中 4已完成 5已取消(含所有退款情况) 6团购失败已退款 7退款中(已完成订单等配送员取货)',
    }),
    (0, typeorm_1.Index)('idx_status'),
    __metadata("design:type", String)
], OrderEntity.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价状态', example: '0' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'review_status',
        length: 1,
        default: '0',
        comment: '评价状态：0未评价1已评价',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "reviewStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'payment_time',
        nullable: true,
        comment: '支付时间',
    }),
    __metadata("design:type", Date)
], OrderEntity.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发货时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'shipment_time',
        nullable: true,
        comment: '发货时间',
    }),
    __metadata("design:type", Date)
], OrderEntity.prototype, "shipmentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '完成时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'completion_time',
        nullable: true,
        comment: '完成时间',
    }),
    __metadata("design:type", Date)
], OrderEntity.prototype, "completionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '取消原因', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'cancel_reason',
        length: 200,
        nullable: true,
        comment: '取消原因',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "cancelReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人姓名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'receiver_name', length: 50, comment: '收货人姓名' }),
    __metadata("design:type", String)
], OrderEntity.prototype, "receiverName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人电话' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'receiver_phone', length: 20, comment: '收货人电话' }),
    __metadata("design:type", String)
], OrderEntity.prototype, "receiverPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货地址' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'receiver_address', length: 500, comment: '收货地址' }),
    __metadata("design:type", String)
], OrderEntity.prototype, "receiverAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'refund_reason',
        length: 500,
        nullable: true,
        comment: '退款原因',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "refundReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款完成时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'refund_time',
        nullable: true,
        comment: '退款完成时间',
    }),
    __metadata("design:type", Date)
], OrderEntity.prototype, "refundTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款类型', required: false }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'refund_type',
        length: 1,
        nullable: true,
        comment: '退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动',
    }),
    __metadata("design:type", String)
], OrderEntity.prototype, "refundType", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id', referencedColumnName: 'userId' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], OrderEntity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => address_entity_1.UserAddress, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'address_id', referencedColumnName: 'addressId' }),
    __metadata("design:type", address_entity_1.UserAddress)
], OrderEntity.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => order_item_entity_1.OrderItemEntity, (orderItem) => orderItem.order, { cascade: true, createForeignKeyConstraints: false }),
    __metadata("design:type", Array)
], OrderEntity.prototype, "orderItems", void 0);
exports.OrderEntity = OrderEntity = __decorate([
    (0, typeorm_1.Entity)('orders', { comment: '订单信息表' }),
    (0, typeorm_1.Index)('idx_user_id', ['userId']),
    (0, typeorm_1.Index)('idx_status', ['status']),
    (0, typeorm_1.Index)('idx_create_time', ['createTime']),
    (0, typeorm_1.Index)('idx_order_type', ['orderType'])
], OrderEntity);
//# sourceMappingURL=order.entity.js.map