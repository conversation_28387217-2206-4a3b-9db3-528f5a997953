"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginlogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const export_1 = require("../../../common/utils/export");
const loginlog_entity_1 = require("./entities/loginlog.entity");
let LoginlogService = class LoginlogService {
    constructor(monitorLoginlogEntityRep) {
        this.monitorLoginlogEntityRep = monitorLoginlogEntityRep;
    }
    async create(createLoginlogDto) {
        return await this.monitorLoginlogEntityRep.save(createLoginlogDto);
    }
    async findAll(query) {
        const entity = this.monitorLoginlogEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.ipaddr) {
            entity.andWhere(`entity.ipaddr LIKE "%${query.ipaddr}%"`);
        }
        if (query.userName) {
            entity.andWhere(`entity.userName LIKE "%${query.userName}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.loginTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        if (query.orderByColumn && query.isAsc) {
            const key = query.isAsc === 'ascending' ? 'ASC' : 'DESC';
            entity.orderBy(`entity.${query.orderByColumn}`, key);
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async remove(ids) {
        const data = await this.monitorLoginlogEntityRep.update({ infoId: (0, typeorm_2.In)(ids) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async removeAll() {
        await this.monitorLoginlogEntityRep.update({ infoId: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok();
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAll(body);
        const options = {
            sheetName: '登录日志',
            data: list.data.list,
            header: [
                { title: '序号', dataIndex: 'infoId' },
                { title: '用户账号', dataIndex: 'userName' },
                { title: '登录状态', dataIndex: 'status' },
                { title: '登录地址', dataIndex: 'ipaddr' },
                { title: '登录地点', dataIndex: 'loginLocation' },
                { title: '浏览器', dataIndex: 'browser' },
                { title: '操作系统', dataIndex: 'os' },
                { title: '提示消息', dataIndex: 'msg' },
                { title: '访问时间', dataIndex: 'loginTime' },
            ],
            dictMap: {
                status: {
                    '0': '成功',
                    '1': '失败',
                },
            },
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.LoginlogService = LoginlogService;
exports.LoginlogService = LoginlogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(loginlog_entity_1.MonitorLoginlogEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LoginlogService);
//# sourceMappingURL=loginlog.service.js.map