"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheEvict = CacheEvict;
exports.Cacheable = Cacheable;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../module/common/redis/redis.service");
const decorator_1 = require("../utils/decorator");
function CacheEvict(CACHE_NAME, CACHE_KEY) {
    const injectRedis = (0, common_1.Inject)(redis_service_1.RedisService);
    return function (target, propertyKey, descriptor) {
        injectRedis(target, 'redis');
        const originMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const key = (0, decorator_1.paramsKeyFormat)(originMethod, CACHE_KEY, args);
            if (key === '*') {
                const res = await this.redis.keys(`${CACHE_NAME}*`);
                if (res.length) {
                    await this.redis.del(res);
                }
            }
            else if (key !== null) {
                await this.redis.del(`${CACHE_NAME}${key}`);
            }
            else {
                await this.redis.del(`${CACHE_NAME}${CACHE_KEY}`);
            }
            return await originMethod.apply(this, args);
        };
    };
}
function Cacheable(CACHE_NAME, CACHE_KEY, CACHE_EXPIRESIN) {
    const injectRedis = (0, common_1.Inject)(redis_service_1.RedisService);
    return function (target, propertyKey, descriptor) {
        injectRedis(target, 'redis');
        const originMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const key = (0, decorator_1.paramsKeyFormat)(originMethod, CACHE_KEY, args);
            if (key === null) {
                return await originMethod.apply(this, args);
            }
            const cacheResult = await this.redis.get(`${CACHE_NAME}${key}`);
            if (!cacheResult) {
                const result = await originMethod.apply(this, args);
                await this.redis.set(`${CACHE_NAME}${key}`, result, CACHE_EXPIRESIN);
                return result;
            }
            return cacheResult;
        };
    };
}
//# sourceMappingURL=redis.decorator.js.map