import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatisticsQueryDto } from './dto/order-statistics.dto';
import { ResultData } from '../../../common/utils/result';
import { CreateRefundRequestDto, ConfirmRefundPickupDto } from './dto/refund-request.dto';
export declare class OrderController {
    private readonly orderService;
    private readonly logger;
    constructor(orderService: OrderService);
    createOrder(userId: number, createOrderDto: CreateOrderDto): Promise<ResultData>;
    getUserOrderStats(userId: number): Promise<ResultData>;
    getUserOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    getOrderDetail(userId: number, orderId: string): Promise<ResultData>;
    cancelOrder(userId: number, orderId: string, body: {
        reason?: string;
    }): Promise<ResultData>;
    updateOrderStatus(updateOrderDto: UpdateOrderDto): Promise<ResultData>;
    findAll(queryDto: OrderQueryDto): Promise<ResultData>;
    findSelfPickupOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    getAdminOrderDetail(orderId: string): Promise<ResultData>;
    completeOrder(body: {
        orderId: string;
        userId: number;
    }): Promise<ResultData>;
    updateAdminOrderStatus(orderId: string, updateOrderDto: UpdateOrderDto): Promise<ResultData>;
    getOrderStats(): Promise<ResultData>;
    getDashboardStatistics(queryDto: OrderStatisticsQueryDto): Promise<ResultData>;
    debugTables(): Promise<ResultData>;
    deliverOrder(body: {
        orderId: string;
    }): Promise<ResultData>;
    refundOrder(body: {
        orderId: string;
        refundReason?: string;
    }): Promise<ResultData>;
    exportOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    batchCancelOrders(body: {
        orderIds: string[];
    }): Promise<ResultData>;
    getOrderDetailForAdmin(orderId: string): Promise<ResultData>;
    checkGroupBuyStatus(activityId: number): Promise<ResultData>;
    createRefundRequest(createRefundRequestDto: CreateRefundRequestDto): Promise<ResultData>;
    confirmRefundPickup(confirmRefundPickupDto: ConfirmRefundPickupDto): Promise<ResultData>;
}
