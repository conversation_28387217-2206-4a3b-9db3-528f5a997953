"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProductDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_product_dto_1 = require("./create-product.dto");
const class_validator_1 = require("class-validator");
const swagger_2 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const product_spec_dto_1 = require("./product-spec.dto");
class UpdateProductDto extends (0, swagger_1.PartialType)(create_product_dto_1.CreateProductDto) {
}
exports.UpdateProductDto = UpdateProductDto;
__decorate([
    (0, swagger_2.ApiProperty)({ description: '是否多规格商品：1是，0否', required: false, example: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '多规格标志必须是0或1' }),
    __metadata("design:type", Number)
], UpdateProductDto.prototype, "hasMultiSpecs", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({ description: '商品规格列表', required: false, type: [product_spec_dto_1.CreateProductSpecDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: '商品规格必须是数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => product_spec_dto_1.CreateProductSpecDto),
    __metadata("design:type", Array)
], UpdateProductDto.prototype, "specList", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({ description: '商品规格列表（兼容前端命名）', required: false, type: [product_spec_dto_1.CreateProductSpecDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: '商品规格必须是数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => product_spec_dto_1.CreateProductSpecDto),
    __metadata("design:type", Array)
], UpdateProductDto.prototype, "specs", void 0);
//# sourceMappingURL=update-product.dto.js.map