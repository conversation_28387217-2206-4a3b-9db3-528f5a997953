"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CartService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cart_entity_1 = require("./entities/cart.entity");
const result_1 = require("../../../common/utils/result");
const upload_entity_1 = require("../../upload/entities/upload.entity");
let CartService = CartService_1 = class CartService {
    constructor(cartRepository, uploadRepository, dataSource) {
        this.cartRepository = cartRepository;
        this.uploadRepository = uploadRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(CartService_1.name);
    }
    async processProductImages(images) {
        try {
            if (!images) {
                return [];
            }
            const urls = images.split(',').filter((url) => url.trim());
            if (urls.length === 0) {
                return [];
            }
            return urls;
        }
        catch (error) {
            this.logger.error(`处理商品图片失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async addToCart(userId, addToCartDto) {
        this.logger.log(`[DEBUG] 添加商品到购物车开始: UserId=${userId}, ProductId=${addToCartDto.productId}, Quantity=${addToCartDto.quantity}`);
        this.logger.log(`[DEBUG] 接收到的参数详情: ${JSON.stringify({ userId, addToCartDto })}`);
        try {
            if (!userId || !addToCartDto.productId || !addToCartDto.quantity) {
                this.logger.error(`[DEBUG] 参数验证失败: userId=${userId}, productId=${addToCartDto.productId}, quantity=${addToCartDto.quantity}`);
                throw new common_1.HttpException('参数不完整', common_1.HttpStatus.BAD_REQUEST);
            }
            if (addToCartDto.quantity <= 0) {
                this.logger.error(`[DEBUG] 数量参数错误: quantity=${addToCartDto.quantity}`);
                throw new common_1.HttpException('商品数量必须大于0', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`[DEBUG] 开始查询商品信息: productId=${addToCartDto.productId}`);
            const productQuery = 'SELECT product_id, name, price, status FROM products WHERE product_id = ?';
            this.logger.log(`[DEBUG] 执行商品查询SQL: ${productQuery}, 参数: [${addToCartDto.productId}]`);
            const product = await this.dataSource.query(productQuery, [addToCartDto.productId]);
            this.logger.log(`[DEBUG] 商品查询结果: ${JSON.stringify(product)}`);
            if (!product || product.length === 0) {
                this.logger.error(`[DEBUG] 商品不存在: productId=${addToCartDto.productId}`);
                throw new common_1.HttpException('商品不存在', common_1.HttpStatus.BAD_REQUEST);
            }
            const productInfo = product[0];
            this.logger.log(`[DEBUG] 商品信息: ${JSON.stringify(productInfo)}`);
            if (productInfo.status === 0) {
                this.logger.error(`[DEBUG] 商品已下架: productId=${addToCartDto.productId}, status=${productInfo.status}`);
                throw new common_1.HttpException('商品已下架', common_1.HttpStatus.BAD_REQUEST);
            }
            const specId = addToCartDto.specId || 0;
            if (specId > 0) {
                this.logger.log(`[DEBUG] 检查商品规格信息: specId=${specId}`);
                const specQuery = 'SELECT spec_id, status FROM product_specs WHERE spec_id = ? AND product_id = ?';
                const specs = await this.dataSource.query(specQuery, [specId, addToCartDto.productId]);
                if (!specs || specs.length === 0) {
                    this.logger.error(`[DEBUG] 商品规格不存在: specId=${specId}`);
                    throw new common_1.HttpException('商品规格不存在', common_1.HttpStatus.BAD_REQUEST);
                }
                const specInfo = specs[0];
                if (specInfo.status === 0) {
                    this.logger.error(`[DEBUG] 商品规格已禁用: specId=${specId}, status=${specInfo.status}`);
                    throw new common_1.HttpException('商品规格已禁用', common_1.HttpStatus.BAD_REQUEST);
                }
            }
            this.logger.log(`[DEBUG] 商品状态正常，无需库存检查`);
            this.logger.log(`[DEBUG] 检查是否已存在相同商品的购物车项（包括软删除的）`);
            this.logger.log(`[DEBUG] 查询购物车项: userId=${userId}, productId=${addToCartDto.productId}, specId=${specId}`);
            const existingCart = await this.cartRepository.findOne({
                where: {
                    userId,
                    productId: addToCartDto.productId,
                    specId: specId,
                },
            });
            this.logger.log(`[DEBUG] 查询购物车项结果: ${existingCart ? `找到购物车项ID=${existingCart.cartId}, delFlag=${existingCart.delFlag}` : '未找到购物车项'}`);
            if (existingCart) {
                if (existingCart.delFlag === '0') {
                    this.logger.log(`[DEBUG] 发现现有购物车项，累加数量: 原数量=${existingCart.quantity}, 新增数量=${addToCartDto.quantity}`);
                    existingCart.quantity += addToCartDto.quantity;
                    existingCart.updateTime = new Date();
                    existingCart.updateBy = userId.toString();
                    await this.cartRepository.save(existingCart);
                    this.logger.log(`[DEBUG] 购物车数量更新完成: 最终数量=${existingCart.quantity}`);
                }
                else {
                    this.logger.log(`[DEBUG] 发现已软删除的购物车项，恢复记录并设置新数量: ${addToCartDto.quantity}`);
                    existingCart.quantity = addToCartDto.quantity;
                    existingCart.delFlag = '0';
                    existingCart.updateTime = new Date();
                    existingCart.updateBy = userId.toString();
                    await this.cartRepository.save(existingCart);
                    this.logger.log(`[DEBUG] 软删除记录恢复完成: 数量=${existingCart.quantity}`);
                }
            }
            else {
                this.logger.log(`[DEBUG] 未发现现有购物车项，创建新记录`);
                const newCart = new cart_entity_1.CartEntity();
                newCart.userId = userId;
                newCart.productId = addToCartDto.productId;
                newCart.quantity = addToCartDto.quantity;
                newCart.specId = specId;
                newCart.createBy = userId.toString();
                newCart.updateBy = userId.toString();
                newCart.createTime = new Date();
                newCart.updateTime = new Date();
                newCart.delFlag = '0';
                newCart.status = '0';
                await this.cartRepository.save(newCart);
                this.logger.log(`[DEBUG] 新购物车记录创建完成: 数量=${newCart.quantity}, 规格ID=${newCart.specId}`);
            }
            const finalCart = await this.cartRepository.findOne({
                where: {
                    userId,
                    productId: addToCartDto.productId,
                    specId: specId,
                    delFlag: '0',
                },
                relations: ['product'],
            });
            this.logger.log(`[DEBUG] 购物车操作完成，最终购物车项: ${JSON.stringify(finalCart)}`);
            return result_1.ResultData.ok(finalCart, '商品已添加到购物车');
        }
        catch (error) {
            this.logger.error(`[ERROR] 添加商品到购物车失败: ${error.message}`);
            this.logger.error(`[ERROR] 错误堆栈: ${error.stack}`);
            this.logger.error(`[ERROR] 错误详情: ${JSON.stringify({
                name: error.name,
                message: error.message,
                code: error.code,
                errno: error.errno,
                sqlState: error.sqlState,
                sqlMessage: error.sqlMessage,
            })}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('添加商品到购物车失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCartItems(userId) {
        this.logger.log(`获取用户购物车列表: UserId=${userId}`);
        try {
            const carts = await this.cartRepository
                .createQueryBuilder('cart')
                .leftJoinAndSelect('cart.product', 'product')
                .where('cart.userId = :userId', { userId })
                .andWhere('cart.delFlag = :delFlag', { delFlag: '0' })
                .orderBy('cart.createTime', 'DESC')
                .getMany();
            const specIds = carts.filter((cart) => cart.specId > 0).map((cart) => cart.specId);
            let specsMap = {};
            if (specIds.length > 0) {
                const specs = await this.dataSource.query('SELECT spec_id, name, value, price, image FROM product_specs WHERE spec_id IN (?)', [specIds]);
                specsMap = specs.reduce((map, spec) => {
                    map[spec.spec_id] = spec;
                    return map;
                }, {});
                this.logger.log(`获取到规格信息: ${JSON.stringify(specsMap)}`);
            }
            const cartItemsPromises = carts.map(async (cart) => {
                let productImageUrls = [];
                if (cart.product?.images) {
                    productImageUrls = await this.processProductImages(cart.product.images);
                }
                let specImageUrl = '';
                const spec = cart.specId > 0 ? specsMap[cart.specId] : null;
                if (spec && spec.image) {
                    const specImageUrls = await this.processProductImages(spec.image);
                    specImageUrl = specImageUrls.length > 0 ? specImageUrls[0] : '';
                }
                const price = spec?.price || cart.product?.price || 0;
                return {
                    cartId: cart.cartId,
                    userId: cart.userId,
                    productId: cart.productId,
                    productName: cart.product?.name || '',
                    productPrice: price,
                    productImages: productImageUrls,
                    specId: cart.specId || 0,
                    specName: spec?.name || '',
                    specValue: spec?.value || '',
                    specImage: specImageUrl,
                    productStock: 999999,
                    quantity: cart.quantity,
                    subtotal: price * cart.quantity,
                    productStatus: cart.product?.status || '1',
                    createTime: cart.createTime,
                };
            });
            const cartItems = await Promise.all(cartItemsPromises);
            this.logger.log(`获取购物车成功: Count=${cartItems.length}`);
            return result_1.ResultData.ok(cartItems);
        }
        catch (error) {
            this.logger.error(`获取购物车列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取购物车列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateCartItem(userId, cartId, updateCartDto) {
        this.logger.log(`更新购物车商品数量: UserId=${userId}, CartId=${cartId}, Quantity=${updateCartDto.quantity}`);
        try {
            this.logger.log(`[DEBUG] 查询购物车项: cartId=${cartId}, userId=${userId}`);
            const cart = await this.cartRepository.findOne({
                where: { cartId, userId, delFlag: '0' },
            });
            this.logger.log(`[DEBUG] 查询购物车项结果: ${cart ? `找到购物车项，productId=${cart.productId}, specId=${cart.specId}` : '未找到购物车项'}`);
            if (!cart) {
                throw new common_1.HttpException('购物车项不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const product = await this.dataSource.query('SELECT status FROM products WHERE product_id = ?', [cart.productId]);
            if (!product || product.length === 0) {
                throw new common_1.HttpException('商品不存在', common_1.HttpStatus.BAD_REQUEST);
            }
            const productInfo = product[0];
            if (productInfo.status === 0) {
                throw new common_1.HttpException('商品已下架', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`[DEBUG] 商品状态正常，无需库存检查，直接更新数量`);
            cart.quantity = updateCartDto.quantity;
            cart.updateTime = new Date();
            cart.updateBy = userId.toString();
            const updatedCart = await this.cartRepository.save(cart);
            this.logger.log(`更新购物车商品数量成功: CartId=${cartId}`);
            return result_1.ResultData.ok(updatedCart, '购物车更新成功');
        }
        catch (error) {
            this.logger.error(`更新购物车失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('更新购物车失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeFromCart(userId, cartId) {
        this.logger.log(`删除购物车商品: UserId=${userId}, CartId=${cartId}`);
        try {
            const cart = await this.cartRepository.findOne({
                where: { cartId, userId, delFlag: '0' },
            });
            if (!cart) {
                throw new common_1.HttpException('购物车项不存在', common_1.HttpStatus.NOT_FOUND);
            }
            cart.delFlag = '1';
            cart.updateTime = new Date();
            cart.updateBy = userId.toString();
            await this.cartRepository.save(cart);
            this.logger.log(`删除购物车商品成功: CartId=${cartId}`);
            return result_1.ResultData.ok(null, '商品已从购物车移除');
        }
        catch (error) {
            this.logger.error(`删除购物车商品失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('删除购物车商品失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async clearCart(userId) {
        this.logger.log(`清空用户购物车: UserId=${userId}`);
        try {
            await this.cartRepository.update({ userId, delFlag: '0' }, {
                delFlag: '1',
                updateTime: new Date(),
                updateBy: userId.toString(),
            });
            this.logger.log(`清空购物车成功: UserId=${userId}`);
            return result_1.ResultData.ok(null, '购物车已清空');
        }
        catch (error) {
            this.logger.error(`清空购物车失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('清空购物车失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCartCount(userId) {
        this.logger.log(`获取购物车统计: UserId=${userId}`);
        try {
            const count = await this.cartRepository.count({
                where: { userId, delFlag: '0' },
            });
            const totalQuantity = await this.cartRepository
                .createQueryBuilder('cart')
                .select('SUM(cart.quantity)', 'total')
                .where('cart.userId = :userId', { userId })
                .andWhere('cart.delFlag = :delFlag', { delFlag: '0' })
                .getRawOne();
            const stats = {
                itemCount: count,
                totalQuantity: parseInt(totalQuantity?.total || '0'),
            };
            this.logger.log(`购物车统计: ItemCount=${stats.itemCount}, TotalQuantity=${stats.totalQuantity}`);
            return result_1.ResultData.ok(stats);
        }
        catch (error) {
            this.logger.error(`获取购物车统计失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取购物车统计失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(queryDto) {
        this.logger.log(`管理员查询购物车列表: ${JSON.stringify(queryDto)}`);
        try {
            const queryBuilder = this.cartRepository
                .createQueryBuilder('cart')
                .leftJoinAndSelect('cart.user', 'user')
                .leftJoinAndSelect('cart.product', 'product')
                .where('cart.delFlag = :delFlag', { delFlag: '0' });
            if (queryDto.userId) {
                queryBuilder.andWhere('cart.userId = :userId', { userId: queryDto.userId });
            }
            if (queryDto.productName) {
                queryBuilder.andWhere('product.name LIKE :productName', {
                    productName: `%${queryDto.productName}%`,
                });
            }
            const pageNum = queryDto.pageNum || 1;
            const pageSize = queryDto.pageSize || 10;
            queryBuilder
                .orderBy('cart.createTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [carts, total] = await queryBuilder.getManyAndCount();
            this.logger.log(`管理员查询购物车成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
            return result_1.ResultData.ok({
                list: carts,
                total,
                pageNum,
                pageSize,
            });
        }
        catch (error) {
            this.logger.error(`管理员查询购物车失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询购物车列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async batchRemove(cartIds) {
        this.logger.log(`批量删除购物车项: CartIds=${cartIds.join(',')}`);
        try {
            for (const cartId of cartIds) {
                await this.cartRepository.update({ cartId }, {
                    delFlag: '1',
                    updateTime: new Date(),
                });
            }
            this.logger.log(`批量删除购物车项成功: Count=${cartIds.length}`);
            return result_1.ResultData.ok(null, `成功删除 ${cartIds.length} 个购物车项`);
        }
        catch (error) {
            this.logger.error(`批量删除购物车项失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('批量删除失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.CartService = CartService;
exports.CartService = CartService = CartService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cart_entity_1.CartEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(upload_entity_1.SysUploadEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], CartService);
//# sourceMappingURL=cart.service.js.map