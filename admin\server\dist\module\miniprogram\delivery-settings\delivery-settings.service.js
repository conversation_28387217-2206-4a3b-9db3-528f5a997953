"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DeliverySettingsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliverySettingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const delivery_settings_entity_1 = require("./entities/delivery-settings.entity");
const delivery_time_slot_entity_1 = require("./entities/delivery-time-slot.entity");
const result_1 = require("../../../common/utils/result");
let DeliverySettingsService = DeliverySettingsService_1 = class DeliverySettingsService {
    constructor(deliverySettingsRepository, deliveryTimeSlotRepository) {
        this.deliverySettingsRepository = deliverySettingsRepository;
        this.deliveryTimeSlotRepository = deliveryTimeSlotRepository;
        this.logger = new common_1.Logger(DeliverySettingsService_1.name);
    }
    async create(createDeliverySettingsDto) {
        try {
            this.logger.log(`创建配送设置开始，请求数据: ${JSON.stringify(createDeliverySettingsDto)}`);
            const existingSettings = await this.deliverySettingsRepository.createQueryBuilder()
                .getOne();
            if (existingSettings) {
                this.logger.warn('配送设置已存在，请直接修改现有设置');
                return result_1.ResultData.fail(400, '配送设置已存在，请直接修改现有设置');
            }
            const deliverySettings = this.deliverySettingsRepository.create(createDeliverySettingsDto);
            const result = await this.deliverySettingsRepository.save(deliverySettings);
            this.logger.log(`配送设置创建成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '配送设置创建成功');
        }
        catch (error) {
            this.logger.error(`创建配送设置失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '创建配送设置失败');
        }
    }
    async findAll(queryDto) {
        try {
            this.logger.log(`获取配送设置列表开始，查询条件: ${JSON.stringify(queryDto)}`);
            const { pageNum = 1, pageSize = 10, storeName } = queryDto;
            const skip = (pageNum - 1) * pageSize;
            const whereConditions = {};
            if (storeName) {
                whereConditions.storeName = (0, typeorm_2.Like)(`%${storeName}%`);
            }
            const [list, total] = await this.deliverySettingsRepository.findAndCount({
                where: whereConditions,
                skip,
                take: pageSize,
                order: {
                    createTime: 'DESC',
                },
            });
            this.logger.log(`配送设置列表获取成功，共 ${total} 条`);
            return result_1.ResultData.ok({ rows: list, total }, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取配送设置列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取配送设置列表失败');
        }
    }
    async findOne(id) {
        try {
            this.logger.log(`获取配送设置详情开始，ID: ${id}`);
            const result = await this.deliverySettingsRepository.findOne({ where: { id } });
            if (!result) {
                this.logger.warn(`配送设置不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送设置不存在');
            }
            this.logger.log(`配送设置详情获取成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取配送设置详情失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取配送设置详情失败');
        }
    }
    async update(id, updateDeliverySettingsDto) {
        try {
            this.logger.log(`更新配送设置开始，ID: ${id}，更新数据: ${JSON.stringify(updateDeliverySettingsDto)}`);
            const existingResult = await this.deliverySettingsRepository.findOne({ where: { id } });
            if (!existingResult) {
                this.logger.warn(`配送设置不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送设置不存在');
            }
            if (updateDeliverySettingsDto.storeName && updateDeliverySettingsDto.storeName !== existingResult.storeName) {
                const existingStore = await this.deliverySettingsRepository.findOne({
                    where: { storeName: updateDeliverySettingsDto.storeName },
                });
                if (existingStore) {
                    this.logger.warn(`超市名称已存在: ${updateDeliverySettingsDto.storeName}`);
                    return result_1.ResultData.fail(400, '该超市名称已存在');
                }
            }
            await this.deliverySettingsRepository.update(id, updateDeliverySettingsDto);
            const result = await this.deliverySettingsRepository.findOne({ where: { id } });
            this.logger.log(`配送设置更新成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '更新成功');
        }
        catch (error) {
            this.logger.error(`更新配送设置失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '更新配送设置失败');
        }
    }
    async remove(id) {
        try {
            this.logger.log(`删除配送设置开始，ID: ${id}`);
            const existing = await this.deliverySettingsRepository.findOne({ where: { id } });
            if (!existing) {
                this.logger.warn(`配送设置不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送设置不存在');
            }
            await this.deliverySettingsRepository.delete(id);
            this.logger.log('配送设置删除成功');
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除配送设置失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '删除配送设置失败');
        }
    }
    async getUniqueSettings() {
        try {
            this.logger.log('获取唯一配送设置开始');
            const result = await this.deliverySettingsRepository.createQueryBuilder()
                .getOne();
            this.logger.log(`唯一配送设置获取成功: ${result ? '存在' : '不存在'}`);
            return result_1.ResultData.ok(result, result ? '获取成功' : '暂无配送设置');
        }
        catch (error) {
            this.logger.error(`获取唯一配送设置失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取唯一配送设置失败');
        }
    }
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371;
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    async checkDeliveryRange(latitude, longitude) {
        try {
            this.logger.log(`检查配送范围开始，地址坐标: ${latitude}, ${longitude}`);
            const settingsResult = await this.getUniqueSettings();
            const settings = settingsResult.data;
            if (!settings) {
                this.logger.warn('未找到配送设置，无法检查配送范围');
                return result_1.ResultData.ok({ inRange: false, message: '请先设置配送范围' }, '检查完成');
            }
            const distance = this.calculateDistance(latitude, longitude, Number(settings.latitude), Number(settings.longitude));
            this.logger.log(`距离 ${settings.storeName}: ${distance.toFixed(2)}km，配送半径: ${settings.deliveryRadius}km`);
            const inRange = distance <= Number(settings.deliveryRadius);
            const result = {
                inRange,
                distance: Number(distance.toFixed(2)),
                storeName: settings.storeName,
                maxRadius: Number(settings.deliveryRadius),
            };
            return result_1.ResultData.ok(result, '检查成功');
        }
        catch (error) {
            this.logger.error(`检查配送范围失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '检查配送范围失败');
        }
    }
    async createTimeSlot(createDto) {
        try {
            this.logger.log(`创建配送时间段开始，请求数据: ${JSON.stringify(createDto)}`);
            const existingSlot = await this.deliveryTimeSlotRepository.findOne({
                where: { label: createDto.label },
            });
            if (existingSlot) {
                this.logger.warn(`配送时间段标签已存在: ${createDto.label}`);
                return result_1.ResultData.fail(400, '该配送时间段标签已存在');
            }
            if (!createDto.sortOrder) {
                const maxSortOrder = await this.deliveryTimeSlotRepository
                    .createQueryBuilder('slot')
                    .select('MAX(slot.sortOrder)', 'max')
                    .getRawOne();
                createDto.sortOrder = (maxSortOrder.max || 0) + 1;
            }
            if (createDto.isActive === undefined) {
                createDto.isActive = 1;
            }
            const timeSlot = this.deliveryTimeSlotRepository.create(createDto);
            const result = await this.deliveryTimeSlotRepository.save(timeSlot);
            this.logger.log(`配送时间段创建成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '配送时间段创建成功');
        }
        catch (error) {
            this.logger.error(`创建配送时间段失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '创建配送时间段失败');
        }
    }
    async findAllTimeSlots(queryDto) {
        try {
            this.logger.log(`获取配送时间段列表开始，查询条件: ${JSON.stringify(queryDto)}`);
            const { pageNum = 1, pageSize = 10, label, isActive } = queryDto;
            const skip = (pageNum - 1) * pageSize;
            const whereConditions = {};
            if (label) {
                whereConditions.label = (0, typeorm_2.Like)(`%${label}%`);
            }
            if (isActive !== undefined) {
                whereConditions.isActive = isActive;
            }
            const [list, total] = await this.deliveryTimeSlotRepository.findAndCount({
                where: whereConditions,
                skip,
                take: pageSize,
                order: {
                    sortOrder: 'ASC',
                    createTime: 'DESC',
                },
            });
            this.logger.log(`配送时间段列表获取成功，共 ${total} 条`);
            return result_1.ResultData.ok({ rows: list, total }, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取配送时间段列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取配送时间段列表失败');
        }
    }
    async findOneTimeSlot(id) {
        try {
            this.logger.log(`获取配送时间段详情开始，ID: ${id}`);
            const result = await this.deliveryTimeSlotRepository.findOne({ where: { id } });
            if (!result) {
                this.logger.warn(`配送时间段不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送时间段不存在');
            }
            this.logger.log(`配送时间段详情获取成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取配送时间段详情失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取配送时间段详情失败');
        }
    }
    async updateTimeSlot(id, updateDto) {
        try {
            this.logger.log(`更新配送时间段开始，ID: ${id}，更新数据: ${JSON.stringify(updateDto)}`);
            const existingSlot = await this.deliveryTimeSlotRepository.findOne({ where: { id } });
            if (!existingSlot) {
                this.logger.warn(`配送时间段不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送时间段不存在');
            }
            if (updateDto.label && updateDto.label !== existingSlot.label) {
                const duplicateSlot = await this.deliveryTimeSlotRepository.findOne({
                    where: { label: updateDto.label },
                });
                if (duplicateSlot) {
                    this.logger.warn(`配送时间段标签已存在: ${updateDto.label}`);
                    return result_1.ResultData.fail(400, '该配送时间段标签已存在');
                }
            }
            await this.deliveryTimeSlotRepository.update(id, updateDto);
            const result = await this.deliveryTimeSlotRepository.findOne({ where: { id } });
            this.logger.log(`配送时间段更新成功: ${JSON.stringify(result)}`);
            return result_1.ResultData.ok(result, '更新成功');
        }
        catch (error) {
            this.logger.error(`更新配送时间段失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '更新配送时间段失败');
        }
    }
    async removeTimeSlot(id) {
        try {
            this.logger.log(`删除配送时间段开始，ID: ${id}`);
            const existing = await this.deliveryTimeSlotRepository.findOne({ where: { id } });
            if (!existing) {
                this.logger.warn(`配送时间段不存在，ID: ${id}`);
                return result_1.ResultData.fail(404, '配送时间段不存在');
            }
            await this.deliveryTimeSlotRepository.delete(id);
            this.logger.log('配送时间段删除成功');
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除配送时间段失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '删除配送时间段失败');
        }
    }
    async getActiveTimeSlots() {
        try {
            this.logger.log('获取可用配送时间段开始');
            const result = await this.deliveryTimeSlotRepository.find({
                where: { isActive: 1 },
                order: { sortOrder: 'ASC' },
            });
            const timeSlots = result.map(slot => ({
                label: slot.label,
                value: slot.value,
                disabled: false,
            }));
            this.logger.log(`可用配送时间段获取成功，共 ${timeSlots.length} 个`);
            return result_1.ResultData.ok(timeSlots, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取可用配送时间段失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, error.message || '获取可用配送时间段失败');
        }
    }
};
exports.DeliverySettingsService = DeliverySettingsService;
exports.DeliverySettingsService = DeliverySettingsService = DeliverySettingsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(delivery_settings_entity_1.DeliverySettings)),
    __param(1, (0, typeorm_1.InjectRepository)(delivery_time_slot_entity_1.DeliveryTimeSlot)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], DeliverySettingsService);
//# sourceMappingURL=delivery-settings.service.js.map