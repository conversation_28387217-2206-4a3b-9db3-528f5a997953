"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CouponScheduler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponScheduler = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const coupon_service_1 = require("./coupon.service");
let CouponScheduler = CouponScheduler_1 = class CouponScheduler {
    constructor(couponService) {
        this.couponService = couponService;
        this.logger = new common_1.Logger(CouponScheduler_1.name);
    }
    async handleExpiredCoupons() {
        this.logger.log('执行过期优惠券处理任务');
        const result = await this.couponService.processExpiredCoupons();
        this.logger.log(`过期优惠券处理完成，共处理 ${result.totalProcessed} 条记录，成功 ${result.successCount} 条`);
    }
    async checkCouponStatus() {
        this.logger.log('执行优惠券状态检查任务');
        const result = await this.couponService.updateCouponStatus();
        this.logger.log(`优惠券状态检查完成，共处理 ${result.totalProcessed} 条记录，更新 ${result.updatedCount} 条`);
    }
};
exports.CouponScheduler = CouponScheduler;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_2AM),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CouponScheduler.prototype, "handleExpiredCoupons", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CouponScheduler.prototype, "checkCouponStatus", null);
exports.CouponScheduler = CouponScheduler = CouponScheduler_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [coupon_service_1.CouponService])
], CouponScheduler);
//# sourceMappingURL=coupon.scheduler.js.map