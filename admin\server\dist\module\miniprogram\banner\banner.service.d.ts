import { Repository } from 'typeorm';
import { BannerEntity } from './entities/banner.entity';
import { CreateBannerDto } from './dto/create-banner.dto';
import { UpdateBannerDto } from './dto/update-banner.dto';
import { QueryBannerDto } from './dto/query-banner.dto';
import { UploadService } from '../../upload/upload.service';
export declare class BannerService {
    private readonly bannerRepository;
    private readonly uploadService;
    private readonly logger;
    constructor(bannerRepository: Repository<BannerEntity>, uploadService: UploadService);
    create(createBannerDto: CreateBannerDto, userId: string): Promise<BannerEntity>;
    findAll(queryParams: QueryBannerDto): Promise<{
        rows: BannerEntity[];
        total: number;
        pageNum: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(bannerId: number): Promise<BannerEntity>;
    update(bannerId: number, updateBannerDto: UpdateBannerDto, userId: string): Promise<BannerEntity>;
    remove(bannerId: number, userId: string): Promise<boolean>;
    getMiniBanners(): Promise<BannerEntity[]>;
}
