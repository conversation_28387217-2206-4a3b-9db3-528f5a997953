"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryUserListDto = exports.DistributeCouponDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class DistributeCouponDto {
    constructor() {
        this.quantity = 1;
    }
}
exports.DistributeCouponDto = DistributeCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '优惠券ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '优惠券ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DistributeCouponDto.prototype, "couponId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID列表', example: [1, 2, 3] }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID列表不能为空' }),
    (0, class_validator_1.IsArray)({ message: '用户ID列表必须是数组' }),
    (0, class_validator_1.IsInt)({ each: true, message: '用户ID必须是整数' }),
    __metadata("design:type", Array)
], DistributeCouponDto.prototype, "userIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每个用户发放数量', example: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '发放数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '发放数量不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DistributeCouponDto.prototype, "quantity", void 0);
class QueryUserListDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.QueryUserListDto = QueryUserListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], QueryUserListDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], QueryUserListDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryUserListDto.prototype, "nickName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryUserListDto.prototype, "phone", void 0);
//# sourceMappingURL=distribute-coupon.dto.js.map