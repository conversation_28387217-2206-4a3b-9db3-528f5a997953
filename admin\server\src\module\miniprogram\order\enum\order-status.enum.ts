/**
 * 订单状态枚举
 */
export enum OrderStatus {
  /** 待支付 */
  PENDING_PAYMENT = '1',
  /** 待发货 */
  PENDING_SHIPMENT = '2',
  /** 配送中 */
  SHIPPING = '3',
  /** 已完成 */
  COMPLETED = '4',
  /** 已取消(含待发货退款) */
  CANCELLED = '5',
  /** 团购失败已退款 */
  GROUP_BUY_FAILED = '6',
  /** 退款中(已完成订单等配送员取货) */
  REFUNDING = '7',
}

/**
 * 订单类型枚举
 */
export enum OrderType {
  /** 普通订单 */
  NORMAL = '1',
  /** 团购订单 */
  GROUP_BUY = '2',
}

/**
 * 配送方式枚举
 */
export enum DeliveryType {
  /** 配送 */
  DELIVERY = '1',
  /** 自提 */
  PICKUP = '2',
}

/**
 * 订单状态中文映射
 */
export const OrderStatusText = {
  [OrderStatus.PENDING_PAYMENT]: '待支付',
  [OrderStatus.PENDING_SHIPMENT]: '待发货',
  [OrderStatus.SHIPPING]: '配送中',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.GROUP_BUY_FAILED]: '团购失败已退款',
  [OrderStatus.REFUNDING]: '退款中',
};

/**
 * 订单类型中文映射
 */
export const OrderTypeText = {
  [OrderType.NORMAL]: '普通订单',
  [OrderType.GROUP_BUY]: '团购订单',
};

/**
 * 配送方式中文映射
 */
export const DeliveryTypeText = {
  [DeliveryType.DELIVERY]: '配送',
  [DeliveryType.PICKUP]: '自提',
};
