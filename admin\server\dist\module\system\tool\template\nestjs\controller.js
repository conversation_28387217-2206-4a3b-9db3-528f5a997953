"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.controllerTem = void 0;
const Lodash = __importStar(require("lodash"));
const controllerTem = (options) => {
    const { BusinessName, businessName, functionName, moduleName, primaryKey } = options;
    const serviceName = `${Lodash.upperFirst(BusinessName)}Service`;
    const serviceInstance = `${businessName}Service`;
    return `
import { Controller, Get, Post, Put, Body, Query, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { RequirePermission } from 'src/common/decorators/require-premission.decorator';
import { ${serviceName} } from './${businessName}.service';
import { Create${Lodash.upperFirst(BusinessName)}Dto, Base${Lodash.upperFirst(BusinessName)}Dto, Update${Lodash.upperFirst(BusinessName)}Dto, Query${Lodash.upperFirst(BusinessName)}Dto, List${Lodash.upperFirst(BusinessName)}Dto } from './dto/${businessName}.dto';
import { ApiDataResponse } from 'src/common/decorators/apiDataResponse.decorator';

@ApiTags('${functionName}')
@Controller('${moduleName}/${businessName}')
export class ${Lodash.upperFirst(BusinessName)}Controller {
constructor(private readonly ${serviceInstance}: ${serviceName}) {}
    @ApiOperation({
        summary: '${functionName}-创建',
    })
    @ApiDataResponse(Base${Lodash.upperFirst(BusinessName)}Dto)
    @RequirePermission('${moduleName}:${businessName}:add')
    @Post()
    create(@Body() body: Create${Lodash.upperFirst(BusinessName)}Dto) {
        return this.${serviceInstance}.create(body);
    }\n
    @ApiOperation({
        summary: '${functionName}-列表',
    })
    @ApiDataResponse(List${Lodash.upperFirst(BusinessName)}Dto, true, true)
    @RequirePermission('${moduleName}:${businessName}:list')
    @Get('list')
    findAll(@Query() query: Query${Lodash.upperFirst(BusinessName)}Dto) {
        return this.${serviceInstance}.findAll(query);
    }\n
    @ApiOperation({
        summary: '${functionName}-详情',
    })
    @ApiDataResponse(Base${Lodash.upperFirst(BusinessName)}Dto)
    @RequirePermission('${moduleName}:${businessName}:query')
    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.${serviceInstance}.findOne(+id);
    }\n
    @ApiOperation({
        summary: '${functionName}-修改',
    })
    @ApiDataResponse()
    @RequirePermission('${moduleName}:${businessName}:edit')
    @Put()
    update(@Body() body: Update${Lodash.upperFirst(BusinessName)}Dto) {
        return this.${serviceInstance}.update(body);
    }\n
    @ApiOperation({
        summary: '${functionName}-删除',
    })
    @ApiDataResponse()
    @RequirePermission('${moduleName}:${businessName}:remove')
    @Delete(':ids')
    remove(@Param('ids') ids: string) {
        const ${primaryKey}s = ids.split(',').map((id) => +id);
        return this.${serviceInstance}.remove(${primaryKey}s);
    }
}`;
};
exports.controllerTem = controllerTem;
//# sourceMappingURL=controller.js.map