"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemModule = void 0;
const common_1 = require("@nestjs/common");
const auth_module_1 = require("./auth/auth.module");
const dept_module_1 = require("./dept/dept.module");
const config_module_1 = require("./config/config.module");
const dict_module_1 = require("./dict/dict.module");
const menu_module_1 = require("./menu/menu.module");
const notice_module_1 = require("./notice/notice.module");
const post_module_1 = require("./post/post.module");
const role_module_1 = require("./role/role.module");
const tool_module_1 = require("./tool/tool.module");
const user_module_1 = require("./user/user.module");
let SystemModule = class SystemModule {
};
exports.SystemModule = SystemModule;
exports.SystemModule = SystemModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            config_module_1.SysConfigModule,
            dept_module_1.DeptModule,
            dict_module_1.DictModule,
            menu_module_1.MenuModule,
            notice_module_1.NoticeModule,
            post_module_1.PostModule,
            role_module_1.RoleModule,
            tool_module_1.ToolModule,
            user_module_1.UserModule,
        ],
    })
], SystemModule);
//# sourceMappingURL=system.module.js.map