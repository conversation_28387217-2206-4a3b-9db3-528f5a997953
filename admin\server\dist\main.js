"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const swagger_1 = require("@nestjs/swagger");
const request_ip_1 = require("request-ip");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const http_exceptions_filter_1 = require("./common/filters/http-exceptions-filter");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const path_1 = require("path");
const fs_1 = require("fs");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        cors: true,
    });
    const config = app.get(config_1.ConfigService);
    app.use((0, express_rate_limit_1.default)({
        windowMs: 15 * 60 * 1000,
        max: 1000,
    }));
    const prefix = config.get('app.prefix');
    const rootPath = process.cwd();
    const baseDirPath = (0, path_1.join)(rootPath, config.get('app.file.location'));
    app.useStaticAssets(baseDirPath, {
        prefix: '/profile/',
        maxAge: 86400000 * 365,
    });
    app.setGlobalPrefix(prefix);
    app.useGlobalPipes(new common_1.ValidationPipe({ transform: true, whitelist: true }));
    app.useGlobalFilters(new http_exceptions_filter_1.HttpExceptionsFilter());
    app.use((0, helmet_1.default)({ crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' }, crossOriginResourcePolicy: false }));
    const swaggerOptions = new swagger_1.DocumentBuilder()
        .setTitle('Nest-Admin')
        .setDescription('Nest-Admin 接口文档')
        .setVersion('2.0.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
    }, 'token')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, swaggerOptions);
    (0, fs_1.writeFileSync)((0, path_1.join)(process.cwd(), 'openApi.json'), JSON.stringify(document, null, 2));
    swagger_1.SwaggerModule.setup(`${prefix}/swagger-ui`, app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
        customSiteTitle: 'Nest-Admin API Docs',
    });
    app.use((0, request_ip_1.mw)({ attributeName: 'ip' }));
    const port = config.get('app.port') || 8080;
    await app.listen(port);
    console.log(`Nest-Admin 服务启动成功`, '\n', '服务地址', `http://localhost:${port}${prefix}/`, '\n', 'swagger 文档地址', `http://localhost:${port}${prefix}/swagger-ui/`);
}
bootstrap();
//# sourceMappingURL=main.js.map