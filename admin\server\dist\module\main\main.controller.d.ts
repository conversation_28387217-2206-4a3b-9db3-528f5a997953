import { MainService } from './main.service';
import { RegisterDto, LoginDto } from './dto/index';
import { ResultData } from 'src/common/utils/result';
import { RedisService } from 'src/module/common/redis/redis.service';
import { ConfigService } from 'src/module/system/config/config.service';
import { ClientInfoDto } from 'src/common/decorators/common.decorator';
import { UserDto } from 'src/module/system/user/user.decorator';
export declare class MainController {
    private readonly mainService;
    private readonly redisService;
    private readonly configService;
    constructor(mainService: MainService, redisService: RedisService, configService: ConfigService);
    login(user: LoginDto, clientInfo: ClientInfoDto): Promise<ResultData>;
    logout(user: UserDto, clientInfo: ClientInfoDto): Promise<ResultData>;
    register(user: RegisterDto): Promise<ResultData>;
    registerUser(): Promise<ResultData>;
    captchaImage(): Promise<ResultData>;
    getInfo(user: UserDto): Promise<{
        msg: string;
        code: number;
        permissions: string[];
        roles: string[];
        user: {
            dept: import("../system/dept/entities/dept.entity").SysDeptEntity;
            roles: Array<import("../system/role/entities/role.entity").SysRoleEntity>;
            posts: Array<import("../system/post/entities/post.entity").SysPostEntity>;
        } & import("../system/user/entities/sys-user.entity").UserEntity;
    }>;
    getRouters(user: UserDto): Promise<ResultData>;
}
