import { IconNavService } from './iconNav.service';
import { CreateIconNavDto } from './dto/create-iconNav.dto';
import { UpdateIconNavDto } from './dto/update-iconNav.dto';
import { QueryIconNavDto } from './dto/query-iconNav.dto';
import { ResultData } from '../../../common/utils/result';
export declare class IconNavController {
    private readonly iconNavService;
    constructor(iconNavService: IconNavService);
    create(createIconNavDto: CreateIconNavDto, user: any): Promise<ResultData>;
    findAll(queryParams: QueryIconNavDto): Promise<ResultData>;
    findOne(id: string): Promise<ResultData>;
    update(id: string, updateIconNavDto: UpdateIconNavDto, user: any): Promise<ResultData>;
    remove(id: string, user: any): Promise<ResultData>;
    getMiniIconNavs(): Promise<ResultData>;
}
