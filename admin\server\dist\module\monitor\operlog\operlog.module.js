"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperlogModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const operlog_service_1 = require("./operlog.service");
const operlog_controller_1 = require("./operlog.controller");
const operlog_entity_1 = require("./entities/operlog.entity");
let OperlogModule = class OperlogModule {
};
exports.OperlogModule = OperlogModule;
exports.OperlogModule = OperlogModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([operlog_entity_1.SysOperlogEntity])],
        controllers: [operlog_controller_1.OperlogController],
        providers: [operlog_service_1.OperlogService],
        exports: [operlog_service_1.OperlogService],
    })
], OperlogModule);
//# sourceMappingURL=operlog.module.js.map