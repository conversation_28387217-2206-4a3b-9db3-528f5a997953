"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CouponService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const coupon_entity_1 = require("./entities/coupon.entity");
const user_coupon_entity_1 = require("./entities/user-coupon.entity");
const coupon_interface_1 = require("./interfaces/coupon.interface");
const result_1 = require("../../../common/utils/result");
const user_entity_1 = require("../user/entities/user.entity");
let CouponService = CouponService_1 = class CouponService {
    constructor(couponRepository, userCouponRepository, userRepository, dataSource) {
        this.couponRepository = couponRepository;
        this.userCouponRepository = userCouponRepository;
        this.userRepository = userRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(CouponService_1.name);
    }
    async create(createCouponDto, adminUserId) {
        try {
            const coupon = new coupon_entity_1.Coupon();
            Object.assign(coupon, createCouponDto);
            coupon.createBy = adminUserId.toString();
            coupon.updateBy = adminUserId.toString();
            this.logger.log(`创建优惠券: ${coupon.name}, 发放方式: ${coupon.distributeType || '未设置'}`);
            if (coupon.type === coupon_interface_1.CouponType.DISCOUNT && !coupon.discountRate) {
                throw new common_1.HttpException('折扣券必须设置折扣率', common_1.HttpStatus.BAD_REQUEST);
            }
            if (coupon.type === coupon_interface_1.CouponType.AMOUNT && !coupon.discountAmount) {
                throw new common_1.HttpException('满减券必须设置优惠金额', common_1.HttpStatus.BAD_REQUEST);
            }
            const savedCoupon = await this.couponRepository.save(coupon);
            return savedCoupon;
        }
        catch (error) {
            this.logger.error(`创建优惠券失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('创建优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async update(updateCouponDto, adminUserId) {
        try {
            const { couponId, ...updateData } = updateCouponDto;
            const coupon = await this.couponRepository.findOne({
                where: { id: couponId },
            });
            if (!coupon) {
                throw new common_1.HttpException('优惠券不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (coupon.type === coupon_interface_1.CouponType.DISCOUNT && updateData.discountRate === null) {
                throw new common_1.HttpException('折扣券必须设置折扣率', common_1.HttpStatus.BAD_REQUEST);
            }
            if (coupon.type === coupon_interface_1.CouponType.AMOUNT && updateData.discountAmount === null) {
                throw new common_1.HttpException('满减券必须设置优惠金额', common_1.HttpStatus.BAD_REQUEST);
            }
            Object.assign(coupon, updateData);
            coupon.updateBy = adminUserId.toString();
            coupon.updateTime = new Date();
            const updatedCoupon = await this.couponRepository.save(coupon);
            return updatedCoupon;
        }
        catch (error) {
            this.logger.error(`更新优惠券失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('更新优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(queryParams) {
        try {
            const { pageNum, pageSize, name, type, status } = queryParams;
            const queryBuilder = this.couponRepository.createQueryBuilder('coupon');
            queryBuilder.where('coupon.del_flag = :delFlag', { delFlag: '0' });
            if (name) {
                queryBuilder.andWhere('coupon.name LIKE :name', { name: `%${name}%` });
            }
            if (type) {
                queryBuilder.andWhere('coupon.type = :type', { type });
            }
            if (status) {
                queryBuilder.andWhere('coupon.status = :status', { status });
            }
            if (queryParams.startTime && queryParams.endTime) {
                queryBuilder.andWhere('(coupon.start_time <= :endTime AND coupon.end_time >= :startTime)', { startTime: queryParams.startTime, endTime: queryParams.endTime });
            }
            if (queryParams.orderByColumn) {
                const order = queryParams.isAsc === 'ascending' ? 'ASC' : 'DESC';
                queryBuilder.orderBy(`coupon.${queryParams.orderByColumn}`, order);
            }
            else {
                queryBuilder.orderBy('coupon.create_time', 'DESC');
            }
            if (pageNum && pageSize) {
                queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
            }
            const [list, total] = await queryBuilder.getManyAndCount();
            this.logger.log(`获取优惠券列表成功: 共${total}条记录`);
            return result_1.ResultData.ok({
                list,
                total,
                pageNum,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
            });
        }
        catch (error) {
            this.logger.error(`获取优惠券列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取优惠券列表失败');
        }
    }
    async findOne(id) {
        try {
            const coupon = await this.couponRepository.findOne({
                where: { id, delFlag: '0' },
            });
            if (!coupon) {
                throw new common_1.HttpException('优惠券不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return coupon;
        }
        catch (error) {
            this.logger.error(`获取优惠券详情失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('获取优惠券详情失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id, adminUserId) {
        try {
            const coupon = await this.couponRepository.findOne({
                where: { id, delFlag: '0' },
            });
            if (!coupon) {
                throw new common_1.HttpException('优惠券不存在', common_1.HttpStatus.NOT_FOUND);
            }
            coupon.delFlag = '1';
            coupon.updateBy = adminUserId.toString();
            coupon.updateTime = new Date();
            await this.couponRepository.save(coupon);
            return true;
        }
        catch (error) {
            this.logger.error(`删除优惠券失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('删除优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findUserAvailableCoupons(userId, orderAmount) {
        try {
            const userCoupons = await this.userCouponRepository.find({
                where: {
                    userId,
                    status: coupon_interface_1.UserCouponStatus.UNUSED,
                    delFlag: '0',
                },
                relations: ['coupon'],
            });
            const now = new Date();
            return userCoupons.filter((userCoupon) => {
                const coupon = userCoupon.coupon;
                if (coupon.status !== coupon_interface_1.CouponStatus.ACTIVE) {
                    return false;
                }
                const startTime = new Date(coupon.startTime);
                const endTime = new Date(coupon.endTime);
                if (now < startTime || now > endTime) {
                    return false;
                }
                if (orderAmount < coupon.conditionAmount) {
                    return false;
                }
                return true;
            });
        }
        catch (error) {
            this.logger.error(`获取用户可用优惠券失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取用户可用优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async userReceiveCoupon(userId, couponId) {
        this.logger.log('用户领取优惠券：userId, couponId', userId, couponId);
        const lockKey = `coupon:receive:${userId}:${couponId}`;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const coupon = await queryRunner.manager
                .createQueryBuilder(coupon_entity_1.Coupon, 'coupon')
                .where('coupon.id = :couponId', { couponId })
                .andWhere('coupon.status = :status', { status: coupon_interface_1.CouponStatus.ACTIVE })
                .andWhere('coupon.del_flag = :delFlag', { delFlag: '0' })
                .setLock('pessimistic_write')
                .getOne();
            if (!coupon) {
                return result_1.ResultData.fail(400, '优惠券不存在或已失效');
            }
            const now = new Date();
            if (now < new Date(coupon.startTime) || now > new Date(coupon.endTime)) {
                return result_1.ResultData.fail(400, '优惠券不在有效期内');
            }
            if (coupon.usedCount >= coupon.totalCount) {
                return result_1.ResultData.fail(400, '优惠券已领完');
            }
            const userCouponCount = await queryRunner.manager.count(user_coupon_entity_1.UserCoupon, {
                where: {
                    userId,
                    couponId,
                    delFlag: '0',
                    status: coupon_interface_1.UserCouponStatus.UNUSED,
                },
            });
            if (userCouponCount >= coupon.perUserLimit) {
                return result_1.ResultData.fail(400, `每人最多可领取${coupon.perUserLimit}张`);
            }
            const userCoupon = new user_coupon_entity_1.UserCoupon();
            userCoupon.userId = userId;
            userCoupon.couponId = couponId;
            userCoupon.status = coupon_interface_1.UserCouponStatus.UNUSED;
            userCoupon.receiveTime = now;
            userCoupon.createBy = userId.toString();
            userCoupon.updateBy = userId.toString();
            coupon.usedCount += 1;
            coupon.updateTime = now;
            coupon.updateBy = userId.toString();
            await queryRunner.manager.save(coupon);
            const savedUserCoupon = await queryRunner.manager.save(userCoupon);
            await queryRunner.commitTransaction();
            return result_1.ResultData.ok(savedUserCoupon, '领取优惠券成功');
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`用户领取优惠券失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '领取优惠券失败');
        }
        finally {
            await queryRunner.release();
        }
    }
    async useCoupon(userCouponId, orderId, userId) {
        try {
            const userCoupon = await this.userCouponRepository.findOne({
                where: { id: userCouponId, userId, status: coupon_interface_1.UserCouponStatus.UNUSED, delFlag: '0' },
                relations: ['coupon'],
            });
            if (!userCoupon) {
                throw new common_1.HttpException('优惠券不存在或已使用', common_1.HttpStatus.BAD_REQUEST);
            }
            const now = new Date();
            const coupon = userCoupon.coupon;
            if (now < new Date(coupon.startTime) || now > new Date(coupon.endTime)) {
                throw new common_1.HttpException('优惠券已过期', common_1.HttpStatus.BAD_REQUEST);
            }
            userCoupon.status = coupon_interface_1.UserCouponStatus.USED;
            userCoupon.useTime = now;
            userCoupon.orderId = orderId;
            userCoupon.updateTime = now;
            userCoupon.updateBy = userId.toString();
            await this.userCouponRepository.save(userCoupon);
            return true;
        }
        catch (error) {
            this.logger.error(`使用优惠券失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('使用优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserCoupons(userId, status) {
        try {
            const queryBuilder = this.userCouponRepository
                .createQueryBuilder('userCoupon')
                .leftJoinAndSelect('userCoupon.coupon', 'coupon')
                .where('userCoupon.user_id = :userId', { userId })
                .andWhere('userCoupon.del_flag = :delFlag', { delFlag: '0' });
            if (status) {
                queryBuilder.andWhere('userCoupon.status = :status', { status });
            }
            queryBuilder.orderBy('userCoupon.receive_time', 'DESC');
            return await queryBuilder.getMany();
        }
        catch (error) {
            this.logger.error(`获取用户优惠券列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取用户优惠券列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processExpiredCoupons() {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const now = new Date();
            const expiredUserCoupons = await queryRunner.manager
                .createQueryBuilder(user_coupon_entity_1.UserCoupon, 'userCoupon')
                .innerJoin('userCoupon.coupon', 'coupon')
                .where('userCoupon.status = :status', { status: coupon_interface_1.UserCouponStatus.UNUSED })
                .andWhere('userCoupon.del_flag = :delFlag', { delFlag: '0' })
                .andWhere('coupon.end_time < :now', { now })
                .getMany();
            let successCount = 0;
            for (const userCoupon of expiredUserCoupons) {
                try {
                    userCoupon.status = coupon_interface_1.UserCouponStatus.EXPIRED;
                    userCoupon.updateTime = now;
                    userCoupon.updateBy = 'system';
                    await queryRunner.manager.save(userCoupon);
                    successCount++;
                }
                catch (error) {
                    this.logger.error(`更新过期优惠券失败 (ID: ${userCoupon.id}): ${error.message}`);
                }
            }
            await queryRunner.commitTransaction();
            return {
                totalProcessed: expiredUserCoupons.length,
                successCount,
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`处理过期优惠券失败: ${error.message}`, error.stack);
            return {
                totalProcessed: 0,
                successCount: 0,
            };
        }
        finally {
            await queryRunner.release();
        }
    }
    async updateCouponStatus() {
        try {
            const now = new Date();
            const expiredCoupons = await this.couponRepository
                .createQueryBuilder('coupon')
                .where('coupon.status = :status', { status: coupon_interface_1.CouponStatus.ACTIVE })
                .andWhere('coupon.del_flag = :delFlag', { delFlag: '0' })
                .andWhere('coupon.end_time < :now', { now })
                .getMany();
            let updatedCount = 0;
            for (const coupon of expiredCoupons) {
                try {
                    coupon.status = coupon_interface_1.CouponStatus.INACTIVE;
                    coupon.updateTime = now;
                    coupon.updateBy = 'system';
                    await this.couponRepository.save(coupon);
                    updatedCount++;
                }
                catch (error) {
                    this.logger.error(`更新优惠券状态失败 (ID: ${coupon.id}): ${error.message}`);
                }
            }
            return {
                totalProcessed: expiredCoupons.length,
                updatedCount,
            };
        }
        catch (error) {
            this.logger.error(`更新优惠券状态失败: ${error.message}`, error.stack);
            return {
                totalProcessed: 0,
                updatedCount: 0,
            };
        }
    }
    async findAvailableToReceive(userId) {
        try {
            const now = new Date();
            const queryBuilder = this.couponRepository
                .createQueryBuilder('coupon')
                .where('coupon.status = :status', { status: '1' })
                .andWhere('coupon.startTime <= :now', { now })
                .andWhere('coupon.endTime >= :now', { now })
                .andWhere('coupon.delFlag = :delFlag', { delFlag: '0' })
                .andWhere('(coupon.distributeType = :publicType OR coupon.distributeType IS NULL)', {
                publicType: coupon_interface_1.CouponDistributeType.PUBLIC
            });
            this.logger.log(`查询用户${userId}可领取优惠券，公开类型值: ${coupon_interface_1.CouponDistributeType.PUBLIC}`);
            queryBuilder.andWhere((qb) => {
                const subQuery = qb.subQuery().select('COUNT(uc.id)').from('t_user_coupon', 'uc').where('uc.coupon_id = coupon.id').andWhere('uc.del_flag = :delFlag', { delFlag: '0' }).getQuery();
                return `(coupon.totalCount = 0 OR coupon.totalCount > ${subQuery})`;
            });
            queryBuilder.andWhere((qb) => {
                const subQuery = qb
                    .subQuery()
                    .select('COUNT(uc.id)')
                    .from('t_user_coupon', 'uc')
                    .where('uc.coupon_id = coupon.id')
                    .andWhere('uc.user_id = :userId', { userId })
                    .andWhere('uc.del_flag = :delFlag', { delFlag: '0' })
                    .getQuery();
                return `(coupon.perUserLimit = 0 OR coupon.perUserLimit > ${subQuery})`;
            });
            const [coupons, total] = await queryBuilder.getManyAndCount();
            coupons.forEach(coupon => {
                this.logger.log(`优惠券: ${coupon.name}, ID: ${coupon.id}, 发放方式: ${coupon.distributeType || 'NULL'}`);
            });
            this.logger.log(`查询到${total}张可领取的优惠券`);
            return {
                list: coupons,
                total,
            };
        }
        catch (error) {
            this.logger.error(`查询用户可领取优惠券列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询用户可领取优惠券列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async distributeCouponToUsers(distributeCouponDto, adminUserId) {
        const { couponId, userIds, quantity = 1 } = distributeCouponDto;
        this.logger.log(`管理员${adminUserId}发放优惠券${couponId}给用户${userIds.join(',')}`);
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const coupon = await queryRunner.manager.findOne(coupon_entity_1.Coupon, {
                where: { id: couponId, delFlag: '0' },
            });
            if (!coupon) {
                return result_1.ResultData.fail(400, '优惠券不存在');
            }
            if (coupon.status !== coupon_interface_1.CouponStatus.ACTIVE) {
                return result_1.ResultData.fail(400, '优惠券已停用');
            }
            const now = new Date();
            if (now < new Date(coupon.startTime) || now > new Date(coupon.endTime)) {
                return result_1.ResultData.fail(400, '优惠券不在有效期内');
            }
            const users = await queryRunner.manager.find(user_entity_1.MiniprogramUser, {
                where: userIds.map(id => ({ userId: id, delFlag: '0' })),
            });
            if (users.length !== userIds.length) {
                const existUserIds = users.map(u => u.userId);
                const notFoundIds = userIds.filter(id => !existUserIds.includes(id));
                return result_1.ResultData.fail(400, `用户不存在: ${notFoundIds.join(',')}`);
            }
            const results = [];
            const errors = [];
            for (const userId of userIds) {
                try {
                    const userCouponCount = await queryRunner.manager.count(user_coupon_entity_1.UserCoupon, {
                        where: {
                            userId,
                            couponId,
                            delFlag: '0',
                        },
                    });
                    const totalCount = userCouponCount + quantity;
                    if (totalCount > coupon.perUserLimit) {
                        errors.push(`用户${userId}已超过领取限制`);
                        continue;
                    }
                    if (coupon.totalCount > 0 && coupon.usedCount + quantity > coupon.totalCount) {
                        errors.push(`优惠券库存不足`);
                        break;
                    }
                    for (let i = 0; i < quantity; i++) {
                        const userCoupon = new user_coupon_entity_1.UserCoupon();
                        userCoupon.userId = userId;
                        userCoupon.couponId = couponId;
                        userCoupon.status = coupon_interface_1.UserCouponStatus.UNUSED;
                        userCoupon.receiveTime = now;
                        userCoupon.createBy = adminUserId.toString();
                        userCoupon.updateBy = adminUserId.toString();
                        await queryRunner.manager.save(userCoupon);
                    }
                    coupon.usedCount += quantity;
                    results.push({ userId, quantity });
                }
                catch (error) {
                    this.logger.error(`发放优惠券给用户${userId}失败: ${error.message}`, error.stack);
                    errors.push(`用户${userId}发放失败: ${error.message}`);
                }
            }
            coupon.updateTime = now;
            coupon.updateBy = adminUserId.toString();
            await queryRunner.manager.save(coupon);
            await queryRunner.commitTransaction();
            const successCount = results.length;
            const totalRequested = userIds.length;
            return result_1.ResultData.ok({
                successCount,
                totalRequested,
                results,
                errors,
            }, `成功发放给${successCount}个用户`);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`批量发放优惠券失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '发放优惠券失败');
        }
        finally {
            await queryRunner.release();
        }
    }
    async getUserList(queryParams) {
        try {
            const { pageNum = 1, pageSize = 10, nickName, phone } = queryParams;
            const queryBuilder = this.userRepository.createQueryBuilder('user');
            queryBuilder.where('user.delFlag = :delFlag', { delFlag: '0' });
            if (nickName) {
                queryBuilder.andWhere('user.nickname LIKE :nickName', { nickName: `%${nickName}%` });
            }
            if (phone) {
                queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${phone}%` });
            }
            queryBuilder.orderBy('user.createTime', 'DESC');
            queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
            const [list, total] = await queryBuilder.getManyAndCount();
            const formattedList = list.map(user => ({
                userId: user.userId,
                nickname: user.nickname,
                phone: user.phone,
                avatar: user.avatar,
                level: user.level,
                points: user.points,
                totalAmount: user.totalAmount,
                createTime: user.createTime,
            }));
            this.logger.log(`获取用户列表成功: 共${total}条记录`);
            return result_1.ResultData.ok({
                list: formattedList,
                total,
                pageNum,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
            });
        }
        catch (error) {
            this.logger.error(`获取用户列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取用户列表失败');
        }
    }
    async getDistributeRecords(couponId, queryParams) {
        try {
            const { pageNum = 1, pageSize = 10, nickName, phone } = queryParams;
            let countSql = `
        SELECT COUNT(*) as total
        FROM t_user_coupon uc
        LEFT JOIN miniprogram_user u ON u.user_id = uc.user_id AND u.del_flag = '0'
        WHERE uc.coupon_id = ? AND uc.del_flag = '0'
      `;
            const countParams = [couponId];
            if (nickName) {
                countSql += ` AND u.nickname LIKE ?`;
                countParams.push(`%${nickName}%`);
            }
            if (phone) {
                countSql += ` AND u.phone LIKE ?`;
                countParams.push(`%${phone}%`);
            }
            const countResult = await this.userCouponRepository.query(countSql, countParams);
            const total = countResult[0]?.total || 0;
            let sql = `
        SELECT 
          uc.id as userCouponId,
          uc.user_id as userId,
          uc.status,
          uc.receive_time as receiveTime,
          uc.use_time as useTime,
          uc.create_by as distributeBy,
          c.distribute_type as couponDistributeType,
          u.nickname,
          u.phone,
          u.avatar
        FROM t_user_coupon uc
        LEFT JOIN miniprogram_user u ON u.user_id = uc.user_id AND u.del_flag = '0'
        LEFT JOIN t_coupon c ON c.id = uc.coupon_id AND c.del_flag = '0'
        WHERE uc.coupon_id = ? AND uc.del_flag = '0'
      `;
            const params = [couponId];
            if (nickName) {
                sql += ` AND u.nickname LIKE ?`;
                params.push(`%${nickName}%`);
            }
            if (phone) {
                sql += ` AND u.phone LIKE ?`;
                params.push(`%${phone}%`);
            }
            sql += ` ORDER BY uc.receive_time DESC`;
            sql += ` LIMIT ? OFFSET ?`;
            params.push(pageSize, (pageNum - 1) * pageSize);
            const rawResults = await this.userCouponRepository.query(sql, params);
            const formattedList = rawResults.map(record => ({
                userCouponId: record.userCouponId,
                userId: record.userId,
                nickname: record.nickname || '未知用户',
                phone: record.phone || '',
                avatar: record.avatar || '',
                status: record.status,
                statusText: this.getStatusText(record.status),
                receiveTime: record.receiveTime,
                useTime: record.useTime,
                distributeBy: record.distributeBy,
                couponDistributeType: record.couponDistributeType,
                acquireType: record.couponDistributeType === '1' ? 'user' : 'admin'
            }));
            this.logger.log(`获取优惠券${couponId}发放记录成功: 共${total}条记录`);
            return result_1.ResultData.ok({
                list: formattedList,
                total,
                pageNum,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
            });
        }
        catch (error) {
            this.logger.error(`获取发放记录失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取发放记录失败');
        }
    }
    getStatusText(status) {
        switch (status) {
            case coupon_interface_1.UserCouponStatus.UNUSED:
                return '未使用';
            case coupon_interface_1.UserCouponStatus.USED:
                return '已使用';
            case coupon_interface_1.UserCouponStatus.EXPIRED:
                return '已过期';
            default:
                return '未知';
        }
    }
};
exports.CouponService = CouponService;
exports.CouponService = CouponService = CouponService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(coupon_entity_1.Coupon)),
    __param(1, (0, typeorm_1.InjectRepository)(user_coupon_entity_1.UserCoupon)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.MiniprogramUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], CouponService);
//# sourceMappingURL=coupon.service.js.map