"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundRequestDto = exports.PaymentNotifyDto = exports.ResourceDto = exports.UserBalanceRechargeDto = exports.BalanceRechargeDto = exports.PaymentRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class PaymentRequestDto {
}
exports.PaymentRequestDto = PaymentRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORDER_20241215_001' }),
    (0, class_validator_1.IsString)({ message: '订单ID必须为字符串' }),
    __metadata("design:type", String)
], PaymentRequestDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '支付方式',
        example: '1',
        enum: ['1', '2'],
        enumName: 'PaymentMethod',
    }),
    (0, class_validator_1.IsString)({ message: '支付方式必须为字符串' }),
    (0, class_validator_1.IsIn)(['1', '2'], { message: '支付方式只能是1(微信支付)、2(余额支付)' }),
    __metadata("design:type", String)
], PaymentRequestDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付描述', required: false, example: '初鲜果味商品订单支付' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付描述必须为字符串' }),
    __metadata("design:type", String)
], PaymentRequestDto.prototype, "description", void 0);
class BalanceRechargeDto {
}
exports.BalanceRechargeDto = BalanceRechargeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 100 }),
    (0, class_validator_1.IsNumber)({}, { message: '充值金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '充值金额必须大于0.01' }),
    __metadata("design:type", Number)
], BalanceRechargeDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付描述', required: false, example: '余额充值' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付描述必须为字符串' }),
    __metadata("design:type", String)
], BalanceRechargeDto.prototype, "description", void 0);
class UserBalanceRechargeDto {
}
exports.UserBalanceRechargeDto = UserBalanceRechargeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须为数字' }),
    __metadata("design:type", Number)
], UserBalanceRechargeDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 100 }),
    (0, class_validator_1.IsNumber)({}, { message: '充值金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '充值金额必须大于0.01' }),
    __metadata("design:type", Number)
], UserBalanceRechargeDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注', required: false, example: '后台管理员充值' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须为字符串' }),
    __metadata("design:type", String)
], UserBalanceRechargeDto.prototype, "remark", void 0);
class ResourceDto {
}
exports.ResourceDto = ResourceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '加密算法',
        example: 'AEAD_AES_256_GCM',
        required: true,
    }),
    (0, class_validator_1.IsString)({ message: '加密算法必须为字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '加密算法不能为空' }),
    __metadata("design:type", String)
], ResourceDto.prototype, "algorithm", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '附加数据',
        example: '',
        required: false,
    }),
    (0, class_validator_1.IsString)({ message: '附加数据必须为字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ResourceDto.prototype, "associated_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '数据密文',
        example: '',
        required: true,
    }),
    (0, class_validator_1.IsString)({ message: '数据密文必须为字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '数据密文不能为空' }),
    __metadata("design:type", String)
], ResourceDto.prototype, "ciphertext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '随机串',
        example: '',
        required: true,
    }),
    (0, class_validator_1.IsString)({ message: '随机串必须为字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '随机串不能为空' }),
    __metadata("design:type", String)
], ResourceDto.prototype, "nonce", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '原始类型',
        example: 'transaction',
        required: true,
    }),
    (0, class_validator_1.IsString)({ message: '原始类型必须为字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '原始类型不能为空' }),
    __metadata("design:type", String)
], ResourceDto.prototype, "original_type", void 0);
class PaymentNotifyDto {
}
exports.PaymentNotifyDto = PaymentNotifyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '通知ID',
        example: '55d390be-f1cf-51e2-82eb-08f1ddf442fd',
    }),
    (0, class_validator_1.IsString)({ message: '通知ID必须为字符串' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '通知创建时间',
        example: '2025-07-12T18:07:12+08:00',
    }),
    (0, class_validator_1.IsString)({ message: '通知创建时间必须为字符串' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "create_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '通知类型',
        example: 'encrypt-resource',
    }),
    (0, class_validator_1.IsString)({ message: '通知类型必须为字符串' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "resource_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '回调事件类型',
        example: 'TRANSACTION.SUCCESS',
    }),
    (0, class_validator_1.IsString)({ message: '回调事件类型必须为字符串' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '回调摘要',
        example: '支付成功',
    }),
    (0, class_validator_1.IsString)({ message: '回调摘要必须为字符串' }),
    __metadata("design:type", String)
], PaymentNotifyDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '通知数据',
        type: () => ResourceDto,
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ResourceDto),
    __metadata("design:type", ResourceDto)
], PaymentNotifyDto.prototype, "resource", void 0);
class RefundRequestDto {
}
exports.RefundRequestDto = RefundRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '支付记录ID必须为数字' }),
    __metadata("design:type", Number)
], RefundRequestDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额', example: 50.0 }),
    (0, class_validator_1.IsNumber)({}, { message: '退款金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '退款金额必须大于0.01' }),
    __metadata("design:type", Number)
], RefundRequestDto.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因', example: '用户主动退款' }),
    (0, class_validator_1.IsString)({ message: '退款原因必须为字符串' }),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "refundReason", void 0);
//# sourceMappingURL=payment-request.dto.js.map