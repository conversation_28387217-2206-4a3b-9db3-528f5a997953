import { Response } from 'express';
import { JobService } from './job.service';
import { CreateJobDto, ListJobDto } from './dto/create-job.dto';
export declare class JobController {
    private readonly jobService;
    constructor(jobService: JobService);
    list(query: {
        pageNum?: number;
        pageSize?: number;
        jobName?: string;
        jobGroup?: string;
        status?: string;
    }): Promise<import("../../../common/utils/result").ResultData>;
    getInfo(jobId: number): Promise<import("../../../common/utils/result").ResultData>;
    add(createJobDto: CreateJobDto, req: any): Promise<import("../../../common/utils/result").ResultData>;
    changeStatus(jobId: number, status: string, req: any): Promise<import("../../../common/utils/result").ResultData>;
    update(jobId: number, updateJobDto: Partial<CreateJobDto>, req: any): Promise<import("../../../common/utils/result").ResultData>;
    remove(jobIds: string): Promise<import("../../../common/utils/result").ResultData>;
    run(jobId: number): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListJobDto): Promise<void>;
}
