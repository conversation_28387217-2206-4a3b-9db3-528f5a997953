"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenTableColumnEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let GenTableColumnEntity = class GenTableColumnEntity extends base_1.BaseEntity {
};
exports.GenTableColumnEntity = GenTableColumnEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '编号' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'column_id', comment: '编号' }),
    __metadata("design:type", Number)
], GenTableColumnEntity.prototype, "columnId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '归属表编号' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'table_id', comment: '归属表编号' }),
    __metadata("design:type", Number)
], GenTableColumnEntity.prototype, "tableId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'column_name', length: 200, comment: '列名称' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "columnName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列描述' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'column_comment', length: 500, comment: '列描述' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "columnComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列类型' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'column_type', length: 100, comment: '列类型' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "columnType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: 'JAVA类型' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'java_type', length: 500, comment: 'JAVA类型' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "javaType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: 'JAVA字段名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'java_field', length: 200, comment: 'JAVA字段名' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "javaField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否主键（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_pk', default: '0', length: 1, comment: '是否主键（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isPk", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否自增（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_increment', default: '0', length: 1, comment: '是否自增（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isIncrement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否必填（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_required', default: '0', length: 1, comment: '是否必填（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否为插入字段（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_insert', default: '0', length: 1, comment: '是否为插入字段（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isInsert", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否编辑字段（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_edit', nullable: true, default: '0', length: 1, comment: '是否编辑字段（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isEdit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否列表字段（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_list', nullable: true, default: '0', length: 1, comment: '是否列表字段（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isList", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否查询字段（1是）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_query', nullable: true, default: '1', length: 1, comment: '是否查询字段（1是）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "isQuery", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '查询方式（等于、不等于、大于、小于、范围）' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'query_type', length: 200, default: 'EQ', comment: '查询方式（等于、不等于、大于、小于、范围）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "queryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'html_type', length: 200, default: '', comment: '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "htmlType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '字典类型' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_type', length: 200, default: '', comment: '字典类型' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "dictType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'column_default', length: 200, default: null, comment: '默认值' }),
    __metadata("design:type", String)
], GenTableColumnEntity.prototype, "columnDefault", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '排序' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'sort', comment: '排序' }),
    __metadata("design:type", Number)
], GenTableColumnEntity.prototype, "sort", void 0);
exports.GenTableColumnEntity = GenTableColumnEntity = __decorate([
    (0, typeorm_1.Entity)('gen_table_column', {
        comment: '代码生成业务表字段',
    })
], GenTableColumnEntity);
//# sourceMappingURL=gen-table-cloumn.entity.js.map