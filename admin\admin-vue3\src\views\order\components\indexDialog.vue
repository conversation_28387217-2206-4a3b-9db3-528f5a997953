
<template>
  <div class="order-detail">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-tag :type="getStatusTagType(orderInfo.status)">
            {{ getStatusLabel(orderInfo.status) }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ orderInfo.orderId }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusTagType(orderInfo.status)">
            {{ getStatusLabel(orderInfo.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ parseTime(orderInfo.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ orderInfo.paymentTime ? parseTime(orderInfo.paymentTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="发货时间">{{ orderInfo.shipmentTime ? parseTime(orderInfo.shipmentTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ orderInfo.completionTime ? parseTime(orderInfo.completionTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="订单类型">
          {{ orderInfo.orderType === '1' ? '普通订单' : '团购订单' }}
        </el-descriptions-item>
        <el-descriptions-item label="配送方式">
          {{ orderInfo.deliveryType === '1' ? '配送' : '自提' }}
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <span v-if="orderInfo.paymentMethod">
            {{ getPaymentMethodText(orderInfo.paymentMethod) }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="配送时间">{{ orderInfo.deliveryTime ? parseTime(orderInfo.deliveryTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="用户信息">
          <div v-if="orderInfo.user">
            <div>{{ orderInfo.user.nickname }}</div>
            <div style="font-size: 12px; color: #999;">{{ orderInfo.user.phone }}</div>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 收货地址信息 -->
      <el-divider content-position="left">收货地址</el-divider>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="收货人">{{ orderInfo.receiverName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ orderInfo.receiverPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">
          {{ orderInfo.receiverAddress || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 商品信息 -->
      <el-divider content-position="left">商品信息</el-divider>
      <el-table :data="orderInfo.items || orderInfo.orderItems || []" border style="width: 100%">
        <el-table-column prop="productName" label="商品名称" />
        <el-table-column label="规格" width="120">
          <template #default="scope">
            <span v-if="scope.row.specName && scope.row.specValue">
              {{ scope.row.specName }}: {{ scope.row.specValue }}
            </span>
            <span v-else>默认规格</span>
          </template>
        </el-table-column>
        <el-table-column prop="productImage" label="商品图片" width="100">
          <template #default="scope">
            <el-image 
              v-if="scope.row.productImage"
              :src="scope.row.productImage" 
              style="width: 60px; height: 60px" 
              fit="cover"
              preview-teleported
              :preview-src-list="[scope.row.productImage]"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价" width="100">
          <template #default="scope">
            ¥{{ (Number(scope.row.price) || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="totalPrice" label="小计" width="120">
          <template #default="scope">
            ¥{{ (Number(scope.row.totalPrice) || 0).toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 价格信息 -->
      <el-divider content-position="left">价格信息</el-divider>
      <el-descriptions :column="1" border class="price-info">
        <el-descriptions-item label="商品总额">¥{{ (Number(orderInfo.totalAmount) || 0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="优惠金额">-¥{{ (Number(orderInfo.discountAmount) || 0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="实付金额">
          <span style="color: #f56c6c; font-weight: bold; font-size: 16px;">
            ¥{{ (Number(orderInfo.finalAmount) || 0).toFixed(2) }}
          </span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作记录 -->
      <el-divider content-position="left">操作记录</el-divider>
      <el-timeline>
        <el-timeline-item
          v-if="orderInfo.createTime"
          :timestamp="parseTime(orderInfo.createTime)"
          placement="top"
        >
          订单创建
        </el-timeline-item>
        <el-timeline-item
          v-if="orderInfo.paymentTime"
          :timestamp="parseTime(orderInfo.paymentTime)"
          placement="top"
        >
          订单支付完成
        </el-timeline-item>
        <el-timeline-item
          v-if="orderInfo.shipmentTime"
          :timestamp="parseTime(orderInfo.shipmentTime)"
          placement="top"
        >
          订单发货
        </el-timeline-item>
        <el-timeline-item
          v-if="orderInfo.completionTime"
          :timestamp="parseTime(orderInfo.completionTime)"
          placement="top"
        >
          订单完成
        </el-timeline-item>
        <el-timeline-item
          v-if="orderInfo.cancelReason"
          :timestamp="parseTime(orderInfo.updateTime)"
          placement="top"
        >
          订单取消：{{ orderInfo.cancelReason }}
        </el-timeline-item>
      </el-timeline>

      <!-- 操作按钮 -->
      <div class="order-actions" style="margin-top: 20px; text-align: center;">
        <el-button 
          v-if="orderInfo.status === '2'" 
          type="primary" 
          @click="handleDeliver"
        >
          确认发货
        </el-button>
        <el-button 
          v-if="orderInfo.status === '2' || orderInfo.status === '3'" 
          type="danger" 
          @click="handleRefund"
        >
          订单退款
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrder, deliverOrder, refundOrder } from '@/api/order'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['refresh'])

const loading = ref(false)
const orderInfo = ref({})

// 获取订单详情
const getOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    const { data } = await getOrder(props.orderId)
    orderInfo.value = data
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取状态标签类型（根据后端Entity状态值）
const getStatusTagType = (status) => {
  const statusMap = {
    '1': 'warning',   // 待支付
    '2': 'success',   // 待发货  
    '3': 'primary',   // 配送中
    '4': 'success',   // 已完成
    '5': 'info',      // 已取消
    '6': 'danger',    // 团购失败已退款
    '7': 'warning'    // 退款中
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本（根据后端Entity状态值）
const getStatusLabel = (status) => {
  const statusMap = {
    '1': '待支付',
    '2': '待发货',
    '3': '配送中', 
    '4': '已完成',
    '5': '已取消',
    '6': '团购失败已退款',
    '7': '退款中'
  }
  return statusMap[status] || status
}

// 获取支付方式文本
const getPaymentMethodText = (paymentMethod) => {
  const paymentMethodMap = {
    '1': '微信支付',
    '2': '余额支付',
    '3': '模拟支付'
  }
  return paymentMethodMap[paymentMethod] || paymentMethod
}

// 确认发货
const handleDeliver = async () => {
  try {
    await ElMessageBox.confirm('确认要发货该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deliverOrder({ orderId: props.orderId })
    ElMessage.success('发货成功')
    emit('refresh')
    getOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发货失败:', error)
      ElMessage.error('发货失败')
    }
  }
}

// 订单退款
const handleRefund = async () => {
  try {
    await ElMessageBox.confirm('确认要对该订单进行退款吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await refundOrder({ orderId: props.orderId })
    ElMessage.success('退款成功')
    emit('refresh')
    getOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退款失败:', error)
      ElMessage.error('退款失败')
    }
  }
}

onMounted(() => {
  getOrderDetail()
})
</script>

<style scoped>
.order-detail {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  max-width: 300px;
  margin-left: auto;
}

.order-actions {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}
</style>