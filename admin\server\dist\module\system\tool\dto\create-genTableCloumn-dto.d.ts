export declare class CreateGenTableCloumnDto {
    tableId: number;
    createBy: string;
    columnType: string;
    columnComment: string;
    columnName: string;
    javaField: string;
    javaType: string;
    queryType: string;
    isInsert: string;
    htmlType: string;
    isEdit: string;
    isList: string;
    isQuery: string;
    isPk: string;
    isIncrement: string;
    isRequired: string;
}
export declare class genTableCloumnUpdate {
    columnId: number;
    columnComment?: string;
    javaType?: string;
    javaField?: string;
    isInsert?: string;
    isEdit?: string;
    isList?: string;
    isQuery?: string;
    queryType?: string;
    isRequired?: string;
    htmlType?: string;
    dictType?: string;
}
