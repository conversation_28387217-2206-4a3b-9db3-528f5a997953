"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfirmRefundPickupDto = exports.RefundRequestStatusText = exports.RefundRequestStatus = exports.RefundRequestListResponseDto = exports.ProcessRefundRequestDto = exports.RefundRequestQueryDto = exports.CreateRefundRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateRefundRequestDto {
}
exports.CreateRefundRequestDto = CreateRefundRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须为数字' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateRefundRequestDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORD20240101001' }),
    (0, class_validator_1.IsString)({ message: '订单ID必须为字符串' }),
    __metadata("design:type", String)
], CreateRefundRequestDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因', example: '商品质量问题' }),
    (0, class_validator_1.IsString)({ message: '退款原因必须为字符串' }),
    __metadata("design:type", String)
], CreateRefundRequestDto.prototype, "refundReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请退款金额', example: 99.99 }),
    (0, class_validator_1.IsNumber)({}, { message: '退款金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '退款金额必须大于0.01' }),
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    __metadata("design:type", Number)
], CreateRefundRequestDto.prototype, "refundAmount", void 0);
class RefundRequestQueryDto {
}
exports.RefundRequestQueryDto = RefundRequestQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '页码必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], RefundRequestQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    (0, class_validator_1.IsInt)({ message: '每页数量必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], RefundRequestQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '申请状态', example: '1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '状态必须为字符串' }),
    (0, class_validator_1.IsIn)(['1', '2', '3'], { message: '状态只能是：1待审核 2已退款 3已拒绝' }),
    __metadata("design:type", String)
], RefundRequestQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '订单ID', example: 'ORDER123' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单ID必须为字符串' }),
    __metadata("design:type", String)
], RefundRequestQueryDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '用户ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], RefundRequestQueryDto.prototype, "userId", void 0);
class ProcessRefundRequestDto {
}
exports.ProcessRefundRequestDto = ProcessRefundRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款申请ID', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '申请ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ProcessRefundRequestDto.prototype, "requestId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '处理动作', example: 'approve', enum: ['approve', 'reject'] }),
    (0, class_validator_1.IsString)({ message: '处理动作必须为字符串' }),
    (0, class_validator_1.IsIn)(['approve', 'reject'], { message: '处理动作只能是：approve或reject' }),
    __metadata("design:type", String)
], ProcessRefundRequestDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '管理员处理意见', example: '同意退款' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '处理意见必须为字符串' }),
    __metadata("design:type", String)
], ProcessRefundRequestDto.prototype, "adminComment", void 0);
class RefundRequestListResponseDto {
}
exports.RefundRequestListResponseDto = RefundRequestListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请ID' }),
    __metadata("design:type", Number)
], RefundRequestListResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], RefundRequestListResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "userNickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "refundReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请退款金额' }),
    __metadata("design:type", Number)
], RefundRequestListResponseDto.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请状态', example: '1' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态文本' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "statusText", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '管理员处理意见', required: false }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "adminComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请时间' }),
    __metadata("design:type", Date)
], RefundRequestListResponseDto.prototype, "requestTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '处理时间', required: false }),
    __metadata("design:type", Date)
], RefundRequestListResponseDto.prototype, "processTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单总金额' }),
    __metadata("design:type", Number)
], RefundRequestListResponseDto.prototype, "orderTotalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态' }),
    __metadata("design:type", String)
], RefundRequestListResponseDto.prototype, "orderStatus", void 0);
var RefundRequestStatus;
(function (RefundRequestStatus) {
    RefundRequestStatus["PENDING"] = "1";
    RefundRequestStatus["REFUNDED"] = "2";
    RefundRequestStatus["REJECTED"] = "3";
})(RefundRequestStatus || (exports.RefundRequestStatus = RefundRequestStatus = {}));
exports.RefundRequestStatusText = {
    [RefundRequestStatus.PENDING]: '待审核',
    [RefundRequestStatus.REFUNDED]: '已退款',
    [RefundRequestStatus.REJECTED]: '已拒绝',
};
class ConfirmRefundPickupDto {
}
exports.ConfirmRefundPickupDto = ConfirmRefundPickupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORD20240101001' }),
    (0, class_validator_1.IsString)({ message: '订单ID必须为字符串' }),
    __metadata("design:type", String)
], ConfirmRefundPickupDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '配送员ID', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '配送员ID必须为数字' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ConfirmRefundPickupDto.prototype, "deliveryStaffId", void 0);
//# sourceMappingURL=refund-request.dto.js.map