import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare class CreatePostDto {
    postName: string;
    postCode: string;
    status?: string;
    remark?: string;
    postSort?: number;
}
export declare class UpdatePostDto extends CreatePostDto {
    postId: number;
}
export declare class ListPostDto extends PagingDto {
    postName?: string;
    postCode?: string;
    status?: string;
}
