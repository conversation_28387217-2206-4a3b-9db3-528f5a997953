"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenTableUpdate = exports.GenTableList = exports.TableId = exports.TableName = exports.GenDbTableList = exports.UpdateGenTableDto = exports.CreateGenTableDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const index_1 = require("../../../../common/dto/index");
class CreateGenTableDto {
}
exports.CreateGenTableDto = CreateGenTableDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '表名称' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "tableName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '表描述' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "tableComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '实体类名称' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "className", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成包路径' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "packageName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成模块名' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "moduleName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成业务名' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "businessName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成功能名' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "functionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成功能作者' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "functionAuthor", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '创建人' }),
    __metadata("design:type", String)
], CreateGenTableDto.prototype, "createBy", void 0);
class UpdateGenTableDto extends CreateGenTableDto {
}
exports.UpdateGenTableDto = UpdateGenTableDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '编号' }),
    __metadata("design:type", Number)
], UpdateGenTableDto.prototype, "tableId", void 0);
class GenDbTableList extends index_1.PagingDto {
}
exports.GenDbTableList = GenDbTableList;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GenDbTableList.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GenDbTableList.prototype, "tableComment", void 0);
class TableName {
}
exports.TableName = TableName;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableName.prototype, "tableNames", void 0);
class TableId {
}
exports.TableId = TableId;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableId.prototype, "tableIds", void 0);
class GenTableList extends index_1.PagingDto {
}
exports.GenTableList = GenTableList;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GenTableList.prototype, "tableNames", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableList.prototype, "tableComment", void 0);
class GenTableUpdate {
}
exports.GenTableUpdate = GenTableUpdate;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], GenTableUpdate.prototype, "tableId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "tableComment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "className", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "functionAuthor", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "remark", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "tplCategory", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "packageName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "moduleName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "businessName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "functionName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "genType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], GenTableUpdate.prototype, "columns", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenTableUpdate.prototype, "tplWebType", void 0);
//# sourceMappingURL=create-genTable-dto.js.map