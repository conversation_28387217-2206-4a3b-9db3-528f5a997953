import { BaseEntity } from '../../../../common/entities/base';
import { MiniprogramUser } from '../../user/entities/user.entity';
import { ProductEntity } from '../../product/entities/product.entity';
import { OrderEntity } from '../../order/entities/order.entity';
export declare class Review extends BaseEntity {
    reviewId: number;
    userId: number;
    productId: number;
    orderId: string;
    specId: number | null;
    rating: number;
    content: string;
    images: string | null;
    isAnonymous: string;
    reply: string | null;
    replyTime: Date | null;
    likeCount: number;
    user: MiniprogramUser;
    product: ProductEntity;
    order: OrderEntity;
}
