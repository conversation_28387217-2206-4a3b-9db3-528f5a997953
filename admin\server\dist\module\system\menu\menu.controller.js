"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MenuController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const menu_service_1 = require("./menu.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let MenuController = class MenuController {
    constructor(menuService) {
        this.menuService = menuService;
    }
    create(createMenuDto) {
        return this.menuService.create(createMenuDto);
    }
    findAll(query) {
        return this.menuService.findAll(query);
    }
    treeSelect() {
        return this.menuService.treeSelect();
    }
    roleMenuTreeselect(menuId) {
        return this.menuService.roleMenuTreeselect(+menuId);
    }
    findOne(menuId) {
        return this.menuService.findOne(+menuId);
    }
    update(updateMenuDto) {
        return this.menuService.update(updateMenuDto);
    }
    remove(menuId) {
        return this.menuService.remove(+menuId);
    }
};
exports.MenuController = MenuController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateMenuDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:add'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateMenuDto]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-列表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListDeptDto]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-树表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:query'),
    (0, common_1.Get)('/treeselect'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "treeSelect", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-角色-树表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:query'),
    (0, common_1.Get)('/roleMenuTreeselect/:menuId'),
    __param(0, (0, common_1.Param)('menuId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "roleMenuTreeselect", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:query'),
    (0, common_1.Get)(':menuId'),
    __param(0, (0, common_1.Param)('menuId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-修改',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdateMenuDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateMenuDto]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '菜单管理-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:menu:remove'),
    (0, common_1.Delete)(':menuId'),
    __param(0, (0, common_1.Param)('menuId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], MenuController.prototype, "remove", null);
exports.MenuController = MenuController = __decorate([
    (0, swagger_1.ApiTags)('菜单管理'),
    (0, common_1.Controller)('system/menu'),
    __metadata("design:paramtypes", [menu_service_1.MenuService])
], MenuController);
//# sourceMappingURL=menu.controller.js.map