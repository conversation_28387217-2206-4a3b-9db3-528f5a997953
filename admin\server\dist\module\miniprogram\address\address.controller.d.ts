import { AddressService } from './address.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto, AddressQueryDto, DeliveryRangeDto } from './dto/update-address.dto';
import { ResultData } from 'src/common/utils/result';
export declare class AddressController {
    private readonly addressService;
    constructor(addressService: AddressService);
    create(userId: number, createAddressDto: CreateAddressDto): Promise<ResultData>;
    findByUserId(userId: number, queryDto: AddressQueryDto): Promise<ResultData>;
    findById(userId: number, addressId: number): Promise<ResultData>;
    update(userId: number, addressId: number, updateAddressDto: UpdateAddressDto): Promise<ResultData>;
    setDefault(userId: number, addressId: number): Promise<ResultData>;
    remove(userId: number, addressId: number): Promise<ResultData>;
    getDefaultAddress(userId: number): Promise<ResultData>;
    getAddressStats(userId: number): Promise<ResultData>;
    checkDeliveryRange(deliveryRangeDto: DeliveryRangeDto): Promise<ResultData>;
    adminList(pageNum?: number, pageSize?: number, userId?: number, receiverName?: string, receiverPhone?: string, addressName?: string): Promise<ResultData>;
}
