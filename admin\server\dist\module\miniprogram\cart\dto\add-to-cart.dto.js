"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddToCartDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class AddToCartDto {
}
exports.AddToCartDto = AddToCartDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '商品ID必须是整数' }),
    (0, class_validator_1.IsPositive)({ message: '商品ID必须大于0' }),
    __metadata("design:type", Number)
], AddToCartDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量', example: 1, minimum: 1, maximum: 999 }),
    (0, class_validator_1.IsInt)({ message: '商品数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '商品数量不能少于1' }),
    (0, class_validator_1.Max)(999, { message: '商品数量不能超过999' }),
    __metadata("design:type", Number)
], AddToCartDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID', example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '规格ID必须是整数' }),
    __metadata("design:type", Number)
], AddToCartDto.prototype, "specId", void 0);
//# sourceMappingURL=add-to-cart.dto.js.map