export declare enum CacheEnum {
    LOGIN_TOKEN_KEY = "login_tokens:",
    CAPTCHA_CODE_KEY = "captcha_codes:",
    SYS_CONFIG_KEY = "sys_config:",
    SYS_DICT_KEY = "sys_dict:",
    REPEAT_SUBMIT_KEY = "repeat_submit:",
    RATE_LIMIT_KEY = "rate_limit:",
    PWD_ERR_CNT_KEY = "pwd_err_cnt:",
    GZ_TYPE = "gz_type:",
    MA_CODE = "ma_code:",
    SYS_USER_KEY = "user:",
    SYS_DEPT_KEY = "sys_dept:"
}
export declare enum DataScopeEnum {
    DATA_SCOPE_ALL = "1",
    DATA_SCOPE_CUSTOM = "2",
    DATA_SCOPE_DEPT = "3",
    DATA_SCOPE_DEPT_AND_CHILD = "4",
    DATA_SCOPE_SELF = "5"
}
export declare enum DelFlagEnum {
    NORMAL = "0",
    DELETE = "1"
}
export declare enum StatusEnum {
    NORMAL = "0",
    STOP = "1"
}
export declare enum SexEnum {
    MAN = "0",
    WOMAN = "1"
}
export declare enum SortRuleEnum {
    ASCENDING = "ascending",
    DESCENDING = "descending"
}
export declare enum CharEnum {
    ENABLE = "0",
    DISABLE = "1"
}
