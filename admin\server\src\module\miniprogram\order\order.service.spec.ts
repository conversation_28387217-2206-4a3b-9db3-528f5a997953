import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { OrderService } from './order.service';
import { OrderEntity } from './entities/order.entity';
import { OrderItemEntity } from './entities/order-item.entity';
import { RefundRequestEntity } from './entities/refund-request.entity';
import { PaymentEntity } from '../../payment/entities/payment.entity';
import { MiniprogramUser } from '../user/entities/user.entity';
import { PaymentService } from '../../payment/payment.service';
import { NotificationGateway } from '../../notification/notification.gateway';

describe('OrderService - Refund Reason Fix', () => {
  let service: OrderService;
  let orderRepository: any;
  let dataSource: any;

  const mockOrderRepository = {
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(),
    })),
  };

  const mockDataSource = {
    query: jest.fn(),
  };

  const mockPaymentService = {
    // mock methods as needed
  };

  const mockNotificationGateway = {
    // mock methods as needed
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderService,
        {
          provide: getRepositoryToken(OrderEntity),
          useValue: mockOrderRepository,
        },
        {
          provide: getRepositoryToken(OrderItemEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(PaymentEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MiniprogramUser),
          useValue: {},
        },
        {
          provide: getRepositoryToken(RefundRequestEntity),
          useValue: {},
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
        {
          provide: NotificationGateway,
          useValue: mockNotificationGateway,
        },
      ],
    }).compile();

    service = module.get<OrderService>(OrderService);
    orderRepository = module.get(getRepositoryToken(OrderEntity));
    dataSource = module.get<DataSource>(DataSource);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll (管理端订单列表)', () => {
    it('should include refund request join and select in query', async () => {
      // Mock data
      const mockOrders = [
        {
          orderId: 'test-order-1',
          userId: 1,
          orderType: 1,
          totalAmount: 100,
          status: 1,
          refund_status: '1',
          refund_reason: '商品质量问题',
          refund_request_time: new Date(),
          orderItems: [],
          user: { userId: 1, nickname: 'test' },
        },
      ];

      const queryBuilder = mockOrderRepository.createQueryBuilder();
      queryBuilder.getManyAndCount.mockResolvedValue([mockOrders, 1]);
      mockDataSource.query.mockResolvedValue([]);

      const queryDto = { pageNum: 1, pageSize: 10 };
      const result = await service.findAll(queryDto);

      // 验证查询构建器是否正确调用了退款申请表的连接
      expect(queryBuilder.leftJoin).toHaveBeenCalledWith(
        'refund_requests',
        'refundRequest',
        'refundRequest.order_id = order.orderId'
      );

      // 验证是否添加了退款申请相关字段的选择
      expect(queryBuilder.addSelect).toHaveBeenCalledWith([
        'refundRequest.status AS refund_status',
        'refundRequest.refund_reason AS refund_reason',
        'refundRequest.request_time AS refund_request_time'
      ]);

      // 验证返回结果包含退款信息
      expect(result.data.list[0]).toHaveProperty('refundStatus');
      expect(result.data.list[0]).toHaveProperty('refundReason');
      expect(result.data.list[0]).toHaveProperty('refundRequestTime');
      expect(result.data.list[0].refundReason).toBe('商品质量问题');
    });
  });

  describe('findSelfPickupOrders (自提订单列表)', () => {
    it('should include refund request join and select in query', async () => {
      // Mock data
      const mockOrders = [
        {
          orderId: 'test-order-2',
          userId: 2,
          orderType: 1,
          totalAmount: 200,
          status: 1,
          deliveryType: '2',
          refund_status: '2',
          refund_reason: '不想要了',
          refund_request_time: new Date(),
          orderItems: [],
          user: { userId: 2, nickname: 'test2' },
        },
      ];

      const queryBuilder = mockOrderRepository.createQueryBuilder();
      queryBuilder.getManyAndCount.mockResolvedValue([mockOrders, 1]);

      const queryDto = { pageNum: 1, pageSize: 10 };
      const result = await service.findSelfPickupOrders(queryDto);

      // 验证查询构建器是否正确调用了退款申请表的连接
      expect(queryBuilder.leftJoin).toHaveBeenCalledWith(
        'refund_requests',
        'refundRequest',
        'refundRequest.order_id = order.orderId'
      );

      // 验证是否添加了退款申请相关字段的选择
      expect(queryBuilder.addSelect).toHaveBeenCalledWith([
        'refundRequest.status AS refund_status',
        'refundRequest.refund_reason AS refund_reason',
        'refundRequest.request_time AS refund_request_time'
      ]);

      // 验证返回结果包含退款信息
      expect(result.data.list[0]).toHaveProperty('refundStatus');
      expect(result.data.list[0]).toHaveProperty('refundReason');
      expect(result.data.list[0]).toHaveProperty('refundRequestTime');
      expect(result.data.list[0].refundReason).toBe('不想要了');
    });
  });
});
