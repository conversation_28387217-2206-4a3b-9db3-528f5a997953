export declare class PaymentResultDto {
    paymentId: number;
    orderId: string;
    paymentStatus: string;
    paymentAmount: number;
    transactionId?: string;
    paymentTime?: Date;
    paymentParams?: any;
}
export declare class BalanceRechargeResultDto {
    paymentId: number;
    rechargeId: string;
    paymentStatus: string;
    amount: number;
    transactionId?: string;
    paymentTime?: Date;
    paymentParams?: any;
}
export declare class UserBalanceRechargeResultDto {
    userId: number;
    beforeBalance: number;
    amount: number;
    afterBalance: number;
    rechargeTime: Date;
    operator: string;
    remark?: string;
}
export declare class PaymentListDto {
    paymentId: number;
    orderId: string;
    userId: number;
    paymentMethod: string;
    paymentAmount: number;
    paymentStatus: string;
    transactionId?: string;
    paymentTime?: Date;
    refundAmount: number;
    createTime: Date;
}
export declare class RefundResultDto {
    paymentId: number;
    refundAmount: number;
    refundTime: Date;
    refundStatus: string;
}
export declare class BalanceChangeOrderItemDto {
    productId: number;
    productName: string;
    productImage: string;
    quantity: number;
    price: number;
    totalPrice: number;
}
export declare class BalanceChangeOrderInfoDto {
    orderId: string;
    items: BalanceChangeOrderItemDto[];
}
export declare class BalanceChangeListDto {
    id: string;
    userId: number;
    type: 'recharge' | 'consume';
    amount: number;
    description: string;
    time: Date;
    status: string;
    orderInfo?: BalanceChangeOrderInfoDto;
}
