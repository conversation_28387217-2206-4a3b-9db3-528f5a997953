"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const job_service_1 = require("./job.service");
const create_job_dto_1 = require("./dto/create-job.dto");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let JobController = class JobController {
    constructor(jobService) {
        this.jobService = jobService;
    }
    list(query) {
        return this.jobService.list(query);
    }
    getInfo(jobId) {
        return this.jobService.getJob(jobId);
    }
    add(createJobDto, req) {
        return this.jobService.create(createJobDto, req.user?.userName);
    }
    changeStatus(jobId, status, req) {
        return this.jobService.changeStatus(jobId, status, req.user?.userName);
    }
    update(jobId, updateJobDto, req) {
        return this.jobService.update(jobId, updateJobDto, req.user?.userName);
    }
    remove(jobIds) {
        return this.jobService.remove(jobIds.split(',').map((id) => +id));
    }
    run(jobId) {
        return this.jobService.run(jobId);
    }
    async export(res, body) {
        return this.jobService.export(res, body);
    }
};
exports.JobController = JobController;
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取定时任务列表' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "list", null);
__decorate([
    (0, common_1.Get)(':jobId'),
    (0, swagger_1.ApiOperation)({ summary: '获取定时任务详细信息' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:query'),
    __param(0, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "getInfo", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建定时任务' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:add'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_job_dto_1.CreateJobDto, Object]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "add", null);
__decorate([
    (0, common_1.Put)('changeStatus'),
    (0, swagger_1.ApiOperation)({ summary: '修改任务状态' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:changeStatus'),
    __param(0, (0, common_1.Body)('jobId')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Object]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "changeStatus", null);
__decorate([
    (0, common_1.Put)(''),
    (0, swagger_1.ApiOperation)({ summary: '修改定时任务' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:edit'),
    __param(0, (0, common_1.Body)('jobId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':jobIds'),
    (0, swagger_1.ApiOperation)({ summary: '删除定时任务' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:remove'),
    __param(0, (0, common_1.Param)('jobIds')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "remove", null);
__decorate([
    (0, common_1.Put)('/run'),
    (0, swagger_1.ApiOperation)({ summary: '立即执行一次' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:changeStatus'),
    __param(0, (0, common_1.Body)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], JobController.prototype, "run", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出定时任务为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_job_dto_1.ListJobDto]),
    __metadata("design:returntype", Promise)
], JobController.prototype, "export", null);
exports.JobController = JobController = __decorate([
    (0, swagger_1.ApiTags)('定时任务管理'),
    (0, common_1.Controller)('monitor/job'),
    __metadata("design:paramtypes", [job_service_1.JobService])
], JobController);
//# sourceMappingURL=job.controller.js.map