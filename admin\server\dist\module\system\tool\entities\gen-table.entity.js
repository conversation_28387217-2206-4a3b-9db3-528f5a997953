"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenTableEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let GenTableEntity = class GenTableEntity extends base_1.BaseEntity {
};
exports.GenTableEntity = GenTableEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '编号' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'table_id', comment: '编号' }),
    __metadata("design:type", Number)
], GenTableEntity.prototype, "tableId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '表名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'table_name', length: 200, default: '', comment: '表名称' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "tableName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '表描述' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'table_comment', length: 500, default: '', comment: '表描述' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "tableComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '关联子表的表名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'sub_table_name', length: 64, nullable: true, comment: '关联子表的表名' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "subTableName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '子表关联的外键名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'sub_table_fk_name', length: 64, nullable: true, comment: '子表关联的外键名' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "subTableFkName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '实体类名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'class_name', length: 100, default: '', comment: '实体类名称' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "className", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '使用的模板（crud单表操作 tree树表操作）' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'tpl_category', length: 200, default: 'crud', comment: '使用的模板（crud单表操作 tree树表操作）' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "tplCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '前端模板类型（element-ui模版 element-plus模版）' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'tpl_web_type', length: 30, default: 'element-plus', comment: '前端模板类型（element-ui模版 element-plus模版）' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "tplWebType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成包路径' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'package_name', length: 100, comment: '生成包路径' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "packageName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成模块名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'module_name', length: 30, comment: '生成模块名' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "moduleName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成业务名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'business_name', length: 30, comment: '生成业务名' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "businessName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成功能名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'function_name', length: 50, comment: '生成功能名' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "functionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成功能作者' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'function_author', length: 50, comment: '生成功能作者' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "functionAuthor", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成代码方式（0zip压缩包 1自定义路径）' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'gen_type', length: 1, default: '0', comment: '生成代码方式（0zip压缩包 1自定义路径）' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "genType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '生成路径（不填默认项目路径）' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'gen_path', length: 200, default: '/', comment: '生成路径（不填默认项目路径）' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "genPath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '其它生成选项' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'options', default: '', length: 1000, comment: '其它生成选项' }),
    __metadata("design:type", String)
], GenTableEntity.prototype, "options", void 0);
exports.GenTableEntity = GenTableEntity = __decorate([
    (0, typeorm_1.Entity)('gen_table', {
        comment: '代码生成业务表',
    })
], GenTableEntity);
//# sourceMappingURL=gen-table.entity.js.map