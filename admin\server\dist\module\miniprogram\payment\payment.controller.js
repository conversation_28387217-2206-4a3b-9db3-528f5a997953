"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_service_1 = require("./payment.service");
const payment_request_dto_1 = require("./dto/payment-request.dto");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("../../system/user/user.decorator");
let PaymentController = PaymentController_1 = class PaymentController {
    constructor(paymentService) {
        this.paymentService = paymentService;
        this.logger = new common_1.Logger(PaymentController_1.name);
    }
    async createPayment(userId, paymentRequest) {
        try {
            this.logger.log(`用户 ${userId} 创建支付订单: ${JSON.stringify(paymentRequest)}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const result = await this.paymentService.createPayment(paymentRequest, userId);
            return result;
        }
        catch (error) {
            this.logger.error(`创建支付订单失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '创建支付订单失败');
        }
    }
    async createBalanceRecharge(userId, rechargeDto) {
        try {
            this.logger.log(`用户 ${userId} 创建余额充值订单: ${JSON.stringify(rechargeDto)}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const result = await this.paymentService.createBalanceRecharge(rechargeDto, userId);
            return result;
        }
        catch (error) {
            this.logger.error(`创建余额充值订单失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '创建余额充值订单失败');
        }
    }
    async rechargeUserBalance(rechargeDto, operatorId) {
        try {
            this.logger.log(`管理员 ${operatorId} 为用户 ${rechargeDto.userId} 充值余额: ${JSON.stringify(rechargeDto)}`);
            if (!operatorId) {
                return result_1.ResultData.fail(401, '管理员未登录');
            }
            const result = await this.paymentService.rechargeUserBalance(rechargeDto, operatorId);
            return result;
        }
        catch (error) {
            this.logger.error(`用户余额充值失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '用户余额充值失败');
        }
    }
    async handlePaymentNotify(orderId, notifyData, res) {
        try {
            this.logger.log(`接收支付回调: 订单=${orderId}, 数据=${JSON.stringify(notifyData)}`);
            const result = await this.paymentService.handlePaymentNotify(orderId, notifyData);
            if (result.success) {
                this.logger.log(`支付回调处理成功: 订单=${orderId}`);
                return res.status(common_1.HttpStatus.OK).send();
            }
            else {
                this.logger.warn(`支付回调处理失败（业务逻辑失败）: 订单=${orderId}, 错误信息=${result.message}`);
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    code: 'FAIL',
                    message: result.message || '支付回调处理失败',
                });
            }
        }
        catch (error) {
            this.logger.error(`支付回调处理异常: ${error.message}, 订单=${orderId}`);
            return res.status(error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                code: 'FAIL',
                message: error.message || '支付回调处理失败',
            });
        }
    }
    async getUserBalance(userId) {
        try {
            this.logger.log(`查询用户余额: ${userId}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const result = await this.paymentService.getUserBalance(userId);
            return result;
        }
        catch (error) {
            this.logger.error(`查询用户余额失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '查询用户余额失败');
        }
    }
    async getPaymentStatus(orderId, userId) {
        try {
            this.logger.log(`查询支付状态: 订单=${orderId}, 用户=${userId}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const result = await this.paymentService.getPaymentStatus(orderId, userId);
            return result;
        }
        catch (error) {
            this.logger.error(`查询支付状态失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '查询支付状态失败');
        }
    }
    async getUserBalanceChangeList(pageNum = '1', pageSize = '10', userId) {
        try {
            this.logger.log(`获取用户余额变动记录: 用户=${userId}, 页码=${pageNum}, 页大小=${pageSize}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const pageNumInt = parseInt(pageNum, 10) || 1;
            const pageSizeInt = parseInt(pageSize, 10) || 10;
            const result = await this.paymentService.getUserBalanceChangeList(userId, pageNumInt, pageSizeInt);
            return result;
        }
        catch (error) {
            this.logger.error(`获取用户余额变动记录失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '获取用户余额变动记录失败');
        }
    }
    async requestRefund(refundRequest, userId) {
        try {
            this.logger.log(`申请退款: 用户=${userId}, 请求=${JSON.stringify(refundRequest)}`);
            if (!userId) {
                return result_1.ResultData.fail(401, '用户未登录');
            }
            const result = await this.paymentService.requestRefund(refundRequest, userId);
            return result;
        }
        catch (error) {
            this.logger.error(`申请退款失败: ${error.message}`);
            return result_1.ResultData.fail(error.status || 500, error.message || '申请退款失败');
        }
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Post)('order/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '创建支付订单' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功', type: result_1.ResultData }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, payment_request_dto_1.PaymentRequestDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Post)('recharge/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '创建余额充值订单' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功', type: result_1.ResultData }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, payment_request_dto_1.BalanceRechargeDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createBalanceRecharge", null);
__decorate([
    (0, common_1.Post)('recharge/admin'),
    (0, swagger_1.ApiOperation)({ summary: '管理员为用户充值余额' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '充值成功', type: result_1.ResultData }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Query)('operatorId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_request_dto_1.UserBalanceRechargeDto, Number]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "rechargeUserBalance", null);
__decorate([
    (0, common_1.Post)('notify/:orderId'),
    (0, swagger_1.ApiOperation)({ summary: '支付回调处理（第三方支付平台调用）' }),
    (0, swagger_1.ApiParam)({ name: 'orderId', description: '订单ID或充值单号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '回调处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '回调处理失败', schema: { example: { code: 'FAIL', message: '失败' } } }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '内部服务器错误', schema: { example: { code: 'FAIL', message: '失败' } } }),
    __param(0, (0, common_1.Param)('orderId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, payment_request_dto_1.PaymentNotifyDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handlePaymentNotify", null);
__decorate([
    (0, common_1.Get)('balance/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '查询用户余额' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getUserBalance", null);
__decorate([
    (0, common_1.Get)('status/:orderId/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '查询支付状态' }),
    (0, swagger_1.ApiParam)({ name: 'orderId', description: '订单ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Param)('orderId')),
    __param(1, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPaymentStatus", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户余额变动记录列表' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: true, example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'pageNum', description: '页码', required: false, example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '页大小', required: false, example: 10 }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)('pageNum')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getUserBalanceChangeList", null);
__decorate([
    (0, common_1.Post)('refund/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '申请退款' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款申请成功', type: result_1.ResultData }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_request_dto_1.RefundRequestDto, Number]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "requestRefund", null);
exports.PaymentController = PaymentController = PaymentController_1 = __decorate([
    (0, swagger_1.ApiTags)('支付管理'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, common_1.Controller)('miniprogram/payment'),
    __metadata("design:paramtypes", [payment_service_1.PaymentService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map