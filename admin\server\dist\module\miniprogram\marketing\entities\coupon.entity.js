"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Coupon = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
const coupon_interface_1 = require("../interfaces/coupon.interface");
const user_coupon_entity_1 = require("./user-coupon.entity");
let Coupon = class Coupon extends base_1.BaseEntity {
};
exports.Coupon = Coupon;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'id', comment: '优惠券ID' }),
    __metadata("design:type", Number)
], Coupon.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'name', length: 100, comment: '优惠券名称' }),
    (0, typeorm_1.Index)('idx_coupon_name'),
    __metadata("design:type", String)
], Coupon.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券类型 1满减券 2折扣券 3无门槛券' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'type',
        length: 1,
        comment: '优惠券类型 1满减券 2折扣券 3无门槛券',
    }),
    (0, typeorm_1.Index)('idx_coupon_type'),
    __metadata("design:type", String)
], Coupon.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券状态 0无效 1有效' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'status',
        length: 1,
        comment: '优惠券状态 0无效 1有效',
        default: coupon_interface_1.CouponStatus.ACTIVE,
    }),
    (0, typeorm_1.Index)('idx_coupon_status'),
    __metadata("design:type", String)
], Coupon.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券描述' }),
    (0, typeorm_1.Column)({
        type: 'text',
        name: 'description',
        comment: '优惠券描述',
        nullable: true,
    }),
    __metadata("design:type", String)
], Coupon.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '满减条件金额(分)' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'condition_amount',
        comment: '满减条件金额(分)',
        default: 0,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "conditionAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠金额(分)' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'discount_amount',
        comment: '优惠金额(分)',
        default: 0,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣率(0-100)' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'discount_rate',
        comment: '折扣率(0-100)',
        default: 0,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "discountRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '可使用商品范围 1全部商品 2指定商品 3指定商品类别' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'goods_type',
        length: 1,
        comment: '可使用商品范围 1全部商品 2指定商品 3指定商品类别',
        default: '1',
    }),
    __metadata("design:type", String)
], Coupon.prototype, "goodsType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID列表，当goodsType=2时使用' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'goods_ids',
        length: 1000,
        comment: '商品ID列表，当goodsType=2时使用',
        nullable: true,
    }),
    __metadata("design:type", String)
], Coupon.prototype, "goodsIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品类别ID列表，当goodsType=3时使用' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'category_ids',
        length: 500,
        comment: '商品类别ID列表，当goodsType=3时使用',
        nullable: true,
    }),
    __metadata("design:type", String)
], Coupon.prototype, "categoryIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期开始时间' }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'start_time',
        comment: '有效期开始时间',
    }),
    (0, typeorm_1.Index)('idx_coupon_time'),
    __metadata("design:type", Date)
], Coupon.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期结束时间' }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'end_time',
        comment: '有效期结束时间',
    }),
    __metadata("design:type", Date)
], Coupon.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券总数量' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'total_count',
        comment: '优惠券总数量',
        default: 0,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '已领取数量' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'used_count',
        comment: '已领取数量',
        default: 0,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "usedCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每人限领数量' }),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'per_user_limit',
        comment: '每人限领数量',
        default: 1,
    }),
    __metadata("design:type", Number)
], Coupon.prototype, "perUserLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发放方式 1公开领取 2指定发放' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'distribute_type',
        length: 1,
        comment: '发放方式 1公开领取 2指定发放',
        default: coupon_interface_1.CouponDistributeType.PUBLIC,
    }),
    (0, typeorm_1.Index)('idx_coupon_distribute_type'),
    __metadata("design:type", String)
], Coupon.prototype, "distributeType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户优惠券关联' }),
    (0, typeorm_1.OneToMany)(() => user_coupon_entity_1.UserCoupon, (userCoupon) => userCoupon.coupon, { createForeignKeyConstraints: false }),
    __metadata("design:type", Array)
], Coupon.prototype, "userCoupons", void 0);
exports.Coupon = Coupon = __decorate([
    (0, typeorm_1.Entity)('t_coupon', { comment: '优惠券表' })
], Coupon);
//# sourceMappingURL=coupon.entity.js.map