"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoticeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const notice_entity_1 = require("./entities/notice.entity");
let NoticeService = class NoticeService {
    constructor(sysNoticeEntityRep) {
        this.sysNoticeEntityRep = sysNoticeEntityRep;
    }
    async create(createNoticeDto) {
        await this.sysNoticeEntityRep.save(createNoticeDto);
        return result_1.ResultData.ok();
    }
    async findAll(query) {
        const entity = this.sysNoticeEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.noticeTitle) {
            entity.andWhere(`entity.noticeTitle LIKE "%${query.noticeTitle}%"`);
        }
        if (query.createBy) {
            entity.andWhere(`entity.createBy LIKE "%${query.createBy}%"`);
        }
        if (query.noticeType) {
            entity.andWhere('entity.noticeType = :noticeType', { noticeType: query.noticeType });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOne(noticeId) {
        const data = await this.sysNoticeEntityRep.findOne({
            where: {
                noticeId: noticeId,
            },
        });
        return result_1.ResultData.ok(data);
    }
    async update(updateNoticeDto) {
        await this.sysNoticeEntityRep.update({
            noticeId: updateNoticeDto.noticeId,
        }, updateNoticeDto);
        return result_1.ResultData.ok();
    }
    async remove(noticeIds) {
        const data = await this.sysNoticeEntityRep.update({ noticeId: (0, typeorm_2.In)(noticeIds) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
};
exports.NoticeService = NoticeService;
exports.NoticeService = NoticeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(notice_entity_1.SysNoticeEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], NoticeService);
//# sourceMappingURL=notice.service.js.map