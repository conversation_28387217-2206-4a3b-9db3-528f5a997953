import { CouponService } from './coupon.service';
import { CreateCouponDto } from './dto/coupon/create-coupon.dto';
import { UpdateCouponDto } from './dto/coupon/update-coupon.dto';
import { QueryCouponDto } from './dto/coupon/query-coupon.dto';
import { ReceiveCouponDto, UseCouponDto, UserCouponQueryDto, AvailableCouponQueryDto } from './dto/coupon/user-coupon.dto';
import { DistributeCouponDto, QueryUserListDto } from './dto/coupon/distribute-coupon.dto';
import { ResultData } from '../../../common/utils/result';
import { CouponResponse } from './dto/coupon/coupon-response.dto';
export declare class CouponController {
    private readonly couponService;
    private readonly logger;
    constructor(couponService: CouponService);
    createCoupon(createCouponDto: CreateCouponDto): Promise<CouponResponse>;
    updateCoupon(updateCouponDto: UpdateCouponDto): Promise<CouponResponse>;
    getCouponList(queryParams: QueryCouponDto): Promise<CouponResponse>;
    getCouponDetail(couponId: number): Promise<CouponResponse>;
    deleteCoupon(couponId: number): Promise<CouponResponse>;
    receiveCoupon(receiveCouponDto: ReceiveCouponDto): Promise<ResultData>;
    getUserCouponList(queryParams: UserCouponQueryDto): Promise<CouponResponse>;
    useCoupon(useCouponDto: UseCouponDto): Promise<CouponResponse>;
    getAvailableCoupons(userId: number, orderAmount: number): Promise<CouponResponse>;
    getAvailableToReceiveCoupons(query: AvailableCouponQueryDto): Promise<ResultData>;
    testConcurrentReceive(params: {
        userCount: number;
        couponId: number;
    }): Promise<ResultData>;
    distributeCoupon(distributeCouponDto: DistributeCouponDto): Promise<ResultData>;
    getUserList(queryParams: QueryUserListDto): Promise<ResultData>;
    getDistributeRecords(couponId: number, queryParams: QueryUserListDto): Promise<ResultData>;
}
