import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare enum TypeEnum {
    YES = "Y",
    NO = "N"
}
export declare class CreateConfigDto {
    configName: string;
    configValue: string;
    configKey: string;
    configType: string;
    remark?: string;
    status?: string;
}
export declare class UpdateConfigDto extends CreateConfigDto {
    configId: number;
}
export declare class ListConfigDto extends PagingDto {
    configName?: string;
    configKey?: string;
    configType?: string;
}
