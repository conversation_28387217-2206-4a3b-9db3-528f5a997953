"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ReviewService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const review_entity_1 = require("./entities/review.entity");
const order_service_1 = require("../order/order.service");
let ReviewService = ReviewService_1 = class ReviewService {
    constructor(reviewRepository, orderService) {
        this.reviewRepository = reviewRepository;
        this.orderService = orderService;
        this.logger = new common_1.Logger(ReviewService_1.name);
    }
    async create(createReviewDto) {
        try {
            const existingReview = await this.reviewRepository.findOne({
                where: {
                    userId: createReviewDto.userId,
                    orderId: createReviewDto.orderId,
                    productId: createReviewDto.productId,
                    specId: createReviewDto.specId || null,
                    delFlag: '0',
                },
            });
            if (existingReview) {
                return result_1.ResultData.fail(400, '您已经评价过该商品');
            }
            const newReview = this.reviewRepository.create({
                ...createReviewDto,
                images: createReviewDto.images ? JSON.stringify(createReviewDto.images) : null,
                createBy: createReviewDto.userId.toString(),
            });
            const savedReview = await this.reviewRepository.save(newReview);
            await this.orderService.updateOrderEvaluationStatus(createReviewDto.orderId, createReviewDto.userId, '1');
            return result_1.ResultData.ok(savedReview.reviewId);
        }
        catch (error) {
            this.logger.error(`创建评价失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('创建评价失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(queryDto) {
        try {
            const { pageNum, pageSize, productId, userId, orderId, minRating, maxRating, hasImage, orderBy, orderType } = queryDto;
            const whereConditions = {
                delFlag: '0',
            };
            if (productId) {
                whereConditions.productId = productId;
            }
            if (userId) {
                whereConditions.userId = userId;
            }
            if (orderId) {
                whereConditions.orderId = orderId;
            }
            if (minRating && maxRating) {
                whereConditions.rating = (0, typeorm_2.Between)(minRating, maxRating);
            }
            else if (minRating) {
                whereConditions.rating = (0, typeorm_2.Between)(minRating, 5);
            }
            else if (maxRating) {
                whereConditions.rating = (0, typeorm_2.Between)(1, maxRating);
            }
            if (hasImage === 1) {
                whereConditions.images = (0, typeorm_2.Not)((0, typeorm_2.IsNull)());
            }
            const [reviews, total] = await this.reviewRepository.findAndCount({
                where: whereConditions,
                order: {
                    [orderBy || 'createTime']: orderType || 'DESC',
                },
                skip: (pageNum - 1) * pageSize,
                take: pageSize,
                relations: ['user', 'product', 'order'],
            });
            const responseData = reviews.map((review) => {
                const reviewResponse = {
                    reviewId: review.reviewId,
                    userId: review.userId,
                    nickname: review.user?.nickname || (review.isAnonymous === '1' ? '匿名用户' : null),
                    avatar: review.user?.avatar || null,
                    productId: review.productId,
                    productName: review.product?.name || '',
                    productImage: review.product?.images ? review.product.images.split(',')[0] : '',
                    orderId: review.orderId,
                    specId: review.specId,
                    specName: null,
                    rating: review.rating,
                    content: review.content,
                    images: review.images ? JSON.parse(review.images) : [],
                    isAnonymous: review.isAnonymous,
                    reply: review.reply,
                    replyTime: review.replyTime,
                    likeCount: review.likeCount,
                    createTime: review.createTime,
                };
                return reviewResponse;
            });
            return result_1.ResultData.ok({
                list: responseData,
                total,
                pageNum,
                pageSize,
            });
        }
        catch (error) {
            this.logger.error(`查询评价列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询评价列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id) {
        try {
            const review = await this.reviewRepository.findOne({
                where: { reviewId: id, delFlag: '0' },
                relations: ['user', 'product', 'order'],
            });
            if (!review) {
                return result_1.ResultData.fail(404, '评价不存在');
            }
            const reviewResponse = {
                reviewId: review.reviewId,
                userId: review.userId,
                nickname: review.user?.nickname || (review.isAnonymous === '1' ? '匿名用户' : null),
                avatar: review.user?.avatar || null,
                productId: review.productId,
                productName: review.product?.name || '',
                productImage: review.product?.images ? review.product.images.split(',')[0] : '',
                orderId: review.orderId,
                specId: review.specId,
                specName: null,
                rating: review.rating,
                content: review.content,
                images: review.images ? JSON.parse(review.images) : [],
                isAnonymous: review.isAnonymous,
                reply: review.reply,
                replyTime: review.replyTime,
                likeCount: review.likeCount,
                createTime: review.createTime,
            };
            return result_1.ResultData.ok(reviewResponse);
        }
        catch (error) {
            this.logger.error(`查询评价详情失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询评价详情失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProductReviewStats(productId) {
        try {
            const totalCount = await this.reviewRepository.count({
                where: { productId, delFlag: '0' },
            });
            const goodCount = await this.reviewRepository.count({
                where: { productId, rating: (0, typeorm_2.Between)(4, 5), delFlag: '0' },
            });
            const mediumCount = await this.reviewRepository.count({
                where: { productId, rating: 3, delFlag: '0' },
            });
            const badCount = await this.reviewRepository.count({
                where: { productId, rating: (0, typeorm_2.Between)(1, 2), delFlag: '0' },
            });
            const hasImageCount = await this.reviewRepository.count({
                where: { productId, images: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()), delFlag: '0' },
            });
            let avgRating = 0;
            if (totalCount > 0) {
                const ratingsSum = await this.reviewRepository
                    .createQueryBuilder('mini_review')
                    .where('mini_review.product_id = :productId', { productId })
                    .andWhere('mini_review.del_flag = :delFlag', { delFlag: '0' })
                    .select('SUM(mini_review.rating)', 'sum')
                    .getRawOne();
                avgRating = ratingsSum && ratingsSum.sum ? parseFloat((ratingsSum.sum / totalCount).toFixed(1)) : 0;
            }
            return result_1.ResultData.ok({
                totalCount,
                goodCount,
                mediumCount,
                badCount,
                hasImageCount,
                avgRating,
                goodRate: totalCount > 0 ? parseFloat(((goodCount / totalCount) * 100).toFixed(1)) : 0,
            });
        }
        catch (error) {
            this.logger.error(`查询商品评价统计失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询商品评价统计失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async reply(id, replyDto, adminId) {
        try {
            const review = await this.reviewRepository.findOne({
                where: { reviewId: id, delFlag: '0' },
            });
            if (!review) {
                return result_1.ResultData.fail(404, '评价不存在');
            }
            review.reply = replyDto.reply;
            review.replyTime = new Date();
            review.updateBy = adminId;
            await this.reviewRepository.save(review);
            return result_1.ResultData.ok(null, '回复成功');
        }
        catch (error) {
            this.logger.error(`回复评价失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('回复评价失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id, userId) {
        try {
            const review = await this.reviewRepository.findOne({
                where: { reviewId: id, delFlag: '0' },
            });
            if (!review) {
                return result_1.ResultData.fail(404, '评价不存在');
            }
            review.delFlag = '1';
            review.updateBy = userId;
            await this.reviewRepository.save(review);
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除评价失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('删除评价失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async like(id) {
        try {
            const review = await this.reviewRepository.findOne({
                where: { reviewId: id, delFlag: '0' },
            });
            if (!review) {
                return result_1.ResultData.fail(404, '评价不存在');
            }
            review.likeCount += 1;
            await this.reviewRepository.save(review);
            return result_1.ResultData.ok(null, '点赞成功');
        }
        catch (error) {
            this.logger.error(`点赞评价失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('点赞评价失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ReviewService = ReviewService;
exports.ReviewService = ReviewService = ReviewService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(review_entity_1.Review)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        order_service_1.OrderService])
], ReviewService);
//# sourceMappingURL=review.service.js.map