"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryTypeText = exports.OrderTypeText = exports.OrderStatusText = exports.DeliveryType = exports.OrderType = exports.OrderStatus = void 0;
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING_PAYMENT"] = "1";
    OrderStatus["PENDING_SHIPMENT"] = "2";
    OrderStatus["SHIPPING"] = "3";
    OrderStatus["COMPLETED"] = "4";
    OrderStatus["CANCELLED"] = "5";
    OrderStatus["GROUP_BUY_FAILED"] = "6";
    OrderStatus["REFUNDING"] = "7";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var OrderType;
(function (OrderType) {
    OrderType["NORMAL"] = "1";
    OrderType["GROUP_BUY"] = "2";
})(OrderType || (exports.OrderType = OrderType = {}));
var DeliveryType;
(function (DeliveryType) {
    DeliveryType["DELIVERY"] = "1";
    DeliveryType["PICKUP"] = "2";
})(DeliveryType || (exports.DeliveryType = DeliveryType = {}));
exports.OrderStatusText = {
    [OrderStatus.PENDING_PAYMENT]: '待支付',
    [OrderStatus.PENDING_SHIPMENT]: '待发货',
    [OrderStatus.SHIPPING]: '配送中',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.GROUP_BUY_FAILED]: '团购失败已退款',
    [OrderStatus.REFUNDING]: '退款中',
};
exports.OrderTypeText = {
    [OrderType.NORMAL]: '普通订单',
    [OrderType.GROUP_BUY]: '团购订单',
};
exports.DeliveryTypeText = {
    [DeliveryType.DELIVERY]: '配送',
    [DeliveryType.PICKUP]: '自提',
};
//# sourceMappingURL=order-status.enum.js.map