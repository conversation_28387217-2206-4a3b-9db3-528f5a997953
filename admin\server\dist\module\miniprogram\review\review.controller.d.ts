import { ReviewService } from './review.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ReviewQueryDto } from './dto/review-query.dto';
import { ReviewReplyDto } from './dto/review-reply.dto';
import { ResultData } from '../../../common/utils/result';
export declare class ReviewController {
    private readonly reviewService;
    constructor(reviewService: ReviewService);
    create(createReviewDto: CreateReviewDto): Promise<ResultData>;
    findAll(queryDto: ReviewQueryDto): Promise<ResultData>;
    findOne(id: string): Promise<ResultData>;
    getProductReviewStats(productId: string): Promise<ResultData>;
    reply(id: string, replyDto: ReviewReplyDto, req: any): Promise<ResultData>;
    remove(id: string, req: any): Promise<ResultData>;
    like(id: string): Promise<ResultData>;
}
