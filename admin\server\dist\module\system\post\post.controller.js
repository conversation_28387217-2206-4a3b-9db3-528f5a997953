"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const post_service_1 = require("./post.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let PostController = class PostController {
    constructor(postService) {
        this.postService = postService;
    }
    create(createPostDto) {
        return this.postService.create(createPostDto);
    }
    findAll(query) {
        return this.postService.findAll(query);
    }
    findOne(id) {
        return this.postService.findOne(+id);
    }
    update(updatePostDto) {
        return this.postService.update(updatePostDto);
    }
    remove(ids) {
        const menuIds = ids.split(',').map((id) => id);
        return this.postService.remove(menuIds);
    }
    async export(res, body) {
        return this.postService.export(res, body);
    }
};
exports.PostController = PostController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '岗位管理-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreatePostDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:add'),
    (0, common_1.Post)('/'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreatePostDto]),
    __metadata("design:returntype", void 0)
], PostController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '岗位管理-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ListPostDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListPostDto]),
    __metadata("design:returntype", void 0)
], PostController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '岗位管理-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:query'),
    (0, common_1.Get)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PostController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '岗位管理-更新',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdatePostDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:edit'),
    (0, common_1.Put)('/'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdatePostDto]),
    __metadata("design:returntype", void 0)
], PostController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '岗位管理-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:remove'),
    (0, common_1.Delete)('/:ids'),
    __param(0, (0, common_1.Param)('ids')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PostController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出岗位管理xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:post:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListPostDto]),
    __metadata("design:returntype", Promise)
], PostController.prototype, "export", null);
exports.PostController = PostController = __decorate([
    (0, swagger_1.ApiTags)('岗位管理'),
    (0, common_1.Controller)('system/post'),
    __metadata("design:paramtypes", [post_service_1.PostService])
], PostController);
//# sourceMappingURL=post.controller.js.map