"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const export_1 = require("../../../common/utils/export");
const post_entity_1 = require("./entities/post.entity");
let PostService = class PostService {
    constructor(sysPostEntityRep) {
        this.sysPostEntityRep = sysPostEntityRep;
    }
    async create(createPostDto) {
        await this.sysPostEntityRep.save(createPostDto);
        return result_1.ResultData.ok();
    }
    async findAll(query) {
        const entity = this.sysPostEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.postName) {
            entity.andWhere(`entity.postName LIKE "%${query.postName}%"`);
        }
        if (query.postCode) {
            entity.andWhere(`entity.postCode LIKE "%${query.postCode}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOne(postId) {
        const res = await this.sysPostEntityRep.findOne({
            where: {
                postId: postId,
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(res);
    }
    async update(updatePostDto) {
        const res = await this.sysPostEntityRep.update({ postId: updatePostDto.postId }, updatePostDto);
        return result_1.ResultData.ok(res);
    }
    async remove(postIds) {
        const data = await this.sysPostEntityRep.update({ postId: (0, typeorm_2.In)(postIds) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAll(body);
        const options = {
            sheetName: '岗位数据',
            data: list.data.list,
            header: [
                { title: '岗位序号', dataIndex: 'postId' },
                { title: '岗位编码', dataIndex: 'postCode' },
                { title: '岗位名称', dataIndex: 'postName' },
                { title: '岗位排序', dataIndex: 'postSort' },
                { title: '状态', dataIndex: 'status' },
            ],
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.PostService = PostService;
exports.PostService = PostService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(post_entity_1.SysPostEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PostService);
//# sourceMappingURL=post.service.js.map