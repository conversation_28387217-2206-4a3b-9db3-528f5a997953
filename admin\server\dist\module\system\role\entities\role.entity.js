"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysRoleEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let SysRoleEntity = class SysRoleEntity extends base_1.BaseEntity {
};
exports.SysRoleEntity = SysRoleEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '角色ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'role_id', comment: '角色ID' }),
    __metadata("design:type", Number)
], SysRoleEntity.prototype, "roleId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'role_name', length: 30, comment: '角色名称' }),
    __metadata("design:type", String)
], SysRoleEntity.prototype, "roleName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'role_sort', default: 0, comment: '显示顺序' }),
    __metadata("design:type", Number)
], SysRoleEntity.prototype, "roleSort", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'role_key', length: 100, comment: '角色权限字符串' }),
    __metadata("design:type", String)
], SysRoleEntity.prototype, "roleKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'data_scope', length: 1, default: '1', comment: '数据范围' }),
    __metadata("design:type", String)
], SysRoleEntity.prototype, "dataScope", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', name: 'menu_check_strictly', default: false, comment: '菜单树选择项是否关联显示' }),
    __metadata("design:type", Boolean)
], SysRoleEntity.prototype, "menuCheckStrictly", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', name: 'dept_check_strictly', default: false, comment: '部门树选择项是否关联显示' }),
    __metadata("design:type", Boolean)
], SysRoleEntity.prototype, "deptCheckStrictly", void 0);
exports.SysRoleEntity = SysRoleEntity = __decorate([
    (0, typeorm_1.Entity)('sys_role', {
        comment: '角色信息表',
    })
], SysRoleEntity);
//# sourceMappingURL=role.entity.js.map