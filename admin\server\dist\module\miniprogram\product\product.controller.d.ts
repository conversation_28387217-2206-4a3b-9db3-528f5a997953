import { ProductService } from './product.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { CreateProductSpecDto, UpdateProductSpecDto } from './dto/product-spec.dto';
export declare class ProductController {
    private readonly productService;
    private readonly logger;
    constructor(productService: ProductService);
    create(createProductDto: CreateProductDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(queryDto: ProductQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    findEnabled(queryDto: ProductQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: number, userId?: number): Promise<import("../../../common/utils/result").ResultData>;
    update(id: number, updateProductDto: UpdateProductDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(id: number): Promise<import("../../../common/utils/result").ResultData>;
    updateStatus(body: {
        ids: number[];
        status: number;
    }): Promise<import("../../../common/utils/result").ResultData>;
    increaseSales(id: number, body: {
        count: number;
    }): Promise<import("../../../common/utils/result").ResultData>;
    addSpec(id: number, createSpecDto: CreateProductSpecDto): Promise<import("../../../common/utils/result").ResultData>;
    findSpecs(id: number): Promise<import("../../../common/utils/result").ResultData>;
    updateSpec(specId: number, updateSpecDto: UpdateProductSpecDto): Promise<import("../../../common/utils/result").ResultData>;
    removeSpec(specId: number): Promise<import("../../../common/utils/result").ResultData>;
    setDefaultSpec(specId: number): Promise<import("../../../common/utils/result").ResultData>;
}
