
  
    <template>
        <div class="app-container">
            <!-- 搜索区域 -->
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
              <el-form-item label="订单号" prop="orderId">
                <el-input
                  v-model="queryParams.orderId"
                  placeholder="请输入订单号"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="订单状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable style="width: 200px">
                  <el-option
                    v-for="dict in orderStatusOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="下单时间" style="width: 308px">
                <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="[new Date('2000-01-01 00:00:00'), new Date('2000-01-01 23:59:59')]"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <!-- 操作按钮区域 -->
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  plain
                  icon="Download"
                  @click="handleExport"
                  v-hasPermi="['order:export']"
                >导出</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <!-- 表格区域 -->
            <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="订单号" align="center" prop="orderId" width="180" />
              <el-table-column label="用户信息" align="center" width="120">
                <template #default="scope">
                  <div v-if="scope.row.user">
                    <div>{{ scope.row.user.nickname }}</div>
                    <div style="font-size: 12px; color: #999;">{{ scope.row.user.phone }}</div>
                  </div>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="订单类型" align="center" prop="orderType" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.orderType === '1' ? '' : 'success'" size="small">
                    {{ scope.row.orderType === '1' ? '普通订单' : '团购订单' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="订单金额" align="center" width="120">
                <template #default="scope">
                  <div>
                    <div>总额: ¥{{ scope.row.totalAmount }}</div>
                    <div style="font-size: 12px; color: #f56c6c;">实付: ¥{{ scope.row.finalAmount }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="订单状态" align="center" prop="status" width="180">
                <template #default="scope">
                  <el-tag 
                    :type="getOrderStatusDisplay(scope.row.status, scope.row.refundType).elTagType"
                    size="small"
                  >
                    {{ getOrderStatusDisplay(scope.row.status, scope.row.refundType).label }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="配送方式" align="center" prop="deliveryType" width="100">
                <template #default="scope">
                  {{ scope.row.deliveryType === '1' ? '配送' : '自提' }}
                </template>
              </el-table-column>
              <!-- <el-table-column label="支付方式" align="center" prop="paymentMethod" width="100">
                <template #default="scope">
                  <span v-if="scope.row.paymentMethod">
                    {{ getPaymentMethodText(scope.row.paymentMethod) }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column> -->
              <el-table-column label="下单时间" align="center" prop="createTime" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                  <el-button 
                    link 
                    type="primary" 
                    icon="View" 
                    @click="handleDetail(scope.row)"
                    v-hasPermi="['order:detail']"
                  >详情</el-button>
                  <el-button
                    link
                    type="primary"
                    icon="Truck"
                    @click="handleDeliver(scope.row)"
                    v-if="scope.row.status === '2'"
                    v-hasPermi="['order:deliver']"
                  >发货</el-button>
                  <el-button
                    link
                    type="primary"
                    icon="RefreshLeft"
                    @click="handleRefund(scope.row)"
                    v-if="scope.row.status === '2' || scope.row.status === '3'"
                    v-hasPermi="['order:refund']"
                  >退款</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页组件 -->
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />

            <!-- 订单详情弹窗 -->
            <el-dialog title="订单详情" v-model="dialogVisible" width="1000px" append-to-body>
              <order-detail v-if="dialogVisible" :order-id="selectedOrderId" @refresh="getList" />
            </el-dialog>
        </div>
    </template>
    
  
    <script setup name="Order">
    import { ref, reactive } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { parseTime } from '@/utils/ruoyi'
    import { listOrder, deliverOrder, refundOrder } from '@/api/order'
    import { getCurrentInstance } from 'vue'
    import orderDetail from './components/indexDialog.vue'

    const { proxy } = getCurrentInstance();

    // 遮罩层
    const loading = ref(false)
    // 选中数组
    const ids = ref([])
    // 非单个禁用
    const single = ref(true)
    // 非多个禁用
    const multiple = ref(true)
    // 显示搜索条件
    const showSearch = ref(true)
    // 总条数
    const total = ref(0)
    // 订单表格数据
    const orderList = ref([])
    // 弹出层标题
    const dialogVisible = ref(false)
    // 选中的订单ID
    const selectedOrderId = ref(null)
    // 日期范围
    const dateRange = ref([])

    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      orderId: undefined,
      status: undefined,
    })

    // 订单状态选项（根据后端Entity中的状态值）
    const orderStatusOptions = [
      { label: '待支付', value: '1', elTagType: 'warning' },
      { label: '待发货', value: '2', elTagType: 'primary' },
      { label: '配送中', value: '3', elTagType: 'info' },
      { label: '已完成', value: '4', elTagType: 'success' },
      { label: '已取消', value: '5', elTagType: 'danger' },
      { label: '团购失败已退款', value: '6', elTagType: 'danger' },
      { label: '退款中', value: '7', elTagType: 'warning' }
    ]

    /** 查询订单列表 */
    const getList = async () => {
      loading.value = true
      try {
        const { data } = await listOrder(queryParams)
        orderList.value = data.list
        total.value = data.total
      } catch (error) {
        console.error('获取订单列表失败:', error)
        ElMessage.error('获取订单列表失败')
      } finally {
        loading.value = false
      }
    }

    /** 搜索按钮操作 */
    const handleQuery = () => {
      queryParams.pageNum = 1
      getList()
    }

    /** 重置按钮操作 */
    const resetQuery = () => {
      dateRange.value = []
      queryParams.pageNum = 1
      proxy.resetForm('queryRef')
      handleQuery()
    }

    /** 多选框选中数据 */
    const handleSelectionChange = (selection) => {
      ids.value = selection.map(item => item.id)
      single.value = selection.length !== 1
      multiple.value = !selection.length
    }

    /** 查看订单详情 */
    const handleDetail = (row) => {
      selectedOrderId.value = row.orderId
      dialogVisible.value = true
    }

    /** 订单发货操作 */
    const handleDeliver = async (row) => {
      try {
        await ElMessageBox.confirm('确认要发货该订单吗？')
        await deliverOrder({ orderId: row.orderId })
        ElMessage.success('发货成功')
        getList()
      } catch (error) {
        console.error('发货失败:', error)
      }
    }

    /** 订单退款操作 */
    const handleRefund = async (row) => {
      try {
        await ElMessageBox.confirm('确认要对该订单进行退款吗？')
        await refundOrder({ orderId: row.orderId })
        ElMessage.success('退款成功')
        getList()
      } catch (error) {
        console.error('退款失败:', error)
      }
    }

    /** 导出按钮操作 */
    const handleExport = () => {
      proxy.download('miniprogram/order/export', {
        ...queryParams,
        ...proxy.addDateRange(queryParams, dateRange)
      }, `order_${new Date().getTime()}.xlsx`)
    }

    /** 获取支付方式文本 */
    const getPaymentMethodText = (paymentMethod) => {
      const paymentMethodMap = {
        '1': '微信支付',
        '2': '余额支付',
        '3': '模拟支付'
      }
      return paymentMethodMap[paymentMethod] || paymentMethod
    }

    /** 获取订单状态显示信息 */
    const getOrderStatusDisplay = (status, refundType) => {
      // 如果是状态5（已取消）且有退款类型，显示详细信息
      if (status === '5' && refundType) {
        const refundTypeMap = {
          '1': { label: '已取消(用户退款-待发货)', elTagType: 'warning' },
          '2': { label: '已取消(用户退款-已完成)', elTagType: 'warning' },
          '3': { label: '已取消(管理员退款)', elTagType: 'danger' },
          '4': { label: '已取消(系统退款)', elTagType: 'info' }
        }
        return refundTypeMap[refundType] || { label: '已取消', elTagType: 'danger' }
      }
      
      // 其他状态使用原有的状态选项
      const statusOption = orderStatusOptions.find(option => option.value === status)
      return statusOption || { label: '未知状态', elTagType: 'info' }
    }

    // 初始化数据
    getList()
    </script>
    
  
  