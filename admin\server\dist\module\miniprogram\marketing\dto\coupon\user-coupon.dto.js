"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvailableCouponQueryDto = exports.UseCouponDto = exports.ReceiveCouponDto = exports.UserCouponQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class UserCouponQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.UserCouponQueryDto = UserCouponQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UserCouponQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券状态 0未使用1已使用2已过期', example: '0', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '状态必须是字符串' }),
    __metadata("design:type", String)
], UserCouponQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UserCouponQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UserCouponQueryDto.prototype, "pageSize", void 0);
class ReceiveCouponDto {
}
exports.ReceiveCouponDto = ReceiveCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ReceiveCouponDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '优惠券ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '优惠券ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ReceiveCouponDto.prototype, "couponId", void 0);
class UseCouponDto {
}
exports.UseCouponDto = UseCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UseCouponDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户优惠券ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户优惠券ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '用户优惠券ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UseCouponDto.prototype, "userCouponId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORDER2023112800001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单ID不能为空' }),
    (0, class_validator_1.IsString)({ message: '订单ID必须是字符串' }),
    __metadata("design:type", String)
], UseCouponDto.prototype, "orderId", void 0);
class AvailableCouponQueryDto {
}
exports.AvailableCouponQueryDto = AvailableCouponQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], AvailableCouponQueryDto.prototype, "userId", void 0);
//# sourceMappingURL=user-coupon.dto.js.map