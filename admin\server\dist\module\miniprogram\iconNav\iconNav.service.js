"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var IconNavService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IconNavService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const iconNav_entity_1 = require("./entities/iconNav.entity");
const upload_service_1 = require("../../upload/upload.service");
let IconNavService = IconNavService_1 = class IconNavService {
    constructor(iconNavRepository, uploadService) {
        this.iconNavRepository = iconNavRepository;
        this.uploadService = uploadService;
        this.logger = new common_1.Logger(IconNavService_1.name);
    }
    async create(createIconNavDto, userId) {
        this.logger.log(`创建金刚区: ${JSON.stringify(createIconNavDto)}`);
        const iconNav = this.iconNavRepository.create({
            ...createIconNavDto,
            createBy: userId,
            updateBy: userId,
        });
        return await this.iconNavRepository.save(iconNav);
    }
    async findAll(queryParams) {
        this.logger.log(`查询金刚区列表: ${JSON.stringify(queryParams)}`);
        const { pageNum, pageSize, name, imageUrl } = queryParams;
        const skip = (pageNum - 1) * pageSize;
        const whereConditions = {};
        if (name) {
            whereConditions.name = (0, typeorm_2.Like)(`%${name}%`);
        }
        if (imageUrl) {
            whereConditions.imageUrl = (0, typeorm_2.Like)(`%${imageUrl}%`);
        }
        const [items, count] = await this.iconNavRepository.findAndCount({
            where: whereConditions,
            order: { sortOrder: 'ASC', createTime: 'DESC' },
            skip,
            take: pageSize,
        });
        return {
            rows: items,
            total: count,
            pageNum: Number(pageNum),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(count / pageSize),
        };
    }
    async findOne(iconId) {
        this.logger.log(`查询金刚区详情: ${iconId}`);
        return await this.iconNavRepository.findOne({ where: { iconId } });
    }
    async update(iconId, updateIconNavDto, userId) {
        this.logger.log(`更新金刚区: ${iconId}, ${JSON.stringify(updateIconNavDto)}`);
        const iconNav = await this.iconNavRepository.findOne({ where: { iconId } });
        if (!iconNav) {
            this.logger.warn(`金刚区不存在: ${iconId}`);
            return null;
        }
        if (updateIconNavDto.imageUrl && updateIconNavDto.imageUrl !== iconNav.imageUrl) {
            this.logger.log(`金刚区图片已更新，删除旧图片: ${iconNav.imageUrl}`);
            const deleteResult = await this.uploadService.deleteFile(iconNav.imageUrl);
            this.logger.log(`旧图片删除结果: ${deleteResult ? '成功' : '失败'}`);
        }
        Object.assign(iconNav, updateIconNavDto, { updateBy: userId });
        return await this.iconNavRepository.save(iconNav);
    }
    async remove(iconId, userId) {
        this.logger.log(`删除金刚区: ${iconId}`);
        const iconNav = await this.iconNavRepository.findOne({ where: { iconId } });
        if (!iconNav) {
            this.logger.warn(`金刚区不存在: ${iconId}`);
            return false;
        }
        if (iconNav.imageUrl) {
            this.logger.log(`删除金刚区图片: ${iconNav.imageUrl}`);
            const deleteResult = await this.uploadService.deleteFile(iconNav.imageUrl);
            this.logger.log(`图片删除结果: ${deleteResult ? '成功' : '失败'}`);
        }
        await this.iconNavRepository.delete(iconId);
        this.logger.log(`金刚区数据已硬删除: ${iconId}`);
        return true;
    }
    async getMiniIconNavs() {
        this.logger.log('获取小程序端金刚区列表');
        return await this.iconNavRepository.find({
            order: { sortOrder: 'ASC' },
        });
    }
};
exports.IconNavService = IconNavService;
exports.IconNavService = IconNavService = IconNavService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(iconNav_entity_1.IconNavEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        upload_service_1.UploadService])
], IconNavService);
//# sourceMappingURL=iconNav.service.js.map