import { NoticeService } from './notice.service';
import { CreateNoticeDto, UpdateNoticeDto, ListNoticeDto } from './dto/index';
export declare class NoticeController {
    private readonly noticeService;
    constructor(noticeService: NoticeService);
    create(createConfigDto: CreateNoticeDto, req: any): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListNoticeDto): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updateNoticeDto: UpdateNoticeDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(ids: string): Promise<import("../../../common/utils/result").ResultData>;
}
