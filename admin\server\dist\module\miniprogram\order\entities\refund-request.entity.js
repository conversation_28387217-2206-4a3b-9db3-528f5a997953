"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundRequestEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const order_entity_1 = require("./order.entity");
const user_entity_1 = require("../../user/entities/user.entity");
let RefundRequestEntity = class RefundRequestEntity extends base_1.BaseEntity {
};
exports.RefundRequestEntity = RefundRequestEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '申请ID主键' }),
    __metadata("design:type", Number)
], RefundRequestEntity.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'order_id', length: 32, comment: '订单ID外键' }),
    __metadata("design:type", String)
], RefundRequestEntity.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID外键' }),
    __metadata("design:type", Number)
], RefundRequestEntity.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'refund_reason', length: 500, comment: '退款原因' }),
    __metadata("design:type", String)
], RefundRequestEntity.prototype, "refundReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请退款金额' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'refund_amount',
        precision: 10,
        scale: 2,
        comment: '申请退款金额',
    }),
    __metadata("design:type", Number)
], RefundRequestEntity.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请状态', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'status',
        length: 1,
        default: '1',
        comment: '申请状态：1待审核2已退款3已拒绝',
    }),
    __metadata("design:type", String)
], RefundRequestEntity.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '管理员处理意见', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'admin_comment',
        length: 500,
        nullable: true,
        comment: '管理员处理意见',
    }),
    __metadata("design:type", String)
], RefundRequestEntity.prototype, "adminComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '申请时间' }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'request_time',
        default: () => 'CURRENT_TIMESTAMP',
        comment: '申请时间',
    }),
    __metadata("design:type", Date)
], RefundRequestEntity.prototype, "requestTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '处理完成时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'process_time',
        nullable: true,
        comment: '处理完成时间',
    }),
    __metadata("design:type", Date)
], RefundRequestEntity.prototype, "processTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => order_entity_1.OrderEntity, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'order_id', referencedColumnName: 'orderId' }),
    __metadata("design:type", order_entity_1.OrderEntity)
], RefundRequestEntity.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id', referencedColumnName: 'userId' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], RefundRequestEntity.prototype, "user", void 0);
exports.RefundRequestEntity = RefundRequestEntity = __decorate([
    (0, typeorm_1.Entity)('refund_requests', { comment: '退款申请表' }),
    (0, typeorm_1.Index)('idx_order_id', ['orderId']),
    (0, typeorm_1.Index)('idx_user_id', ['userId']),
    (0, typeorm_1.Index)('idx_status', ['status']),
    (0, typeorm_1.Index)('idx_request_time', ['requestTime'])
], RefundRequestEntity);
//# sourceMappingURL=refund-request.entity.js.map