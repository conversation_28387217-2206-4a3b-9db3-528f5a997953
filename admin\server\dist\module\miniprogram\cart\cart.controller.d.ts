import { CartService } from './cart.service';
import { AddToCartDto } from './dto/add-to-cart.dto';
import { UpdateCartDto } from './dto/update-cart.dto';
import { CartQueryDto } from './dto/cart-query.dto';
export declare class CartController {
    private readonly cartService;
    private readonly logger;
    constructor(cartService: CartService);
    addToCart(userId: number, addToCartDto: AddToCartDto): Promise<import("../../../common/utils/result").ResultData>;
    getCartItems(userId: number): Promise<import("../../../common/utils/result").ResultData>;
    updateCartItem(userId: number, cartId: number, updateCartDto: UpdateCartDto): Promise<import("../../../common/utils/result").ResultData>;
    removeFromCart(userId: number, cartId: number): Promise<import("../../../common/utils/result").ResultData>;
    clearCart(userId: number): Promise<import("../../../common/utils/result").ResultData>;
    getCartCount(userId: number): Promise<import("../../../common/utils/result").ResultData>;
    findAll(queryDto: CartQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    batchRemove(body: {
        cartIds: number[];
    }): Promise<import("../../../common/utils/result").ResultData>;
}
