import { Repository } from 'typeorm';
import { CategoryEntity } from './entities/category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryQueryDto } from './dto/category-query.dto';
import { ResultData } from '../../../common/utils/result';
export declare class CategoryService {
    private readonly categoryRepository;
    private readonly logger;
    constructor(categoryRepository: Repository<CategoryEntity>);
    create(createCategoryDto: CreateCategoryDto): Promise<ResultData>;
    findAll(queryDto: CategoryQueryDto): Promise<ResultData>;
    findOne(id: number): Promise<ResultData>;
    findEnabled(): Promise<ResultData>;
    update(id: number, updateCategoryDto: UpdateCategoryDto): Promise<ResultData>;
    remove(id: number): Promise<ResultData>;
    updateStatus(ids: number[], status: number): Promise<ResultData>;
}
