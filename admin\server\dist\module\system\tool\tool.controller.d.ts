import { ToolService } from './tool.service';
import { TableName, GenDbTableList, GenTableList, GenTableUpdate } from './dto/create-genTable-dto';
import { Response } from 'express';
import { UserDto } from 'src/module/system/user/user.decorator';
export declare class ToolController {
    private readonly toolService;
    constructor(toolService: ToolService);
    findAll(query: GenTableList): Promise<import("../../../common/utils/result").ResultData>;
    genDbList(query: GenDbTableList): Promise<import("../../../common/utils/result").ResultData>;
    genImportTable(table: TableName, user: UserDto): Promise<import("../../../common/utils/result").ResultData>;
    synchDb(tableName: string): Promise<import("../../../common/utils/result").ResultData>;
    gen(id: string): Promise<import("../../../common/utils/result").ResultData>;
    genUpdate(genTableUpdate: GenTableUpdate): Promise<import("../../../common/utils/result").ResultData>;
    remove(id: string): Promise<import("../../../common/utils/result").ResultData>;
    batchGenCode(tables: TableName, res: Response): Promise<void>;
    preview(id: string): Promise<import("../../../common/utils/result").ResultData>;
}
