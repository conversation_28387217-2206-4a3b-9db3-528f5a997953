import { Response } from 'express';
import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { MonitorLoginlogEntity } from './entities/loginlog.entity';
import { CreateLoginlogDto, ListLoginlogDto } from './dto/index';
export declare class LoginlogService {
    private readonly monitorLoginlogEntityRep;
    constructor(monitorLoginlogEntityRep: Repository<MonitorLoginlogEntity>);
    create(createLoginlogDto: CreateLoginlogDto): Promise<CreateLoginlogDto & MonitorLoginlogEntity>;
    findAll(query: ListLoginlogDto): Promise<ResultData>;
    remove(ids: string[]): Promise<ResultData>;
    removeAll(): Promise<ResultData>;
    export(res: Response, body: ListLoginlogDto): Promise<void>;
}
