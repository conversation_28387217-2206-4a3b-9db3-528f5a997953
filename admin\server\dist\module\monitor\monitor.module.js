"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitorModule = void 0;
const common_1 = require("@nestjs/common");
const job_module_1 = require("./job/job.module");
const server_module_1 = require("./server/server.module");
const cache_module_1 = require("./cache/cache.module");
const loginlog_module_1 = require("./loginlog/loginlog.module");
const online_module_1 = require("./online/online.module");
const operlog_module_1 = require("./operlog/operlog.module");
let MonitorModule = class MonitorModule {
};
exports.MonitorModule = MonitorModule;
exports.MonitorModule = MonitorModule = __decorate([
    (0, common_1.Module)({
        imports: [job_module_1.JobModule, server_module_1.ServerModule, cache_module_1.CacheModule, loginlog_module_1.LoginlogModule, online_module_1.OnlineModule, operlog_module_1.OperlogModule],
        exports: [job_module_1.JobModule],
    })
], MonitorModule);
//# sourceMappingURL=monitor.module.js.map