{"version": 3, "file": "create-product.dto.js", "sourceRoot": "", "sources": ["../../../../../src/module/miniprogram/product/dto/create-product.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAwH;AACxH,6CAA8C;AAC9C,yDAAyC;AACzC,yDAA0D;AAK1D,MAAa,gBAAgB;IAA7B;QA0CE,kBAAa,GAAY,CAAC,CAAC;IAe7B,CAAC;CAAA;AAzDD,4CAyDC;AArDC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACrD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACnC,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;8CACvB;AAOb;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IACpD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACnC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;+CACnB;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACzE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;qDACf;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;iDACnB;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,+DAA+D,EAAE,CAAC;IACzI,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;gDACzB;AAMhB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAChD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACnC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;oDACb;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvE,IAAA,4BAAU,GAAE;IACZ,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;gDACvB;AAKhB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtF,IAAA,4BAAU,GAAE;IACZ,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;uDACd;AAO3B;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,uCAAoB,CAAC,EAAE,CAAC;IACrF,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACjC,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,uCAAoB,CAAC;;+CACF;AAO/B;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,uCAAoB,CAAC,EAAE,CAAC;IAC3F,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACjC,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,uCAAoB,CAAC;;kDACC"}