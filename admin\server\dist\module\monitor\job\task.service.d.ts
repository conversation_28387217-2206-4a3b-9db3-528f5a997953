import { OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { JobLogService } from './job-log.service';
export declare class TaskService implements OnModuleInit {
    private moduleRef;
    private jobLogService;
    private readonly logger;
    private readonly taskMap;
    private serviceInstances;
    constructor(moduleRef: ModuleRef, jobLogService: JobLogService);
    onModuleInit(): void;
    private initializeTasks;
    getTasks(): string[];
    executeTask(invokeTarget: string, jobName?: string, jobGroup?: string): Promise<boolean>;
    private parseParams;
    ryNoParams(): Promise<void>;
    ryParams(param1: string, param2: number, param3: boolean): Promise<void>;
    clearTemp(): Promise<void>;
    monitorSystem(): Promise<void>;
    backupDatabase(): Promise<void>;
}
