"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const review_service_1 = require("./review.service");
const create_review_dto_1 = require("./dto/create-review.dto");
const review_query_dto_1 = require("./dto/review-query.dto");
const review_reply_dto_1 = require("./dto/review-reply.dto");
const user_decorator_1 = require("../../system/user/user.decorator");
let ReviewController = class ReviewController {
    constructor(reviewService) {
        this.reviewService = reviewService;
    }
    async create(createReviewDto) {
        return this.reviewService.create(createReviewDto);
    }
    async findAll(queryDto) {
        return this.reviewService.findAll(queryDto);
    }
    async findOne(id) {
        return this.reviewService.findOne(+id);
    }
    async getProductReviewStats(productId) {
        return this.reviewService.getProductReviewStats(+productId);
    }
    async reply(id, replyDto, req) {
        const adminId = req.user?.userId || 'admin';
        return this.reviewService.reply(+id, replyDto, adminId);
    }
    async remove(id, req) {
        const userId = req.user?.userId || 'admin';
        return this.reviewService.remove(+id, userId);
    }
    async like(id) {
        return this.reviewService.like(+id);
    }
};
exports.ReviewController = ReviewController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建商品评价' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_review_dto_1.CreateReviewDto]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '查询评价列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [review_query_dto_1.ReviewQueryDto]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '查询评价详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('product/:productId/stats'),
    (0, swagger_1.ApiOperation)({ summary: '查询商品评价统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "getProductReviewStats", null);
__decorate([
    (0, common_1.Put)(':id/reply'),
    (0, swagger_1.ApiOperation)({ summary: '商家回复评价' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '回复成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, review_reply_dto_1.ReviewReplyDto, Object]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "reply", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除评价' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/like'),
    (0, swagger_1.ApiOperation)({ summary: '点赞评价' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '点赞成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReviewController.prototype, "like", null);
exports.ReviewController = ReviewController = __decorate([
    (0, swagger_1.ApiTags)('商品评价'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, common_1.Controller)('miniprogram/review'),
    __metadata("design:paramtypes", [review_service_1.ReviewService])
], ReviewController);
//# sourceMappingURL=review.controller.js.map