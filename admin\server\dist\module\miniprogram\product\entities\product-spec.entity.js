"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSpecEntity = void 0;
const typeorm_1 = require("typeorm");
const product_entity_1 = require("./product.entity");
let ProductSpecEntity = class ProductSpecEntity {
};
exports.ProductSpecEntity = ProductSpecEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'spec_id', comment: '规格ID' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "specId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'product_id', comment: '商品ID' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, comment: '规格名称' }),
    __metadata("design:type", String)
], ProductSpecEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, comment: '规格价格' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, comment: '规格值(如重量、颜色等)' }),
    __metadata("design:type", String)
], ProductSpecEntity.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0, comment: '库存数量' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "stock", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 200, nullable: true, comment: '规格图片' }),
    __metadata("design:type", String)
], ProductSpecEntity.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'tinyint', default: 1, comment: '状态：1启用，0禁用' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'tinyint', default: 0, comment: '是否默认规格：1是，0否' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'sales_count', default: 0, comment: '销售数量' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "salesCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, name: 'group_buy_price', nullable: true, comment: '团购价格' }),
    __metadata("design:type", Number)
], ProductSpecEntity.prototype, "groupBuyPrice", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time', comment: '创建时间' }),
    __metadata("design:type", Date)
], ProductSpecEntity.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time', comment: '更新时间' }),
    __metadata("design:type", Date)
], ProductSpecEntity.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.ProductEntity, (product) => product.specs, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id' }),
    __metadata("design:type", product_entity_1.ProductEntity)
], ProductSpecEntity.prototype, "product", void 0);
exports.ProductSpecEntity = ProductSpecEntity = __decorate([
    (0, typeorm_1.Entity)('product_specs', { comment: '商品规格信息表' })
], ProductSpecEntity);
//# sourceMappingURL=product-spec.entity.js.map