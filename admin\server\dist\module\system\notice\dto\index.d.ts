import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare enum TypeEnum {
    Instruct = "1",
    Notice = "2"
}
export declare class CreateNoticeDto {
    noticeTitle: string;
    noticeType: string;
    remark?: string;
    status?: string;
    noticeContent?: string;
}
export declare class UpdateNoticeDto extends CreateNoticeDto {
    noticeId: number;
}
export declare class ListNoticeDto extends PagingDto {
    noticeTitle?: string;
    noticeType?: string;
    createBy?: string;
}
