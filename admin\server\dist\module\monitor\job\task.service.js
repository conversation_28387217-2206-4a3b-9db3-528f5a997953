"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TaskService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const task_decorator_1 = require("../../../common/decorators/task.decorator");
const job_log_service_1 = require("./job-log.service");
let TaskService = TaskService_1 = class TaskService {
    constructor(moduleRef, jobLogService) {
        this.moduleRef = moduleRef;
        this.jobLogService = jobLogService;
        this.logger = new common_1.Logger(TaskService_1.name);
        this.taskMap = new Map();
        this.serviceInstances = new Map();
    }
    onModuleInit() {
        this.initializeTasks();
    }
    async initializeTasks() {
        const tasks = task_decorator_1.TaskRegistry.getInstance().getTasks();
        for (const { classOrigin, methodName, metadata } of tasks) {
            try {
                let serviceInstance = this.serviceInstances.get(classOrigin.name);
                if (!serviceInstance) {
                    serviceInstance = await this.moduleRef.get(classOrigin);
                    this.serviceInstances.set(classOrigin.name, serviceInstance);
                }
                const method = serviceInstance[methodName].bind(serviceInstance);
                this.taskMap.set(metadata.name, method);
                this.logger.log(`注册任务: ${metadata.name}`);
            }
            catch (error) {
                this.logger.error(`注册任务失败 ${metadata.name}: ${error.message}`);
            }
        }
    }
    getTasks() {
        return Array.from(this.taskMap.keys());
    }
    async executeTask(invokeTarget, jobName, jobGroup) {
        const startTime = new Date();
        let status = '0';
        let jobMessage = '执行成功';
        let exceptionInfo = '';
        try {
            const regex = /^([^(]+)(?:\((.*)\))?$/;
            const match = invokeTarget.match(regex);
            if (!match) {
                throw new Error('调用目标格式错误');
            }
            const [, methodName, paramsStr] = match;
            const params = paramsStr ? this.parseParams(paramsStr) : [];
            const taskFn = this.taskMap.get(methodName);
            if (!taskFn) {
                throw new Error(`任务 ${methodName} 不存在`);
            }
            await taskFn(...params);
            return true;
        }
        catch (error) {
            status = '1';
            jobMessage = '执行失败';
            exceptionInfo = error.message;
            this.logger.error(`执行任务失败: ${error.message}`);
            return false;
        }
        finally {
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();
            await this.jobLogService.addJobLog({
                jobName: jobName || '未知任务',
                jobGroup: jobGroup || 'DEFAULT',
                invokeTarget,
                status,
                jobMessage: `${jobMessage}，耗时 ${duration}ms`,
                exceptionInfo,
                createTime: startTime,
            });
        }
    }
    parseParams(paramsStr) {
        if (!paramsStr.trim()) {
            return [];
        }
        try {
            const normalizedStr = paramsStr
                .replace(/'/g, '"')
                .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');
            return Function(`return [${normalizedStr}]`)();
        }
        catch (error) {
            this.logger.error(`解析参数失败: ${error.message}`);
            return [];
        }
    }
    async ryNoParams() {
        this.logger.log('执行无参示例任务');
    }
    async ryParams(param1, param2, param3) {
        this.logger.log(`执行有参示例任务，参数：${JSON.stringify({ param1, param2, param3 })}`);
    }
    async clearTemp() {
        this.logger.log('执行清理临时文件任务');
    }
    async monitorSystem() {
        this.logger.log('执行系统状态监控任务');
    }
    async backupDatabase() {
        this.logger.log('执行数据库备份任务');
    }
};
exports.TaskService = TaskService;
__decorate([
    (0, task_decorator_1.Task)({
        name: 'task.noParams',
        description: '无参示例任务',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "ryNoParams", null);
__decorate([
    (0, task_decorator_1.Task)({
        name: 'task.params',
        description: '有参示例任务',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Boolean]),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "ryParams", null);
__decorate([
    (0, task_decorator_1.Task)({
        name: 'task.clearTemp',
        description: '清理临时文件',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "clearTemp", null);
__decorate([
    (0, task_decorator_1.Task)({
        name: 'task.monitorSystem',
        description: '系统状态监控',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "monitorSystem", null);
__decorate([
    (0, task_decorator_1.Task)({
        name: 'task.backupDatabase',
        description: '数据库备份',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "backupDatabase", null);
exports.TaskService = TaskService = TaskService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.ModuleRef,
        job_log_service_1.JobLogService])
], TaskService);
//# sourceMappingURL=task.service.js.map