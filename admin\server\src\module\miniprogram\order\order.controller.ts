import { Controller, Get, Post, Put, Body, Param, Query, UseGuards, ParseIntPipe, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatisticsQueryDto } from './dto/order-statistics.dto';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { NotRequireAuth } from '../../system/user/user.decorator';
import { User } from '../../system/user/user.decorator';
import { ResultData } from '../../../common/utils/result';
import { OrderStatus } from './enum/order-status.enum';
import { CreateRefundRequestDto, ConfirmRefundPickupDto } from './dto/refund-request.dto';

@ApiTags('订单管理')
@Controller('miniprogram/order')
@UseGuards(JwtAuthGuard)
export class OrderController {
  private readonly logger = new Logger(OrderController.name);

  constructor(private readonly orderService: OrderService) {}

  // ==================== 小程序端接口 ====================

  @Post(':userId/create')
  @NotRequireAuth()
  @ApiOperation({ summary: '创建订单（小程序端）' })
  @ApiResponse({ status: 200, description: '订单创建成功' })
  async createOrder(@Param('userId', ParseIntPipe) userId: number, @Body() createOrderDto: CreateOrderDto) {
    this.logger.log(`创建订单请求: UserId=${userId}, Data=${JSON.stringify(createOrderDto)}`);
    try {
      return await this.orderService.createOrder(userId, createOrderDto);
    } catch (error) {
      this.logger.error(`创建订单失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `创建订单失败: ${error.message}`);
    }
  }

  @Get(':userId/stats')
  @NotRequireAuth()
  @ApiOperation({ summary: '获取用户订单统计（小程序端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getUserOrderStats(@Param('userId', ParseIntPipe) userId: number) {
    this.logger.log(`获取用户订单统计请求: UserId=${userId}`);
    return await this.orderService.getUserOrderStats(userId);
  }

  @Get('list')
  @NotRequireAuth()
  @ApiOperation({ summary: '获取用户订单列表（小程序端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getUserOrders(@Query() queryDto: OrderQueryDto) {
    this.logger.log(`获取用户订单列表请求: Query=${JSON.stringify(queryDto)}`);
    return await this.orderService.getUserOrders(queryDto);
  }

  @Get(':userId/detail/:orderId')
  @NotRequireAuth()
  @ApiOperation({ summary: '获取订单详情（小程序端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getOrderDetail(@Param('userId', ParseIntPipe) userId: number, @Param('orderId') orderId: string) {
    this.logger.log(`获取订单详情请求: UserId=${userId}, OrderId=${orderId}`);
    return await this.orderService.getOrderDetail(orderId, userId);
  }

  @Put(':userId/cancel/:orderId')
  @NotRequireAuth()
  @ApiOperation({ summary: '取消订单（小程序端）' })
  @ApiResponse({ status: 200, description: '取消成功' })
  async cancelOrder(@Param('userId', ParseIntPipe) userId: number, @Param('orderId') orderId: string, @Body() body: { reason?: string }) {
    this.logger.log(`取消订单请求: UserId=${userId}, OrderId=${orderId}, Reason=${body.reason}`);
    return await this.orderService.cancelOrder(orderId, userId, body.reason);
  }

  @Put('status')
  @NotRequireAuth()
  @ApiOperation({ summary: '更新订单状态（小程序端）' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateOrderStatus(@Body() updateOrderDto: UpdateOrderDto) {
    this.logger.log(`更新订单状态请求: UserId=${updateOrderDto.userId}, OrderId=${updateOrderDto.orderId}, Data=${JSON.stringify(updateOrderDto)}`);
    try {
      return await this.orderService.updateOrderStatus(updateOrderDto.orderId, updateOrderDto, updateOrderDto.userId);
    } catch (error) {
      this.logger.error(`更新订单状态失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `更新订单状态失败: ${error.message}`);
    }
  }

  // ==================== 管理端接口 ====================

  @Get('admin/list')
  @ApiBearerAuth()
  @ApiOperation({ summary: '分页查询订单列表（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findAll(@Query() queryDto: OrderQueryDto) {
    this.logger.log(`管理员查询订单列表请求: ${JSON.stringify(queryDto)}`);
    return await this.orderService.findAll(queryDto);
  }

  @Get('admin/self-pickup/list')
  @NotRequireAuth()
  @ApiOperation({ summary: '分页查询自提订单列表（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findSelfPickupOrders(@Query() queryDto: OrderQueryDto) {
    this.logger.log(`管理员查询自提订单列表请求: ${JSON.stringify(queryDto)}`);
    return await this.orderService.findSelfPickupOrders(queryDto);
  }

  @Get('admin/detail/:orderId')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取订单详情（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getAdminOrderDetail(@Param('orderId') orderId: string) {
    this.logger.log(`管理员获取订单详情请求: OrderId=${orderId}`);
    return await this.orderService.getOrderDetail(orderId);
  }

  @Post('complete')
  @NotRequireAuth()
  @ApiOperation({ summary: '完成订单（小程序端）' })
  @ApiResponse({ status: 200, description: '订单完成成功' })
  async completeOrder(@Body() body: { orderId: string; userId: number }) {
    const { orderId, userId } = body;
    this.logger.log(`用户完成订单请求: OrderId=${orderId}, UserId=${userId}`);
    try {
      const updateOrderDto = new UpdateOrderDto();
      updateOrderDto.status = OrderStatus.COMPLETED;
      return await this.orderService.updateOrderStatus(orderId, updateOrderDto, userId);
    } catch (error) {
      this.logger.error(`完成订单失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `完成订单失败: ${error.message}`);
    }
  }

  @Put('admin/status/:orderId')
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新订单状态（管理端）' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateAdminOrderStatus(@Param('orderId') orderId: string, @Body() updateOrderDto: UpdateOrderDto) {
    this.logger.log(`管理员更新订单状态请求: OrderId=${orderId}, Data=${JSON.stringify(updateOrderDto)}`);
    return await this.orderService.updateOrderStatus(orderId, updateOrderDto);
  }

  @Get('admin/stats')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取订单统计（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getOrderStats() {
    this.logger.log(`获取订单统计请求`);
    return await this.orderService.getOrderStats();
  }

  @Get('admin/dashboard/statistics')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取仪表板详细统计数据（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getDashboardStatistics(@Query() queryDto: OrderStatisticsQueryDto) {
    this.logger.log(`获取仪表板统计数据请求: ${JSON.stringify(queryDto)}`);
    return await this.orderService.getDetailedOrderStatistics(queryDto);
  }

  @Get('admin/debug/tables')
  @NotRequireAuth()
  @ApiOperation({ summary: '调试：检查数据库表和数据（调试用）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async debugTables() {
    this.logger.log(`调试：检查数据库表和数据`);
    try {
      // 检查订单表是否存在和有数据
      const tablesResult = await this.orderService.debugCheckTables();
      return ResultData.ok(tablesResult);
    } catch (error) {
      this.logger.error(`调试检查失败: ${error.message}`, error.stack);
      return ResultData.fail(500, `调试检查失败: ${error.message}`);
    }
  }

  @Post('deliver')
  @ApiBearerAuth()
  @ApiOperation({ summary: '订单发货（管理端）' })
  @ApiResponse({ status: 200, description: '发货成功' })
  async deliverOrder(@Body() body: { orderId: string }) {
    this.logger.log(`订单发货请求: OrderId=${body.orderId}`);
    try {
      return await this.orderService.deliverOrder(body.orderId);
    } catch (error) {
      this.logger.error(`订单发货失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `订单发货失败: ${error.message}`);
    }
  }

  @Post('refund')
  @ApiBearerAuth()
  @ApiOperation({ summary: '订单退款（管理端）' })
  @ApiResponse({ status: 200, description: '退款成功' })
  async refundOrder(@Body() body: { orderId: string; refundReason?: string }) {
    this.logger.log(`订单退款请求: OrderId=${body.orderId}`);
    try {
      return await this.orderService.refundOrder(body.orderId, body.refundReason);
    } catch (error) {
      this.logger.error(`订单退款失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `订单退款失败: ${error.message}`);
    }
  }

  @Get('export')
  @ApiBearerAuth()
  @ApiOperation({ summary: '导出订单数据（管理端）' })
  @ApiResponse({ status: 200, description: '导出成功' })
  async exportOrders(@Query() queryDto: OrderQueryDto) {
    this.logger.log(`导出订单数据请求: ${JSON.stringify(queryDto)}`);
    return await this.orderService.exportOrders(queryDto);
  }

  @Post('batch/cancel')
  @ApiBearerAuth()
  @ApiOperation({ summary: '批量取消订单（管理端）' })
  @ApiResponse({ status: 200, description: '取消成功' })
  async batchCancelOrders(@Body() body: { orderIds: string[] }) {
    this.logger.log(`批量取消订单请求: OrderIds=${body.orderIds.join(',')}`);
    try {
      return await this.orderService.batchCancelOrders(body.orderIds);
    } catch (error) {
      this.logger.error(`批量取消订单失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        return ResultData.fail(error.getStatus(), error.message);
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `批量取消订单失败: ${error.message}`);
    }
  }

  @Get('detail/:orderId')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取订单详情（管理端）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getOrderDetailForAdmin(@Param('orderId') orderId: string) {
    this.logger.log(`管理端获取订单详情请求: OrderId=${orderId}`);
    return await this.orderService.getOrderDetail(orderId);
  }

  // ==================== 调试接口 ====================

  /**
   * 检查团购活动状态（调试用）
   */
  @Get('debug/check-group-buy/:activityId')
  @NotRequireAuth()
  @ApiOperation({ summary: '检查团购活动状态（调试用）' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async checkGroupBuyStatus(@Param('activityId', ParseIntPipe) activityId: number) {
    this.logger.log(`调试检查团购活动状态: ActivityId=${activityId}`);
    try {
      const result = await this.orderService.checkGroupBuyStatus(activityId);
      return result;
    } catch (error) {
      this.logger.error(`检查团购活动状态失败: ${error.message}`, error.stack);
      return ResultData.fail(500, `检查失败: ${error.message}`);
    }
  }

  // ==================== 退款申请相关接口 ====================

  /**
   * 提交退款申请（小程序端）
   */
  @Post('refund-request')
  @NotRequireAuth()
  @ApiOperation({ summary: '提交退款申请（小程序端）' })
  @ApiResponse({ status: 200, description: '退款申请提交成功' })
  async createRefundRequest(@Body() createRefundRequestDto: CreateRefundRequestDto) {
    this.logger.log(`提交退款申请: UserId=${createRefundRequestDto.userId}, OrderId=${createRefundRequestDto.orderId}`);
    try {
      return await this.orderService.createRefundRequest(
        createRefundRequestDto.userId, 
        createRefundRequestDto.orderId, 
        createRefundRequestDto
      );
    } catch (error) {
      this.logger.error(`提交退款申请失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `提交退款申请失败: ${error.message}`);
    }
  }

  // 注意：getUserRefundRequests 和 getRefundRequestsForAdmin 接口已删除
  // 退款信息已整合到订单管理中，用户和管理员可通过订单列表查看退款状态

  /**
   * 配送员确认取货（已完成订单退款流程）
   */
  @Post('admin/refund/confirm-pickup')
  @NotRequireAuth()
  @ApiOperation({ summary: '配送员确认取货（已完成订单退款流程-小程序端）' })
  @ApiResponse({ status: 200, description: '取货确认成功，退款已完成' })
  async confirmRefundPickup(@Body() confirmRefundPickupDto: ConfirmRefundPickupDto) {
    this.logger.log(`配送员确认取货: OrderId=${confirmRefundPickupDto.orderId}, DeliveryStaffId=${confirmRefundPickupDto.deliveryStaffId}`);
    try {
      return await this.orderService.confirmRefundPickup(
        confirmRefundPickupDto.orderId, 
        confirmRefundPickupDto.deliveryStaffId
      );
    } catch (error) {
      this.logger.error(`配送员确认取货失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      return ResultData.fail(HttpStatus.INTERNAL_SERVER_ERROR, `确认取货失败: ${error.message}`);
    }
  }
}
