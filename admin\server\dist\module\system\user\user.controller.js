"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("./user.service");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
const require_role_decorator_1 = require("../../../common/decorators/require-role.decorator");
const upload_service_1 = require("../../upload/upload.service");
const index_1 = require("./dto/index");
const platform_express_1 = require("@nestjs/platform-express");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("./user.decorator");
const business_constant_1 = require("../../../common/constant/business.constant");
const operlog_decorator_1 = require("../../../common/decorators/operlog.decorator");
let UserController = class UserController {
    constructor(userService, uploadService) {
        this.userService = userService;
        this.uploadService = uploadService;
    }
    profile(user) {
        return result_1.ResultData.ok(user.user);
    }
    updateProfile(user, updateProfileDto) {
        return this.userService.updateProfile(user, updateProfileDto);
    }
    async avatar(avatarfile, user) {
        const res = await this.uploadService.singleFileUpload(avatarfile);
        return result_1.ResultData.ok({ imgUrl: res.fileName });
    }
    updatePwd(user, updatePwdDto) {
        return this.userService.updatePwd(user, updatePwdDto);
    }
    create(createUserDto, { injectCreate }) {
        return this.userService.create(injectCreate(createUserDto));
    }
    findAll(query, user) {
        return this.userService.findAll(query, user.user);
    }
    deptTree() {
        return this.userService.deptTree();
    }
    findPostAndRoleAll() {
        return this.userService.findPostAndRoleAll();
    }
    authRole(id) {
        return this.userService.authRole(+id);
    }
    updateAuthRole(query) {
        return this.userService.updateAuthRole(query);
    }
    findOne(userId) {
        return this.userService.findOne(+userId);
    }
    changeStatus(changeStatusDto) {
        return this.userService.changeStatus(changeStatusDto);
    }
    async update(updateUserDto, user) {
        const activeUserId = user.userId;
        const result = await this.userService.update(updateUserDto, activeUserId);
        return result;
    }
    resetPwd(body) {
        return this.userService.resetPwd(body);
    }
    remove(ids) {
        const menuIds = ids.split(',').map((id) => +id);
        return this.userService.remove(menuIds);
    }
    async export(res, body, user) {
        return this.userService.export(res, body, user.user);
    }
};
exports.UserController = UserController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '个人中心-用户信息',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:query'),
    (0, common_1.Get)('/profile'),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "profile", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '个人中心-修改用户信息',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:edit'),
    (0, common_1.Put)('/profile'),
    (0, operlog_decorator_1.Operlog)({ businessType: business_constant_1.BusinessType.UPDATE }),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.UpdateProfileDto]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "updateProfile", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '个人中心-上传用户头像',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:edit'),
    (0, common_1.Post)('/profile/avatar'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('avatarfile')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "avatar", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '个人中心-修改密码',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:edit'),
    (0, operlog_decorator_1.Operlog)({ businessType: business_constant_1.BusinessType.UPDATE }),
    (0, common_1.Put)('/profile/updatePwd'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.UpdatePwdDto]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "updatePwd", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateUserDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:add'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.UserTool)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateUserDto, Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-列表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:list'),
    (0, common_1.Get)('list'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListUserDto, Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-部门树',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:query'),
    (0, common_1.Get)('deptTree'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UserController.prototype, "deptTree", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-角色+岗位',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:add'),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UserController.prototype, "findPostAndRoleAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-分配角色-详情',
    }),
    (0, require_role_decorator_1.RequireRole)('admin'),
    (0, common_1.Get)('authRole/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "authRole", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-角色信息-更新',
    }),
    (0, require_role_decorator_1.RequireRole)('admin'),
    (0, common_1.Put)('authRole'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "updateAuthRole", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:query'),
    (0, common_1.Get)(':userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-停用角色',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ChangeStatusDto,
        required: true,
    }),
    (0, require_role_decorator_1.RequireRole)('admin'),
    (0, common_1.Put)('changeStatus'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ChangeStatusDto]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "changeStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-更新',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdateUserDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-重置密码',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ResetPwdDto,
        required: true,
    }),
    (0, require_role_decorator_1.RequireRole)('admin'),
    (0, common_1.Put)('resetPwd'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ResetPwdDto]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "resetPwd", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户-删除',
    }),
    (0, require_role_decorator_1.RequireRole)('admin'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出用户信息数据为xlsx' }),
    (0, require_premission_decorator_1.RequirePermission)('system:user:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListUserDto, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "export", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('用户管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('system/user'),
    __metadata("design:paramtypes", [user_service_1.UserService,
        upload_service_1.UploadService])
], UserController);
//# sourceMappingURL=user.controller.js.map