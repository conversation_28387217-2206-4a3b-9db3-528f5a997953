"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateOrderDto {
}
exports.UpdateOrderDto = UpdateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户ID',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], UpdateOrderDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单ID',
        example: 'ORDER_202401010001',
    }),
    (0, class_validator_1.IsString)({ message: '订单ID必须是字符串' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单状态',
        example: '2',
        enum: ['1', '2', '3', '4', '5', '6'],
        enumName: 'OrderStatus',
    }),
    (0, class_validator_1.IsString)({ message: '订单状态必须是字符串' }),
    (0, class_validator_1.IsEnum)(['1', '2', '3', '4', '5', '6'], {
        message: '订单状态只能是：1待支付 2待发货 3配送中 4已完成 5已取消 6团购失败已退款',
    }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '取消原因（取消订单时必填）',
        example: '用户主动取消',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '取消原因必须是字符串' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "cancelReason", void 0);
//# sourceMappingURL=update-order.dto.js.map