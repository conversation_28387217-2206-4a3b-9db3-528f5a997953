{"openapi": "3.0.0", "paths": {"/login": {"post": {"operationId": "MainController_login", "summary": "用户登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/logout": {"post": {"operationId": "MainController_logout", "summary": "退出登录", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/register": {"post": {"operationId": "MainController_register", "summary": "用户注册", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/registerUser": {"get": {"operationId": "MainController_registerUser", "summary": "账号自助-是否开启用户注册功能", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/captchaImage": {"get": {"operationId": "MainController_captchaImage", "summary": "获取验证图片", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/getInfo": {"get": {"operationId": "MainController_getInfo", "summary": "用户信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/getRouters": {"get": {"operationId": "MainController_getRouters", "summary": "路由信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["根目录"]}}, "/common/upload": {"post": {"operationId": "UploadController_singleFileUpload", "summary": "文件上传", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/chunk/uploadId": {"get": {"operationId": "UploadController_getChunkUploadId", "summary": "获取切片上传任务Id", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/chunk": {"post": {"operationId": "UploadController_chunkFileUpload", "summary": "文件切片上传", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/chunk/merge": {"post": {"operationId": "UploadController_chunkMergeFile", "summary": "合并切片", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChunkMergeFileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/chunk/result": {"get": {"operationId": "UploadController_getChunkUploadResult", "summary": "获取切片上传结果", "parameters": [{"name": "uploadId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/cos/authorization": {"get": {"operationId": "UploadController_getAuthorization", "summary": "获取cos上传密钥", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/common/upload/delete": {"get": {"operationId": "UploadController_deleteFile", "summary": "删除文件", "parameters": [{"name": "fileUrl", "required": true, "in": "query", "description": "文件URL", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["通用-文件上传"]}}, "/system/config": {"post": {"operationId": "ConfigController_create", "summary": "参数设置-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConfigDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["参数设置"]}, "put": {"operationId": "ConfigController_update", "summary": "参数设置-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConfigDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}}, "/system/config/list": {"get": {"operationId": "ConfigController_findAll", "summary": "参数设置-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListConfigDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}}, "/system/config/{id}": {"get": {"operationId": "ConfigController_findOne", "summary": "参数设置-详情(id)", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}, "delete": {"operationId": "ConfigController_remove", "summary": "参数设置-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}}, "/system/config/configKey/{id}": {"get": {"operationId": "ConfigController_findOneByconfigKey", "summary": "参数设置-详情(config<PERSON><PERSON>)【走缓存】", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}}, "/system/config/refreshCache": {"delete": {"operationId": "ConfigController_refreshCache", "summary": "参数设置-刷新缓存", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["参数设置"]}}, "/system/config/export": {"post": {"operationId": "ConfigController_export", "summary": "导出参数管理为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListConfigDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["参数设置"]}}, "/system/dept": {"post": {"operationId": "DeptController_create", "summary": "部门管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeptDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}, "put": {"operationId": "DeptController_update", "summary": "部门管理-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeptDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}}, "/system/dept/list": {"get": {"operationId": "DeptController_findAll", "summary": "部门管理-列表", "parameters": [{"name": "deptName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}}, "/system/dept/{id}": {"get": {"operationId": "DeptController_findOne", "summary": "部门管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}, "delete": {"operationId": "DeptController_remove", "summary": "部门管理-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}}, "/system/dept/list/exclude/{id}": {"get": {"operationId": "DeptController_findListExclude", "summary": "部门管理-黑名单", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["部门管理"]}}, "/system/dict/type": {"post": {"operationId": "DictController_createType", "summary": "字典类型-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDictTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}, "put": {"operationId": "DictController_updateType", "summary": "字典类型-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDictTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/type/refreshCache": {"delete": {"operationId": "DictController_refreshCache", "summary": "字典数据-刷新缓存", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/type/{id}": {"delete": {"operationId": "DictController_deleteType", "summary": "字典类型-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}, "get": {"operationId": "DictController_findOneType", "summary": "字典类型-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/type/list": {"get": {"operationId": "DictController_findAllType", "summary": "字典类型-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/type/optionselect": {"get": {"operationId": "DictController_findOptionselect", "summary": "全部字典类型-下拉数据", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/data": {"post": {"operationId": "DictController_createDictData", "summary": "字典数据-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDictDataDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}, "put": {"operationId": "DictController_updateDictData", "summary": "字典数据-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDictDataDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/data/{id}": {"delete": {"operationId": "DictController_deleteDictData", "summary": "字典数据-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}, "get": {"operationId": "DictController_findOneDictData", "summary": "字典数据-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/data/list": {"get": {"operationId": "DictController_findAllData", "summary": "字典数据-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/data/type/{id}": {"get": {"operationId": "DictController_findOneDataType", "summary": "字典数据-类型-详情【走缓存】", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/type/export": {"post": {"operationId": "DictController_export", "summary": "导出字典组为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDictType"}}}}, "responses": {"201": {"description": ""}}, "tags": ["字典管理"]}}, "/system/dict/data/export": {"post": {"operationId": "DictController_exportData", "summary": "导出字典内容为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDictType"}}}}, "responses": {"201": {"description": ""}}, "tags": ["字典管理"]}}, "/system/menu": {"post": {"operationId": "MenuController_create", "summary": "菜单管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["菜单管理"]}, "put": {"operationId": "MenuController_update", "summary": "菜单管理-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}}, "/system/menu/list": {"get": {"operationId": "MenuController_findAll", "summary": "菜单管理-列表", "parameters": [{"name": "menuName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}}, "/system/menu/treeselect": {"get": {"operationId": "MenuController_treeSelect", "summary": "菜单管理-树表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}}, "/system/menu/roleMenuTreeselect/{menuId}": {"get": {"operationId": "MenuController_roleMenuTreeselect", "summary": "菜单管理-角色-树表", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}}, "/system/menu/{menuId}": {"get": {"operationId": "MenuController_findOne", "summary": "菜单管理-详情", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}, "delete": {"operationId": "MenuController_remove", "summary": "菜单管理-删除", "parameters": [{"name": "menuId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["菜单管理"]}}, "/system/notice": {"post": {"operationId": "NoticeController_create", "summary": "通知公告-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNoticeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["通知公告"]}, "put": {"operationId": "NoticeController_update", "summary": "通知公告-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNoticeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通知公告"]}}, "/system/notice/list": {"get": {"operationId": "NoticeController_findAll", "summary": "通知公告-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListNoticeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["通知公告"]}}, "/system/notice/{id}": {"get": {"operationId": "NoticeController_findOne", "summary": "通知公告-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["通知公告"]}, "delete": {"operationId": "NoticeController_remove", "summary": "通知公告-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["通知公告"]}}, "/system/post": {"post": {"operationId": "PostController_create", "summary": "岗位管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePostDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["岗位管理"]}, "put": {"operationId": "PostController_update", "summary": "岗位管理-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePostDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["岗位管理"]}}, "/system/post/list": {"get": {"operationId": "PostController_findAll", "summary": "岗位管理-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListPostDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["岗位管理"]}}, "/system/post/{id}": {"get": {"operationId": "PostController_findOne", "summary": "岗位管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["岗位管理"]}}, "/system/post/{ids}": {"delete": {"operationId": "PostController_remove", "summary": "岗位管理-删除", "parameters": [{"name": "ids", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["岗位管理"]}}, "/system/post/export": {"post": {"operationId": "PostController_export", "summary": "导出岗位管理xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListPostDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["岗位管理"]}}, "/system/role": {"post": {"operationId": "RoleController_create", "summary": "角色管理-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["角色管理"]}, "put": {"operationId": "RoleController_update", "summary": "角色管理-修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/list": {"get": {"operationId": "RoleController_findAll", "summary": "角色管理-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/deptTree/{id}": {"get": {"operationId": "RoleController_deptTree", "summary": "角色管理-部门树", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/{id}": {"get": {"operationId": "RoleController_findOne", "summary": "角色管理-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}, "delete": {"operationId": "RoleController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/dataScope": {"put": {"operationId": "RoleController_dataScope", "summary": "角色管理-数据权限修改", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/changeStatus": {"put": {"operationId": "RoleController_changeStatus", "summary": "角色管理-停用角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/authUser/allocatedList": {"get": {"operationId": "RoleController_authUserAllocatedList", "summary": "角色管理-角色已分配用户列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "roleId", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocatedListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/authUser/unallocatedList": {"get": {"operationId": "RoleController_authUserUnAllocatedList", "summary": "角色管理-角色未分配用户列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "roleId", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocatedListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/authUser/cancel": {"put": {"operationId": "RoleController_authUserCancel", "summary": "角色管理-解绑角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserCancelDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/authUser/cancelAll": {"put": {"operationId": "RoleController_authUserCancelAll", "summary": "角色管理-批量解绑角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserCancelAllDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/authUser/selectAll": {"put": {"operationId": "RoleController_authUserSelectAll", "summary": "角色管理-批量绑定角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUserSelectAllDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["角色管理"]}}, "/system/role/export": {"post": {"operationId": "RoleController_export", "summary": "导出角色管理xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListRoleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["角色管理"]}}, "/tool/gen/list": {"get": {"operationId": "ToolController_findAll", "summary": "数据表列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/db/list": {"get": {"operationId": "ToolController_genDbList", "summary": "查询数据库列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/importTable": {"post": {"operationId": "ToolController_genImportTable", "summary": "导入表", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TableName"}}}}, "responses": {"201": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/synchDb/{tableName}": {"get": {"operationId": "ToolController_synchDb", "summary": "同步表", "parameters": [{"name": "tableName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/{id}": {"get": {"operationId": "ToolController_gen", "summary": "查询表详细信息", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}, "delete": {"operationId": "ToolController_remove", "summary": "删除表数据", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen": {"put": {"operationId": "ToolController_genUpdate", "summary": "修改代码生成信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenTableUpdate"}}}}, "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/batchGenCode/zip": {"get": {"operationId": "ToolController_batchGenCode", "summary": "生成代码", "parameters": [{"name": "tableNames", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/tool/gen/preview/{id}": {"get": {"operationId": "ToolController_preview", "summary": "查看代码", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统工具"]}}, "/system/user/profile": {"get": {"operationId": "UserController_profile", "summary": "个人中心-用户信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}, "put": {"operationId": "UserController_updateProfile", "summary": "个人中心-修改用户信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/profile/avatar": {"post": {"operationId": "UserController_avatar", "summary": "个人中心-上传用户头像", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/profile/updatePwd": {"put": {"operationId": "UserController_updatePwd", "summary": "个人中心-修改密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePwdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user": {"post": {"operationId": "UserController_create", "summary": "用户-创建", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}, "get": {"operationId": "UserController_findPostAndRoleAll", "summary": "用户-角色+岗位", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}, "put": {"operationId": "UserController_update", "summary": "用户-更新", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/list": {"get": {"operationId": "UserController_findAll", "summary": "用户-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "deptId", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "nick<PERSON><PERSON>", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "email", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "phonenumber", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/deptTree": {"get": {"operationId": "UserController_deptTree", "summary": "用户-部门树", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/authRole/{id}": {"get": {"operationId": "UserController_authRole", "summary": "用户-分配角色-详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/authRole": {"put": {"operationId": "UserController_updateAuthRole", "summary": "用户-角色信息-更新", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/{userId}": {"get": {"operationId": "UserController_findOne", "summary": "用户-详情", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/changeStatus": {"put": {"operationId": "UserController_changeStatus", "summary": "用户-停用角色", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/resetPwd": {"put": {"operationId": "UserController_resetPwd", "summary": "用户-重置密码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPwdDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/{id}": {"delete": {"operationId": "UserController_remove", "summary": "用户-删除", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/system/user/export": {"post": {"operationId": "UserController_export", "summary": "导出用户信息数据为xlsx", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListUserDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["用户管理"], "security": [{"bearer": []}]}}, "/monitor/job/list": {"get": {"operationId": "JobController_list", "summary": "获取定时任务列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job/{jobId}": {"get": {"operationId": "JobController_getInfo", "summary": "获取定时任务详细信息", "parameters": [{"name": "jobId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job": {"post": {"operationId": "JobController_add", "summary": "创建定时任务", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务管理"]}, "put": {"operationId": "JobController_update", "summary": "修改定时任务", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job/changeStatus": {"put": {"operationId": "JobController_changeStatus", "summary": "修改任务状态", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job/{jobIds}": {"delete": {"operationId": "JobController_remove", "summary": "删除定时任务", "parameters": [{"name": "jobIds", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job/run": {"put": {"operationId": "JobController_run", "summary": "立即执行一次", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/job/export": {"post": {"operationId": "JobController_export", "summary": "导出定时任务为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListJobDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务管理"]}}, "/monitor/jobLog/list": {"get": {"operationId": "JobLogController_list", "summary": "获取定时任务日志列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "job<PERSON>ame", "required": true, "in": "query", "description": "任务名称", "schema": {"type": "string"}}, {"name": "jobGroup", "required": true, "in": "query", "description": "任务组名", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "状态（0正常 1暂停）", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/monitor/jobLog/clean": {"delete": {"operationId": "JobLogController_clean", "summary": "清空定时任务日志", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/monitor/jobLog/export": {"post": {"operationId": "JobLogController_export", "summary": "导出调度日志为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListJobLogDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["定时任务日志管理"]}}, "/monitor/server": {"get": {"operationId": "ServerController_getInfo", "summary": "在线用户-列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["系统监控-服务监控"]}}, "/monitor/cache": {"get": {"operationId": "CacheController_getInfo", "summary": "缓存监控信息", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/getNames": {"get": {"operationId": "CacheController_getNames", "summary": "缓存列表", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/getKeys/{id}": {"get": {"operationId": "CacheController_get<PERSON>eys", "summary": "键名列表", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/getValue/{cacheName}/{cacheKey}": {"get": {"operationId": "CacheController_getValue", "summary": "缓存内容", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/clearCacheName/{cacheName}": {"delete": {"operationId": "CacheController_clearCacheName", "summary": "清理缓存名称", "parameters": [{"name": "cacheName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/clearCacheKey/{cacheKey}": {"delete": {"operationId": "CacheController_clearCache<PERSON>ey", "summary": "清理缓存键名", "parameters": [{"name": "cache<PERSON>ey", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/cache/clearCacheAll": {"delete": {"operationId": "CacheController_clearCacheAll", "summary": "清理全部", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["缓存管理"]}}, "/monitor/logininfor/list": {"get": {"operationId": "LoginlogController_findAll", "summary": "登录日志-列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "ipaddr", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "userName", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListLoginlogDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/monitor/logininfor/clean": {"delete": {"operationId": "LoginlogController_removeAll", "summary": "登录日志-清除全部日志", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/monitor/logininfor/{id}": {"delete": {"operationId": "LoginlogController_remove", "summary": "登录日志-删除日志", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["登录日志"]}}, "/monitor/logininfor/export": {"post": {"operationId": "LoginlogController_export", "summary": "导出登录日志为xlsx文件", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListLoginlogDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["登录日志"]}}, "/monitor/online/list": {"get": {"operationId": "OnlineController_findAll", "summary": "在线用户-列表", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OnlineListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["系统监控-在线用户"]}}, "/monitor/online/{token}": {"delete": {"operationId": "OnlineController_delete", "summary": "在线用户-强退", "parameters": [{"name": "token", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["系统监控-在线用户"]}}, "/monitor/operlog": {"post": {"operationId": "OperlogController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOperlogDto"}}}}, "responses": {"201": {"description": ""}}}}, "/monitor/operlog/clean": {"delete": {"operationId": "OperlogController_removeAll", "summary": "登录日志-清除全部日志", "parameters": [], "responses": {"200": {"description": ""}}}}, "/monitor/operlog/list": {"get": {"operationId": "OperlogController_findAll", "parameters": [], "responses": {"200": {"description": ""}}}}, "/monitor/operlog/{id}": {"get": {"operationId": "OperlogController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}, "patch": {"operationId": "OperlogController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOperlogDto"}}}}, "responses": {"200": {"description": ""}}}, "delete": {"operationId": "OperlogController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/api/miniprogram/delivery-time-slots": {"get": {"operationId": "MiniprogramApiController_getDeliveryTimeSlots", "summary": "获取可用的配送时间段", "description": "获取小程序可用的配送时间段列表", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": [{"label": "上午9:00-12:00", "value": "10:00:00", "disabled": false}, {"label": "下午13:00-18:00", "value": "15:00:00", "disabled": false}, {"label": "晚上18:00-21:00", "value": "19:30:00", "disabled": false}]}}}}}}, "tags": ["小程序公开接口"]}}, "/miniprogram/user/wechat-phone-auth": {"post": {"operationId": "UserController_wechatPhoneAuth", "summary": "微信授权手机号登录/注册", "description": "通过微信手机号快速验证组件获取用户手机号，实现一键登录/注册功能。支持与微信登录结合使用。", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WechatPhoneAuthDto"}}}}, "responses": {"200": {"description": "登录/注册成功", "schema": {"example": {"code": 200, "msg": "登录成功", "data": {"user": {"userId": 1, "nickname": "张三", "avatar": "https://example.com/avatar.jpg", "phone": "13812345678", "gender": "1"}, "isNewUser": false}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDto"}}}}}, "tags": ["小程序用户管理"]}}, "/miniprogram/user/profile/{userId}": {"get": {"operationId": "UserController_getUserProfile", "summary": "获取用户资料", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDto"}}}}}, "tags": ["小程序用户管理"]}, "put": {"operationId": "UserController_updateUserProfile", "summary": "更新用户资料", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDto"}}}}}, "tags": ["小程序用户管理"]}}, "/miniprogram/user/admin/list": {"get": {"operationId": "UserController_getUserList", "summary": "管理员获取用户列表", "parameters": [{"name": "nickname", "required": false, "in": "query", "description": "用户昵称", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "手机号码", "schema": {"type": "string"}}, {"name": "level", "required": false, "in": "query", "description": "用户等级", "schema": {"type": "number"}}, {"name": "userType", "required": false, "in": "query", "description": "用户类型：0-普通用户，1-VIP用户", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态：0-正常，1-停用", "schema": {"type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": "获取成功"}}, "tags": ["小程序用户管理"]}}, "/miniprogram/user/admin/{userId}": {"put": {"operationId": "UserController_adminUpdateUser", "summary": "管理员更新用户信息", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminUpdateUserDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDto"}}}}}, "tags": ["小程序用户管理"]}}, "/miniprogram/user/stats": {"get": {"operationId": "UserController_getUserStats", "summary": "获取用户统计信息", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序用户管理"]}}, "/miniprogram/category": {"post": {"operationId": "CategoryController_create", "summary": "创建分类", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"201": {"description": "创建成功"}}, "tags": ["商品分类管理"], "security": [{"bearer": []}]}, "get": {"operationId": "CategoryController_findAll", "summary": "分页查询分类列表（管理端）", "parameters": [{"name": "name", "required": false, "in": "query", "description": "分类名称（模糊搜索）", "schema": {"example": "水果", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "状态：1启用，0禁用", "schema": {"example": 1, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品分类管理"]}}, "/miniprogram/category/enabled": {"get": {"operationId": "CategoryController_findEnabled", "summary": "获取启用的分类列表（小程序端）", "parameters": [], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品分类管理"]}}, "/miniprogram/category/detail": {"get": {"operationId": "CategoryController_findOne", "summary": "获取单个分类详情", "parameters": [{"name": "id", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品分类管理"], "security": [{"bearer": []}]}}, "/miniprogram/category/{id}": {"put": {"operationId": "CategoryController_update", "summary": "更新分类", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["商品分类管理"], "security": [{"bearer": []}]}, "delete": {"operationId": "CategoryController_remove", "summary": "删除分类", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["商品分类管理"], "security": [{"bearer": []}]}}, "/miniprogram/category/status/batch": {"put": {"operationId": "CategoryController_updateStatus", "summary": "批量更新分类状态", "parameters": [], "responses": {"200": {"description": "更新成功"}}, "tags": ["商品分类管理"], "security": [{"bearer": []}]}}, "/miniprogram/product": {"post": {"operationId": "ProductController_create", "summary": "创建商品", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"201": {"description": "创建成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}, "get": {"operationId": "ProductController_findAll", "summary": "分页查询商品列表（管理端）", "parameters": [{"name": "name", "required": false, "in": "query", "description": "商品名称（模糊搜索）", "schema": {"example": "苹果", "type": "string"}}, {"name": "categoryId", "required": false, "in": "query", "description": "分类ID", "schema": {"example": 1, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "状态：1上架，0下架", "schema": {"example": 1, "type": "number"}}, {"name": "minPrice", "required": false, "in": "query", "description": "最低价格", "schema": {"example": 10, "type": "number"}}, {"name": "maxPrice", "required": false, "in": "query", "description": "最高价格", "schema": {"example": 100, "type": "number"}}, {"name": "originPlace", "required": false, "in": "query", "description": "产地（模糊搜索）", "schema": {"example": "山东", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品管理"]}}, "/miniprogram/product/enabled": {"get": {"operationId": "ProductController_findEnabled", "summary": "获取上架商品列表（小程序端）", "parameters": [{"name": "name", "required": false, "in": "query", "description": "商品名称（模糊搜索）", "schema": {"example": "苹果", "type": "string"}}, {"name": "categoryId", "required": false, "in": "query", "description": "分类ID", "schema": {"example": 1, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "状态：1上架，0下架", "schema": {"example": 1, "type": "number"}}, {"name": "minPrice", "required": false, "in": "query", "description": "最低价格", "schema": {"example": 10, "type": "number"}}, {"name": "maxPrice", "required": false, "in": "query", "description": "最高价格", "schema": {"example": 100, "type": "number"}}, {"name": "originPlace", "required": false, "in": "query", "description": "产地（模糊搜索）", "schema": {"example": "山东", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品管理"]}}, "/miniprogram/product/{id}": {"get": {"operationId": "ProductController_findOne", "summary": "查询商品详情（小程序端）", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "userId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品管理"]}, "put": {"operationId": "ProductController_update", "summary": "更新商品", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}, "delete": {"operationId": "ProductController_remove", "summary": "删除商品", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}}, "/miniprogram/product/status/batch": {"put": {"operationId": "ProductController_updateStatus", "summary": "批量更新商品状态", "parameters": [], "responses": {"200": {"description": "更新成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}}, "/miniprogram/product/{id}/sales": {"put": {"operationId": "ProductController_increaseSales", "summary": "增加商品销量", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "更新成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}}, "/miniprogram/product/{id}/specs": {"post": {"operationId": "ProductController_addSpec", "summary": "添加商品规格", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductSpecDto"}}}}, "responses": {"201": {"description": "添加成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}, "get": {"operationId": "ProductController_findSpecs", "summary": "获取商品规格列表", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品管理"]}}, "/miniprogram/product/specs/{specId}": {"put": {"operationId": "ProductController_updateSpec", "summary": "更新商品规格", "parameters": [{"name": "specId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSpecDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}, "delete": {"operationId": "ProductController_removeSpec", "summary": "删除商品规格", "parameters": [{"name": "specId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}}, "/miniprogram/product/specs/{specId}/default": {"put": {"operationId": "ProductController_setDefaultSpec", "summary": "设置默认规格", "parameters": [{"name": "specId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "设置成功"}}, "tags": ["商品管理"], "security": [{"bearer": []}]}}, "/miniprogram/footprint/list": {"get": {"operationId": "FootprintController_findUserFootprints", "summary": "获取用户足迹列表", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "pageNum", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["用户足迹"]}}, "/miniprogram/footprint/{id}": {"delete": {"operationId": "FootprintController_removeFootprint", "summary": "删除单条足迹", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "userId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["用户足迹"]}}, "/miniprogram/footprint/delete/batch": {"post": {"operationId": "FootprintController_removeFootprints", "summary": "批量删除足迹", "parameters": [], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["用户足迹"]}}, "/miniprogram/footprint/clear": {"delete": {"operationId": "FootprintController_clearUserFootprints", "summary": "清空用户足迹", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "清空成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["用户足迹"]}}, "/miniprogram/cart/{userId}/add": {"post": {"operationId": "CartController_addToCart", "summary": "添加商品到购物车（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartDto"}}}}, "responses": {"200": {"description": "添加成功"}}, "tags": ["购物车管理"]}}, "/miniprogram/cart/{userId}/items": {"get": {"operationId": "CartController_getCartItems", "summary": "获取用户购物车列表（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["购物车管理"]}}, "/miniprogram/cart/{userId}/items/{cartId}": {"put": {"operationId": "CartController_updateCartItem", "summary": "更新购物车商品数量（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "cartId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["购物车管理"]}, "delete": {"operationId": "CartController_removeFromCart", "summary": "删除购物车商品（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "cartId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["购物车管理"]}}, "/miniprogram/cart/{userId}/clear": {"delete": {"operationId": "CartController_clearCart", "summary": "清空用户购物车（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "清空成功"}}, "tags": ["购物车管理"]}}, "/miniprogram/cart/{userId}/count": {"get": {"operationId": "CartController_getCartCount", "summary": "获取购物车统计信息（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["购物车管理"]}}, "/miniprogram/cart/admin/list": {"get": {"operationId": "CartController_findAll", "summary": "分页查询购物车列表（管理端）", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "productName", "required": false, "in": "query", "description": "商品名称搜索", "schema": {"example": "苹果", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["购物车管理"], "security": [{"bearer": []}]}}, "/miniprogram/cart/admin/batch": {"delete": {"operationId": "CartController_batchRemove", "summary": "批量删除购物车项（管理端）", "parameters": [], "responses": {"200": {"description": "删除成功"}}, "tags": ["购物车管理"], "security": [{"bearer": []}]}}, "/miniprogram/address/{userId}": {"post": {"operationId": "AddressController_create", "summary": "创建地址", "description": "用户创建新的收货地址，支持设置为默认地址", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAddressDto"}}}}, "responses": {"201": {"description": "地址创建成功", "schema": {"example": {"code": 200, "msg": "地址创建成功", "data": {"addressId": 1, "userId": 1, "receiverName": "张三", "receiverPhone": "13812345678", "addressName": "中关村软件园", "detailAddress": "科技园南区科苑路XX号", "isDefault": 1, "label": "家"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddress"}}}}}, "tags": ["小程序地址管理"]}, "get": {"operationId": "AddressController_findByUserId", "summary": "获取用户地址列表", "description": "获取用户的所有收货地址，支持按标签和默认地址筛选", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}, {"name": "label", "required": false, "in": "query", "description": "地址标签", "schema": {"example": "家", "type": "string"}}, {"name": "isDefault", "required": false, "in": "query", "description": "是否默认地址", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddress"}}}}}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/{userId}/{addressId}": {"get": {"operationId": "AddressController_findById", "summary": "获取地址详情", "description": "根据地址ID获取具体的地址信息", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}, {"name": "addressId", "required": true, "in": "path", "description": "地址ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddress"}}}}}, "tags": ["小程序地址管理"]}, "put": {"operationId": "AddressController_update", "summary": "更新地址", "description": "更新指定地址的信息", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}, {"name": "addressId", "required": true, "in": "path", "description": "地址ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAddressDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddress"}}}}}, "tags": ["小程序地址管理"]}, "delete": {"operationId": "AddressController_remove", "summary": "删除地址", "description": "删除指定的地址（软删除）", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}, {"name": "addressId", "required": true, "in": "path", "description": "地址ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/{userId}/{addressId}/default": {"put": {"operationId": "AddressController_setDefault", "summary": "设置默认地址", "description": "将指定地址设置为默认地址，其他地址自动取消默认状态", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}, {"name": "addressId", "required": true, "in": "path", "description": "地址ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "设置成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddress"}}}}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/{userId}/default": {"get": {"operationId": "AddressController_getDefaultAddress", "summary": "获取用户默认地址", "description": "获取用户设置的默认收货地址", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddress"}}}}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/{userId}/stats": {"get": {"operationId": "AddressController_getAddressStats", "summary": "获取地址统计信息", "description": "获取用户地址的统计信息，包括总数量、是否有默认地址等", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": {"total": 3, "hasDefault": true}}}}}}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/check-delivery": {"post": {"operationId": "AddressController_checkDeliveryRange", "summary": "检查配送范围", "description": "检查指定地址是否在配送范围内", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryRangeDto"}}}}, "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "检查成功", "data": {"inRange": true, "distance": 5.2}}}}}}}, "tags": ["小程序地址管理"]}}, "/miniprogram/address/admin/list": {"get": {"operationId": "AddressController_adminList", "summary": "管理员获取地址列表", "description": "管理员获取所有用户的地址列表，支持分页和条件筛选", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页条数", "schema": {"type": "number"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"type": "number"}}, {"name": "<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "收货人姓名", "schema": {"type": "string"}}, {"name": "receiverPhone", "required": false, "in": "query", "description": "收货人电话", "schema": {"type": "string"}}, {"name": "addressName", "required": false, "in": "query", "description": "地址名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": {"rows": [{"addressId": 1, "userId": 1, "receiverName": "张三", "receiverPhone": "13812345678", "addressName": "中关村软件园", "detailAddress": "科技园南区科苑路XX号", "isDefault": 1, "label": "家", "createTime": "2023-01-01 12:00:00"}], "total": 1}}}}}}}, "tags": ["小程序地址管理"]}}, "/admin/delivery-settings": {"post": {"operationId": "DeliverySettingsController_create", "summary": "创建配送设置", "description": "创建新的配送设置，包括超市名称、经纬度、配送半径等信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeliverySettingsDto"}}}}, "responses": {"201": {"description": "配送设置创建成功", "schema": {"example": {"code": 200, "msg": "配送设置创建成功", "data": {"id": 1, "storeName": "初鲜果味超市", "latitude": 22.5329, "longitude": 114.0544, "deliveryRadius": 5, "isActive": 1, "createTime": "2023-01-01 12:00:00", "updateTime": "2023-01-01 12:00:00"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliverySettings"}}}}}, "tags": ["配送设置管理"]}, "get": {"operationId": "DeliverySettingsController_findAll", "summary": "获取配送设置列表", "description": "获取配送设置列表，支持分页和条件筛选", "parameters": [{"name": "storeName", "required": false, "in": "query", "description": "超市名称", "schema": {"type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页条数", "schema": {"example": 10, "type": "number"}}, {"name": "isActive", "required": false, "in": "query", "description": "是否启用", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": {"rows": [{"id": 1, "storeName": "初鲜果味超市", "latitude": 22.5329, "longitude": 114.0544, "deliveryRadius": 5, "isActive": 1, "createTime": "2023-01-01 12:00:00", "updateTime": "2023-01-01 12:00:00"}], "total": 1}}}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/{id}": {"put": {"operationId": "DeliverySettingsController_update", "summary": "更新配送设置", "description": "更新指定配送设置的信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送设置ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeliverySettingsDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliverySettings"}}}}}, "tags": ["配送设置管理"]}, "delete": {"operationId": "DeliverySettingsController_remove", "summary": "删除配送设置", "description": "删除指定的配送设置", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送设置ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["配送设置管理"]}, "get": {"operationId": "DeliverySettingsController_findOne", "summary": "获取配送设置详情", "description": "根据ID获取具体的配送设置信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送设置ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliverySettings"}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/settings/unique": {"get": {"operationId": "DeliverySettingsController_getUniqueSettings", "summary": "获取唯一的配送设置", "description": "获取系统中唯一的配送设置", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliverySettings"}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/check-range": {"post": {"operationId": "DeliverySettingsController_checkDeliveryRange", "summary": "检查配送范围", "description": "检查指定坐标是否在配送范围内", "parameters": [], "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "检查成功", "data": {"inRange": true, "distance": 3.2, "storeName": "初鲜果味超市", "maxRadius": 5}}}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/time-slots": {"post": {"operationId": "DeliverySettingsController_createTimeSlot", "summary": "创建配送时间段", "description": "创建新的配送时间段", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeliveryTimeSlotDto"}}}}, "responses": {"201": {"description": "配送时间段创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryTimeSlot"}}}}}, "tags": ["配送设置管理"]}, "get": {"operationId": "DeliverySettingsController_getActiveTimeSlots", "summary": "获取启用的配送时间段列表", "description": "获取所有启用的配送时间段，按排序顺序返回（小程序端使用）", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": [{"id": 1, "label": "上午9:00-12:00", "value": "10:00:00", "sortOrder": 1, "isActive": 1}, {"id": 2, "label": "下午13:00-18:00", "value": "15:00:00", "sortOrder": 2, "isActive": 1}]}}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/time-slots/page": {"post": {"operationId": "DeliverySettingsController_findAllTimeSlots", "summary": "获取配送时间段列表", "description": "获取配送时间段列表，支持分页和条件筛选", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryTimeSlotQueryDto"}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "msg": "获取成功", "data": {"rows": [{"id": 1, "label": "上午9:00-12:00", "value": "10:00:00", "sortOrder": 1, "isActive": 1, "createTime": "2023-01-01 12:00:00", "updateTime": "2023-01-01 12:00:00"}], "total": 1}}}}}}}, "tags": ["配送设置管理"]}}, "/admin/delivery-settings/time-slots/{id}": {"get": {"operationId": "DeliverySettingsController_findOneTimeSlot", "summary": "获取配送时间段详情", "description": "根据ID获取具体的配送时间段信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送时间段ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryTimeSlot"}}}}}, "tags": ["配送设置管理"]}, "put": {"operationId": "DeliverySettingsController_updateTimeSlot", "summary": "更新配送时间段", "description": "更新指定配送时间段的信息", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送时间段ID", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeliveryTimeSlotDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryTimeSlot"}}}}}, "tags": ["配送设置管理"]}, "delete": {"operationId": "DeliverySettingsController_removeTimeSlot", "summary": "删除配送时间段", "description": "删除指定的配送时间段", "parameters": [{"name": "id", "required": true, "in": "path", "description": "配送时间段ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["配送设置管理"]}}, "/miniprogram/order/{userId}/create": {"post": {"operationId": "OrderController_createOrder", "summary": "创建订单（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}}}, "responses": {"200": {"description": "订单创建成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/{userId}/stats": {"get": {"operationId": "OrderController_getUserOrderStats", "summary": "获取用户订单统计（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/list": {"get": {"operationId": "OrderController_getUserOrders", "summary": "获取用户订单列表（小程序端）", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "订单状态", "schema": {"example": "1", "enum": ["1", "2", "3", "4", "5", "6", "7"], "type": "string"}}, {"name": "orderType", "required": false, "in": "query", "description": "订单类型", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "orderId", "required": false, "in": "query", "description": "订单ID关键词", "schema": {"example": "ORD", "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "收货人姓名", "schema": {"example": "张三", "type": "string"}}, {"name": "receiverPhone", "required": false, "in": "query", "description": "收货人电话", "schema": {"example": "13800138000", "type": "string"}}, {"name": "phoneLastFour", "required": false, "in": "query", "description": "手机号尾号(后4位)", "schema": {"example": "8000", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"example": "2024-06-01", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"example": "2024-06-30", "type": "string"}}, {"name": "deliveryType", "required": false, "in": "query", "description": "配送方式", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "orderByDeliveryTime", "required": false, "in": "query", "description": "按预约配送时间排序（ASC/DESC）", "schema": {"example": "DESC", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/{userId}/detail/{orderId}": {"get": {"operationId": "OrderController_getOrderDetail", "summary": "获取订单详情（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/{userId}/cancel/{orderId}": {"put": {"operationId": "OrderController_cancelOrder", "summary": "取消订单（小程序端）", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "取消成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/status": {"put": {"operationId": "OrderController_updateOrderStatus", "summary": "更新订单状态（小程序端）", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/admin/list": {"get": {"operationId": "OrderController_findAll", "summary": "分页查询订单列表（管理端）", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "订单状态", "schema": {"example": "1", "enum": ["1", "2", "3", "4", "5", "6", "7"], "type": "string"}}, {"name": "orderType", "required": false, "in": "query", "description": "订单类型", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "orderId", "required": false, "in": "query", "description": "订单ID关键词", "schema": {"example": "ORD", "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "收货人姓名", "schema": {"example": "张三", "type": "string"}}, {"name": "receiverPhone", "required": false, "in": "query", "description": "收货人电话", "schema": {"example": "13800138000", "type": "string"}}, {"name": "phoneLastFour", "required": false, "in": "query", "description": "手机号尾号(后4位)", "schema": {"example": "8000", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"example": "2024-06-01", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"example": "2024-06-30", "type": "string"}}, {"name": "deliveryType", "required": false, "in": "query", "description": "配送方式", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "orderByDeliveryTime", "required": false, "in": "query", "description": "按预约配送时间排序（ASC/DESC）", "schema": {"example": "DESC", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/admin/self-pickup/list": {"get": {"operationId": "OrderController_findSelfPickupOrders", "summary": "分页查询自提订单列表（管理端）", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "订单状态", "schema": {"example": "1", "enum": ["1", "2", "3", "4", "5", "6", "7"], "type": "string"}}, {"name": "orderType", "required": false, "in": "query", "description": "订单类型", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "orderId", "required": false, "in": "query", "description": "订单ID关键词", "schema": {"example": "ORD", "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "收货人姓名", "schema": {"example": "张三", "type": "string"}}, {"name": "receiverPhone", "required": false, "in": "query", "description": "收货人电话", "schema": {"example": "13800138000", "type": "string"}}, {"name": "phoneLastFour", "required": false, "in": "query", "description": "手机号尾号(后4位)", "schema": {"example": "8000", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"example": "2024-06-01", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"example": "2024-06-30", "type": "string"}}, {"name": "deliveryType", "required": false, "in": "query", "description": "配送方式", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "orderByDeliveryTime", "required": false, "in": "query", "description": "按预约配送时间排序（ASC/DESC）", "schema": {"example": "DESC", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/admin/detail/{orderId}": {"get": {"operationId": "OrderController_getAdminOrderDetail", "summary": "获取订单详情（管理端）", "parameters": [{"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/complete": {"post": {"operationId": "OrderController_completeOrder", "summary": "完成订单（小程序端）", "parameters": [], "responses": {"200": {"description": "订单完成成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/admin/status/{orderId}": {"put": {"operationId": "OrderController_updateAdminOrderStatus", "summary": "更新订单状态（管理端）", "parameters": [{"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderDto"}}}}, "responses": {"200": {"description": "更新成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/admin/stats": {"get": {"operationId": "OrderController_getOrderStats", "summary": "获取订单统计（管理端）", "parameters": [], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/admin/dashboard/statistics": {"get": {"operationId": "OrderController_getDashboardStatistics", "summary": "获取仪表板详细统计数据（管理端）", "parameters": [{"name": "period", "required": false, "in": "query", "description": "统计周期", "schema": {"default": "day", "enum": ["day", "week", "month"], "type": "string"}}, {"name": "range", "required": false, "in": "query", "description": "时间范围长度", "schema": {"default": 7, "type": "number"}}, {"name": "topCount", "required": false, "in": "query", "description": "热销商品排名数量", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/admin/debug/tables": {"get": {"operationId": "OrderController_debugTables", "summary": "调试：检查数据库表和数据（调试用）", "parameters": [], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/deliver": {"post": {"operationId": "OrderController_deliverOrder", "summary": "订单发货（管理端）", "parameters": [], "responses": {"200": {"description": "发货成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/refund": {"post": {"operationId": "OrderController_refundOrder", "summary": "订单退款（管理端）", "parameters": [], "responses": {"200": {"description": "退款成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/export": {"get": {"operationId": "OrderController_exportOrders", "summary": "导出订单数据（管理端）", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "订单状态", "schema": {"example": "1", "enum": ["1", "2", "3", "4", "5", "6", "7"], "type": "string"}}, {"name": "orderType", "required": false, "in": "query", "description": "订单类型", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "orderId", "required": false, "in": "query", "description": "订单ID关键词", "schema": {"example": "ORD", "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "required": false, "in": "query", "description": "收货人姓名", "schema": {"example": "张三", "type": "string"}}, {"name": "receiverPhone", "required": false, "in": "query", "description": "收货人电话", "schema": {"example": "13800138000", "type": "string"}}, {"name": "phoneLastFour", "required": false, "in": "query", "description": "手机号尾号(后4位)", "schema": {"example": "8000", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"example": "2024-06-01", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"example": "2024-06-30", "type": "string"}}, {"name": "deliveryType", "required": false, "in": "query", "description": "配送方式", "schema": {"example": "1", "enum": ["1", "2"], "type": "string"}}, {"name": "orderByDeliveryTime", "required": false, "in": "query", "description": "按预约配送时间排序（ASC/DESC）", "schema": {"example": "DESC", "type": "string"}}], "responses": {"200": {"description": "导出成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/batch/cancel": {"post": {"operationId": "OrderController_batchCancelOrders", "summary": "批量取消订单（管理端）", "parameters": [], "responses": {"200": {"description": "取消成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/detail/{orderId}": {"get": {"operationId": "OrderController_getOrderDetailForAdmin", "summary": "获取订单详情（管理端）", "parameters": [{"name": "orderId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"], "security": [{"bearer": []}]}}, "/miniprogram/order/debug/check-group-buy/{activityId}": {"get": {"operationId": "OrderController_checkGroupBuyStatus", "summary": "检查团购活动状态（调试用）", "parameters": [{"name": "activityId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/refund-request": {"post": {"operationId": "OrderController_createRefundRequest", "summary": "提交退款申请（小程序端）", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRefundRequestDto"}}}}, "responses": {"200": {"description": "退款申请提交成功"}}, "tags": ["订单管理"]}}, "/miniprogram/order/admin/refund/confirm-pickup": {"post": {"operationId": "OrderController_confirmRefundPickup", "summary": "配送员确认取货（已完成订单退款流程-小程序端）", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmRefundPickupDto"}}}}, "responses": {"200": {"description": "取货确认成功，退款已完成"}}, "tags": ["订单管理"]}}, "/miniprogram/payment/order/{userId}": {"post": {"operationId": "PaymentController_createPayment", "summary": "创建支付订单", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}, "400": {"description": "参数错误"}}, "tags": ["支付管理"]}}, "/miniprogram/payment/recharge/{userId}": {"post": {"operationId": "PaymentController_createBalanceRecharge", "summary": "创建余额充值订单", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceRechargeDto"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}, "400": {"description": "参数错误"}}, "tags": ["支付管理"]}}, "/miniprogram/payment/recharge/admin": {"post": {"operationId": "PaymentController_rechargeUserBalance", "summary": "管理员为用户充值余额", "parameters": [{"name": "operatorId", "required": true, "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBalanceRechargeDto"}}}}, "responses": {"200": {"description": "充值成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}, "400": {"description": "参数错误"}}, "tags": ["支付管理"]}}, "/miniprogram/payment/notify/{orderId}": {"post": {"operationId": "PaymentController_handlePaymentNotify", "summary": "支付回调处理（第三方支付平台调用）", "parameters": [{"name": "orderId", "required": true, "in": "path", "description": "订单ID或充值单号", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentNotifyDto"}}}}, "responses": {"200": {"description": "回调处理成功"}, "400": {"description": "回调处理失败", "content": {"application/json": {"schema": {"example": {"code": "FAIL", "message": "失败"}}}}}, "500": {"description": "内部服务器错误", "content": {"application/json": {"schema": {"example": {"code": "FAIL", "message": "失败"}}}}}}, "tags": ["支付管理"]}}, "/miniprogram/payment/balance/{userId}": {"get": {"operationId": "PaymentController_getUserBalance", "summary": "查询用户余额", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["支付管理"]}}, "/miniprogram/payment/status/{orderId}/{userId}": {"get": {"operationId": "PaymentController_getPaymentStatus", "summary": "查询支付状态", "parameters": [{"name": "orderId", "required": true, "in": "path", "description": "订单ID", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["支付管理"]}}, "/miniprogram/payment/list": {"get": {"operationId": "PaymentController_getUserBalanceChangeList", "summary": "获取用户余额变动记录列表", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "页大小", "schema": {"example": 10, "type": "string"}}, {"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["支付管理"]}}, "/miniprogram/payment/refund/{userId}": {"post": {"operationId": "PaymentController_requestRefund", "summary": "申请退款", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestDto"}}}}, "responses": {"200": {"description": "退款申请成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}, "400": {"description": "参数错误"}}, "tags": ["支付管理"]}}, "/miniprogram/marketing/coupon/admin/create": {"post": {"operationId": "CouponController_createCoupon", "summary": "创建优惠券", "description": "管理员创建新的优惠券", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCouponDto"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/update": {"put": {"operationId": "CouponController_updateCoupon", "summary": "更新优惠券", "description": "管理员更新优惠券信息", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCouponDto"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/list": {"get": {"operationId": "CouponController_getCouponList", "summary": "查询优惠券列表", "description": "管理员查询优惠券列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "name", "required": false, "in": "query", "description": "优惠券名称", "schema": {"example": "满100减10", "type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "优惠券类型 1满减券 2折扣券 3无门槛券", "schema": {"example": "1", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "优惠券状态 0无效 1有效", "schema": {"example": "1", "type": "string"}}, {"name": "startTime", "required": false, "in": "query", "description": "开始时间", "schema": {"format": "date-time", "example": "2024-07-01 00:00:00", "type": "string"}}, {"name": "endTime", "required": false, "in": "query", "description": "结束时间", "schema": {"format": "date-time", "example": "2024-07-31 23:59:59", "type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/detail/{couponId}": {"get": {"operationId": "CouponController_getCouponDetail", "summary": "获取优惠券详情", "description": "根据ID获取优惠券详情", "parameters": [{"name": "couponId", "required": true, "in": "path", "description": "优惠券ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/delete/{couponId}": {"delete": {"operationId": "CouponController_deleteCoupon", "summary": "删除优惠券", "description": "管理员删除优惠券", "parameters": [{"name": "couponId", "required": true, "in": "path", "description": "优惠券ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/receive": {"post": {"operationId": "CouponController_receiveCoupon", "summary": "领取优惠券", "description": "用户领取优惠券", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiveCouponDto"}}}}, "responses": {"200": {"description": "领取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/user/list": {"get": {"operationId": "CouponController_getUserCouponList", "summary": "获取用户优惠券列表", "description": "查询用户的优惠券列表", "parameters": [{"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "优惠券状态 0未使用1已使用2已过期", "schema": {"example": "0", "type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/use": {"post": {"operationId": "CouponController_useCoupon", "summary": "使用优惠券", "description": "用户使用优惠券", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UseCouponDto"}}}}, "responses": {"200": {"description": "使用成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/user/available": {"get": {"operationId": "CouponController_getAvailableCoupons", "summary": "获取用户可用优惠券", "description": "获取用户可用于指定订单金额的优惠券列表", "parameters": [{"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"type": "number"}}, {"name": "orderAmount", "required": true, "in": "query", "description": "订单金额", "schema": {"type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CouponResponse"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/available-to-receive": {"get": {"operationId": "CouponController_getAvailableToReceiveCoupons", "summary": "获取用户可领取优惠券列表", "description": "获取当前可领取的优惠券列表", "parameters": [{"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/test-concurrency": {"post": {"operationId": "CouponController_testConcurrentReceive", "summary": "模拟高并发领券（仅用于测试）", "parameters": [], "responses": {"200": {"description": "模拟高并发领券成功"}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/distribute": {"post": {"operationId": "CouponController_distributeCoupon", "summary": "发放优惠券给指定用户", "description": "管理员将优惠券发放给指定用户", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DistributeCouponDto"}}}}, "responses": {"200": {"description": "发放成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/users": {"get": {"operationId": "CouponController_getUserList", "summary": "获取用户列表", "description": "获取用户列表供发放优惠券时选择", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "页码", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "nick<PERSON><PERSON>", "required": false, "in": "query", "description": "用户昵称", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "手机号", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/marketing/coupon/admin/distribute-records/{couponId}": {"get": {"operationId": "CouponController_getDistributeRecords", "summary": "查看优惠券发放记录", "description": "查看指定优惠券的发放记录", "parameters": [{"name": "couponId", "required": true, "in": "path", "description": "优惠券ID", "schema": {"type": "number"}}, {"name": "pageNum", "required": true, "in": "query", "description": "页码", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "nick<PERSON><PERSON>", "required": false, "in": "query", "description": "用户昵称", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "手机号", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["营销模块-优惠券"]}}, "/miniprogram/review": {"post": {"operationId": "ReviewController_create", "summary": "创建商品评价", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewDto"}}}}, "responses": {"200": {"description": "创建成功"}}, "tags": ["商品评价"]}, "get": {"operationId": "ReviewController_findAll", "summary": "查询评价列表", "parameters": [{"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "productId", "required": false, "in": "query", "description": "商品ID", "schema": {"type": "number"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID", "schema": {"type": "number"}}, {"name": "orderId", "required": false, "in": "query", "description": "订单ID", "schema": {"type": "string"}}, {"name": "minRating", "required": false, "in": "query", "description": "最低评分", "schema": {"type": "number"}}, {"name": "maxRating", "required": false, "in": "query", "description": "最高评分", "schema": {"type": "number"}}, {"name": "hasImage", "required": false, "in": "query", "description": "是否有图片", "schema": {"type": "number"}}, {"name": "orderBy", "required": false, "in": "query", "description": "排序字段", "schema": {"default": "createTime", "type": "string"}}, {"name": "orderType", "required": false, "in": "query", "description": "排序方式 asc/desc", "schema": {"default": "desc", "type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品评价"]}}, "/miniprogram/review/{id}": {"get": {"operationId": "ReviewController_findOne", "summary": "查询评价详情", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品评价"]}, "delete": {"operationId": "ReviewController_remove", "summary": "删除评价", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功"}}, "tags": ["商品评价"]}}, "/miniprogram/review/product/{productId}/stats": {"get": {"operationId": "ReviewController_getProductReviewStats", "summary": "查询商品评价统计", "parameters": [{"name": "productId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功"}}, "tags": ["商品评价"]}}, "/miniprogram/review/{id}/reply": {"put": {"operationId": "ReviewController_reply", "summary": "商家回复评价", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewReplyDto"}}}}, "responses": {"200": {"description": "回复成功"}}, "tags": ["商品评价"]}}, "/miniprogram/review/{id}/like": {"post": {"operationId": "ReviewController_like", "summary": "点赞评价", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "点赞成功"}}, "tags": ["商品评价"]}}, "/miniprogram/favorite": {"post": {"operationId": "FavoriteController_addFavorite", "summary": "添加收藏", "description": "用户添加商品收藏", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFavoriteDto"}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["收藏管理"], "security": [{"bearer": []}]}}, "/miniprogram/favorite/{favoriteId}": {"delete": {"operationId": "FavoriteController_cancelFavorite", "summary": "取消收藏", "description": "用户取消商品收藏", "parameters": [{"name": "favoriteId", "required": true, "in": "path", "description": "收藏ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "取消成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["收藏管理"], "security": [{"bearer": []}]}}, "/miniprogram/favorite/list": {"get": {"operationId": "FavoriteController_getFavoriteList", "summary": "获取收藏列表", "description": "获取用户收藏的商品列表", "parameters": [{"name": "userId", "required": true, "in": "query", "description": "用户ID", "schema": {"example": "1001", "type": "string"}}, {"name": "pageNum", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["收藏管理"], "security": [{"bearer": []}]}}, "/miniprogram/favorite/check/{productId}": {"post": {"operationId": "FavoriteController_checkFavorite", "summary": "检查收藏状态", "description": "检查用户是否已收藏指定商品", "parameters": [{"name": "productId", "required": true, "in": "path", "description": "商品ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["收藏管理"], "security": [{"bearer": []}]}}, "/miniprogram/banner": {"post": {"operationId": "BannerController_create", "summary": "创建轮播图", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBannerDto"}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}}, "/miniprogram/banner/list": {"get": {"operationId": "BannerController_findAll", "summary": "获取轮播图列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "imageUrl", "required": false, "in": "query", "description": "图片URL", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}}, "/miniprogram/banner/{id}": {"get": {"operationId": "BannerController_findOne", "summary": "获取轮播图详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "轮播图ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}, "put": {"operationId": "BannerController_update", "summary": "更新轮播图", "parameters": [{"name": "id", "required": true, "in": "path", "description": "轮播图ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBannerDto"}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}, "delete": {"operationId": "BannerController_remove", "summary": "删除轮播图", "parameters": [{"name": "id", "required": true, "in": "path", "description": "轮播图ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}}, "/miniprogram/banner/mini/list": {"get": {"operationId": "BannerController_getMiniBanners", "summary": "获取小程序端轮播图列表", "parameters": [], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-轮播图管理"]}}, "/miniprogram/iconNav": {"post": {"operationId": "IconNavController_create", "summary": "创建金刚区", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIconNavDto"}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}}, "/miniprogram/iconNav/list": {"get": {"operationId": "IconNavController_findAll", "summary": "获取金刚区列表", "parameters": [{"name": "pageNum", "required": true, "in": "query", "description": "当前分页", "schema": {"default": 1, "type": "number"}}, {"name": "pageSize", "required": true, "in": "query", "description": "每页数量", "schema": {"default": 10, "type": "number"}}, {"name": "orderByColumn", "required": false, "in": "query", "description": "排序字段", "schema": {"type": "string"}}, {"name": "isAsc", "required": false, "in": "query", "description": "排序规则", "schema": {"type": "string"}}, {"name": "name", "required": false, "in": "query", "description": "图标名称", "schema": {"type": "string"}}, {"name": "imageUrl", "required": false, "in": "query", "description": "图片URL", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}}, "/miniprogram/iconNav/{id}": {"get": {"operationId": "IconNavController_findOne", "summary": "获取金刚区详情", "parameters": [{"name": "id", "required": true, "in": "path", "description": "金刚区ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}, "put": {"operationId": "IconNavController_update", "summary": "更新金刚区", "parameters": [{"name": "id", "required": true, "in": "path", "description": "金刚区ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIconNavDto"}}}}, "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}, "delete": {"operationId": "IconNavController_remove", "summary": "删除金刚区", "parameters": [{"name": "id", "required": true, "in": "path", "description": "金刚区ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}}, "/miniprogram/iconNav/mini/list": {"get": {"operationId": "IconNavController_getMiniIconNavs", "summary": "获取小程序端金刚区列表", "parameters": [], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultData"}}}}}, "tags": ["小程序-金刚区管理"]}}}, "info": {"title": "Nest-Admin", "description": "Nest-Admin 接口文档", "version": "2.0.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"token": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginDto": {"type": "object", "properties": {"code": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "uuid": {"type": "string"}}, "required": ["userName", "password", "uuid"]}, "RegisterDto": {"type": "object", "properties": {"code": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "uuid": {"type": "string"}}, "required": ["userName", "password", "uuid"]}, "FileUploadDto": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}, "ChunkMergeFileDto": {"type": "object", "properties": {"uploadId": {"type": "string"}, "fileName": {"type": "string"}}, "required": ["uploadId", "fileName"]}, "CreateConfigDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "DateParamsDTO": {"type": "object", "properties": {}}, "ListConfigDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateConfigDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "CreateDeptDto": {"type": "object", "properties": {"parentId": {"type": "number"}, "deptName": {"type": "string"}, "orderNum": {"type": "number"}, "leader": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "status": {"type": "string"}}, "required": ["parentId", "deptName", "orderNum"]}, "UpdateDeptDto": {"type": "object", "properties": {"parentId": {"type": "number"}, "deptName": {"type": "string"}, "orderNum": {"type": "number"}, "leader": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "status": {"type": "string"}, "deptId": {"type": "number"}}, "required": ["parentId", "deptName", "orderNum"]}, "CreateDictTypeDto": {"type": "object", "properties": {"dictName": {"type": "string"}, "dictType": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["dictName", "dictType", "remark"]}, "UpdateDictTypeDto": {"type": "object", "properties": {"dictName": {"type": "string"}, "dictType": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["dictName", "dictType", "remark"]}, "CreateDictDataDto": {"type": "object", "properties": {}}, "UpdateDictDataDto": {"type": "object", "properties": {}}, "ListDictType": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "CreateMenuDto": {"type": "object", "properties": {"menuName": {"type": "string"}, "orderNum": {"type": "number"}, "parentId": {"type": "number"}, "path": {"type": "string"}, "query": {"type": "string"}, "component": {"type": "string"}, "icon": {"type": "string"}, "menuType": {"type": "string"}, "isCache": {"type": "string"}, "isFrame": {"type": "string"}, "status": {"type": "string"}, "visible": {"type": "string"}, "perms": {"type": "string"}}, "required": ["menuName", "parentId", "isFrame"]}, "UpdateMenuDto": {"type": "object", "properties": {"menuName": {"type": "string"}, "orderNum": {"type": "number"}, "parentId": {"type": "number"}, "path": {"type": "string"}, "query": {"type": "string"}, "component": {"type": "string"}, "icon": {"type": "string"}, "menuType": {"type": "string"}, "isCache": {"type": "string"}, "isFrame": {"type": "string"}, "status": {"type": "string"}, "visible": {"type": "string"}, "perms": {"type": "string"}, "menuId": {"type": "number"}}, "required": ["menuName", "parentId", "isFrame", "menuId"]}, "CreateNoticeDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "ListNoticeDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateNoticeDto": {"type": "object", "properties": {"remark": {"type": "string"}, "status": {"type": "string"}}, "required": ["remark"]}, "CreatePostDto": {"type": "object", "properties": {"remark": {"type": "string"}, "postSort": {"type": "number"}}, "required": ["postSort"]}, "ListPostDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdatePostDto": {"type": "object", "properties": {"remark": {"type": "string"}, "postSort": {"type": "number"}, "postId": {"type": "number"}}, "required": ["postSort", "postId"]}, "CreateRoleDto": {"type": "object", "properties": {"roleName": {"type": "string"}, "roleKey": {"type": "string"}, "roleSort": {"type": "number"}, "status": {"type": "string"}, "dataScope": {"type": "string"}, "remark": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort"]}, "ListRoleDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}}, "required": ["pageNum", "pageSize"]}, "UpdateRoleDto": {"type": "object", "properties": {"roleName": {"type": "string"}, "roleKey": {"type": "string"}, "roleSort": {"type": "number"}, "status": {"type": "string"}, "dataScope": {"type": "string"}, "remark": {"type": "string"}, "roleId": {"type": "number"}}, "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort", "roleId"]}, "ChangeStatusDto": {"type": "object", "properties": {"userId": {"type": "number"}, "status": {"type": "string"}}, "required": ["userId", "status"]}, "AllocatedListDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "userName": {"type": "string"}, "phonenumber": {"type": "string"}, "roleId": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "AuthUserCancelDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userId": {"type": "number"}}, "required": ["roleId", "userId"]}, "AuthUserCancelAllDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userIds": {"type": "string"}}, "required": ["roleId", "userIds"]}, "AuthUserSelectAllDto": {"type": "object", "properties": {"roleId": {"type": "number"}, "userIds": {"type": "string"}}, "required": ["roleId", "userIds"]}, "TableName": {"type": "object", "properties": {"tableNames": {"type": "string"}}, "required": ["tableNames"]}, "GenTableUpdate": {"type": "object", "properties": {"tableId": {"type": "number"}}, "required": ["tableId"]}, "UpdateProfileDto": {"type": "object", "properties": {"nickName": {"type": "string"}, "email": {"type": "string"}, "phonenumber": {"type": "string"}, "sex": {"type": "string"}, "avatar": {"type": "string"}}, "required": ["nick<PERSON><PERSON>", "email", "phonenumber", "sex"]}, "UpdatePwdDto": {"type": "object", "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}}, "required": ["oldPassword", "newPassword"]}, "CreateUserDto": {"type": "object", "properties": {"deptId": {"type": "number"}, "email": {"type": "string"}, "nickName": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "phonenumber": {"type": "string"}, "postIds": {"type": "array", "items": {"type": "string"}}, "roleIds": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}, "sex": {"type": "string"}, "remark": {"type": "string"}, "postSort": {"type": "number"}}, "required": ["nick<PERSON><PERSON>", "userName", "password", "postSort"]}, "UpdateUserDto": {"type": "object", "properties": {"nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "用户头像"}, "gender": {"type": "string", "description": "用户性别：0-未知，1-男，2-女", "enum": ["0", "1", "2"]}, "email": {"type": "string", "description": "用户邮箱"}, "birthday": {"type": "string", "description": "用户生日"}, "region": {"type": "string", "description": "用户地区"}, "tags": {"type": "string", "description": "用户标签"}, "pushEnabled": {"type": "string", "description": "是否接收推送：0-否，1-是", "enum": ["0", "1"]}}}, "ResetPwdDto": {"type": "object", "properties": {"userId": {"type": "number"}, "password": {"type": "string"}}, "required": ["userId", "password"]}, "ListUserDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "deptId": {"type": "string"}, "nickName": {"type": "string"}, "email": {"type": "string"}, "userName": {"type": "string"}, "phonenumber": {"type": "string"}, "status": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "CreateJobDto": {"type": "object", "properties": {"jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "invokeTarget": {"type": "string", "description": "调用目标字符串"}, "cronExpression": {"type": "string", "description": "cron执行表达式"}, "misfirePolicy": {"type": "string", "description": "计划执行错误策略（1立即执行 2执行一次 3放弃执行）"}, "concurrent": {"type": "string", "description": "是否并发执行（0允许 1禁止）"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}, "remark": {"type": "string", "description": "备注信息"}}, "required": ["job<PERSON>ame", "jobGroup", "invoke<PERSON><PERSON><PERSON>", "cronExpression", "misfirePolicy", "concurrent", "status"]}, "ListJobDto": {"type": "object", "properties": {"jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}}, "required": ["job<PERSON>ame", "jobGroup", "status"]}, "ListJobLogDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "jobName": {"type": "string", "description": "任务名称"}, "jobGroup": {"type": "string", "description": "任务组名"}, "status": {"type": "string", "description": "状态（0正常 1暂停）"}}, "required": ["pageNum", "pageSize", "job<PERSON>ame", "jobGroup", "status"]}, "ListLoginlogDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "当前分页", "default": 1}, "pageSize": {"type": "number", "description": "每页数量", "default": 10}, "params": {"description": "时间范围", "allOf": [{"$ref": "#/components/schemas/DateParamsDTO"}]}, "orderByColumn": {"type": "string", "description": "排序字段"}, "isAsc": {"type": "string", "description": "排序规则"}, "ipaddr": {"type": "string"}, "userName": {"type": "string"}, "status": {"type": "string"}}, "required": ["pageNum", "pageSize"]}, "OnlineListDto": {"type": "object", "properties": {"pageNum": {"type": "number"}, "pageSize": {"type": "number"}, "ipaddr": {"type": "string"}, "userName": {"type": "string"}}}, "CreateOperlogDto": {"type": "object", "properties": {}}, "UpdateOperlogDto": {"type": "object", "properties": {}}, "WechatPhoneAuthDto": {"type": "object", "properties": {"code": {"type": "string", "description": "微信获取手机号的动态令牌code"}, "loginCode": {"type": "string", "description": "微信登录code（用于获取openid）"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "用户头像"}, "gender": {"type": "string", "description": "用户性别：0-未知，1-男，2-女", "enum": ["0", "1", "2"]}}, "required": ["code"]}, "UserProfileDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID"}, "openid": {"type": "string", "description": "微信OpenID"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "用户头像"}, "gender": {"type": "string", "description": "用户性别：0-未知，1-男，2-女"}, "phone": {"type": "string", "description": "手机号码"}, "email": {"type": "string", "description": "用户邮箱"}, "birthday": {"format": "date-time", "type": "string", "description": "用户生日"}, "region": {"type": "string", "description": "用户地区"}, "level": {"type": "number", "description": "用户等级"}, "points": {"type": "number", "description": "用户积分"}, "balance": {"type": "number", "description": "用户余额"}, "totalAmount": {"type": "number", "description": "累计消费金额"}, "orderCount": {"type": "number", "description": "订单数量"}, "userType": {"type": "string", "description": "用户类型：0-普通用户，1-VIP用户"}, "vipExpireTime": {"format": "date-time", "type": "string", "description": "VIP到期时间"}, "tags": {"type": "string", "description": "用户标签"}, "pushEnabled": {"type": "string", "description": "是否接收推送：0-否，1-是"}, "createTime": {"format": "date-time", "type": "string", "description": "创建时间"}, "lastLoginTime": {"format": "date-time", "type": "string", "description": "最后登录时间"}}, "required": ["userId", "openid", "nickname", "avatar", "gender", "phone", "email", "birthday", "region", "level", "points", "balance", "totalAmount", "orderCount", "userType", "vipExpireTime", "tags", "pushEnabled", "createTime", "lastLoginTime"]}, "AdminUpdateUserDto": {"type": "object", "properties": {"nickname": {"type": "string", "description": "用户昵称"}, "avatar": {"type": "string", "description": "用户头像"}, "gender": {"type": "string", "description": "用户性别：0-未知，1-男，2-女", "enum": ["0", "1", "2"]}, "email": {"type": "string", "description": "用户邮箱"}, "birthday": {"type": "string", "description": "用户生日"}, "region": {"type": "string", "description": "用户地区"}, "tags": {"type": "string", "description": "用户标签"}, "pushEnabled": {"type": "string", "description": "是否接收推送：0-否，1-是", "enum": ["0", "1"]}, "level": {"type": "number", "description": "用户等级"}, "points": {"type": "number", "description": "用户积分"}, "balance": {"type": "number", "description": "用户余额"}, "userType": {"type": "string", "description": "用户类型：0-普通用户，1-VIP用户", "enum": ["0", "1"]}, "vipExpireTime": {"type": "string", "description": "VIP到期时间"}, "status": {"type": "string", "description": "状态：0-正常，1-停用", "enum": ["0", "1"]}, "remark": {"type": "string", "description": "备注"}}}, "ResultData": {"type": "object", "properties": {"code": {"type": "number", "default": 200}, "msg": {"type": "string", "default": "操作成功"}}, "required": ["code", "msg"]}, "CreateCategoryDto": {"type": "object", "properties": {"name": {"type": "string", "description": "分类名称", "example": "水果"}, "description": {"type": "string", "description": "分类描述", "example": "新鲜水果分类"}, "sortOrder": {"type": "number", "description": "排序", "example": 1}, "status": {"type": "number", "description": "状态：1启用，0禁用", "example": 1}}, "required": ["name"]}, "UpdateCategoryDto": {"type": "object", "properties": {"name": {"type": "string", "description": "分类名称", "example": "水果"}, "description": {"type": "string", "description": "分类描述", "example": "新鲜水果分类"}, "sortOrder": {"type": "number", "description": "排序", "example": 1}, "status": {"type": "number", "description": "状态：1启用，0禁用", "example": 1}}}, "CreateProductSpecDto": {"type": "object", "properties": {"specId": {"type": "number", "description": "规格ID", "example": 1}, "name": {"type": "string", "description": "规格名称", "example": "规格1"}, "price": {"type": "number", "description": "规格价格", "example": 15.99}, "groupBuyPrice": {"type": "number", "description": "团购价格", "example": 12.99}, "value": {"type": "string", "description": "规格值", "example": "500g/袋"}, "stock": {"type": "number", "description": "库存数量", "example": 100}, "image": {"type": "string", "description": "规格图片", "example": "upload_id_1"}, "status": {"type": "number", "description": "状态：1启用，0禁用", "example": 1}, "isDefault": {"type": "number", "description": "是否默认规格：1是，0否", "example": 0}}, "required": ["name", "price", "value", "stock"]}, "CreateProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "商品名称", "example": "新鲜苹果"}, "price": {"type": "number", "description": "商品价格", "example": 15.99}, "description": {"type": "string", "description": "商品描述", "example": "新鲜甜美的苹果"}, "details": {"type": "string", "description": "商品详情", "example": "<p>这是一个富文本</p>"}, "images": {"type": "string", "description": "商品图片URL列表，逗号分隔", "example": "https://example.com/image1.jpg,https://example.com/image2.jpg"}, "categoryId": {"type": "number", "description": "分类ID", "example": 1}, "status": {"type": "number", "description": "状态：1上架，0下架", "example": 1}, "hasMultiSpecs": {"type": "number", "description": "是否多规格商品：1是，0否", "example": 1, "default": 1}, "specs": {"description": "商品规格列表", "type": "array", "items": {"$ref": "#/components/schemas/CreateProductSpecDto"}}, "specList": {"description": "商品规格列表（后端命名）", "type": "array", "items": {"$ref": "#/components/schemas/CreateProductSpecDto"}}}, "required": ["name", "price", "categoryId"]}, "UpdateProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "商品名称", "example": "新鲜苹果"}, "price": {"type": "number", "description": "商品价格", "example": 15.99}, "description": {"type": "string", "description": "商品描述", "example": "新鲜甜美的苹果"}, "details": {"type": "string", "description": "商品详情", "example": "<p>这是一个富文本</p>"}, "images": {"type": "string", "description": "商品图片URL列表，逗号分隔", "example": "https://example.com/image1.jpg,https://example.com/image2.jpg"}, "categoryId": {"type": "number", "description": "分类ID", "example": 1}, "status": {"type": "number", "description": "状态：1上架，0下架", "example": 1}, "hasMultiSpecs": {"type": "number", "description": "是否多规格商品：1是，0否", "example": 0, "default": 1}, "specs": {"description": "商品规格列表（兼容前端命名）", "type": "array", "items": {"$ref": "#/components/schemas/CreateProductSpecDto"}}, "specList": {"description": "商品规格列表", "type": "array", "items": {"$ref": "#/components/schemas/CreateProductSpecDto"}}}}, "UpdateProductSpecDto": {"type": "object", "properties": {"name": {"type": "string", "description": "规格名称", "example": "规格1"}, "price": {"type": "number", "description": "规格价格", "example": 15.99}, "groupBuyPrice": {"type": "number", "description": "团购价格", "example": 12.99}, "value": {"type": "string", "description": "规格值", "example": "500g/袋"}, "stock": {"type": "number", "description": "库存数量", "example": 100}, "image": {"type": "string", "description": "规格图片", "example": "upload_id_1"}, "status": {"type": "number", "description": "状态：1启用，0禁用", "example": 1}, "isDefault": {"type": "number", "description": "是否默认规格：1是，0否", "example": 0}}}, "AddToCartDto": {"type": "object", "properties": {"productId": {"type": "number", "description": "商品ID", "example": 1}, "quantity": {"type": "number", "description": "商品数量", "example": 1, "minimum": 1, "maximum": 999}, "specId": {"type": "number", "description": "规格ID", "example": 0}}, "required": ["productId", "quantity"]}, "UpdateCartDto": {"type": "object", "properties": {"quantity": {"type": "number", "description": "商品数量", "example": 2, "minimum": 1, "maximum": 999}}, "required": ["quantity"]}, "CreateAddressDto": {"type": "object", "properties": {"receiverName": {"type": "string", "description": "收货人姓名", "example": "张三"}, "receiverPhone": {"type": "string", "description": "收货人电话", "example": "13812345678"}, "addressName": {"type": "string", "description": "地址名称", "example": "中关村软件园"}, "detailAddress": {"type": "string", "description": "详细地址", "example": "科技园南区科苑路XX号"}, "areaCode": {"type": "string", "description": "地区编码", "example": "440305"}, "postalCode": {"type": "string", "description": "邮政编码", "example": "518000"}, "isDefault": {"type": "number", "description": "是否设为默认地址", "example": false}, "label": {"type": "string", "description": "地址标签", "example": "家"}, "latitude": {"type": "string", "description": "纬度", "example": 22.5329}, "longitude": {"type": "string", "description": "经度", "example": 114.0544}}, "required": ["<PERSON><PERSON><PERSON>", "receiverPhone", "addressName", "detail<PERSON><PERSON><PERSON>"]}, "UserAddress": {"type": "object", "properties": {"delFlag": {"type": "string", "description": "删除标志"}, "status": {"type": "string", "description": "状态"}, "createBy": {"type": "string", "description": "创建者"}, "addressId": {"type": "number", "description": "地址ID"}, "userId": {"type": "number", "description": "用户ID"}, "receiverName": {"type": "string", "description": "收货人姓名"}, "receiverPhone": {"type": "string", "description": "收货人电话"}, "addressName": {"type": "string", "description": "地址名称"}, "detailAddress": {"type": "string", "description": "详细地址"}, "areaCode": {"type": "string", "description": "地区编码"}, "postalCode": {"type": "string", "description": "邮政编码"}, "isDefault": {"type": "boolean", "description": "是否默认地址"}, "label": {"type": "string", "description": "地址标签"}, "latitude": {"type": "number", "description": "纬度"}, "longitude": {"type": "number", "description": "经度"}}, "required": ["delFlag", "status", "createBy", "addressId", "userId", "<PERSON><PERSON><PERSON>", "receiverPhone", "addressName", "detail<PERSON><PERSON><PERSON>", "areaCode", "postalCode", "isDefault", "label", "latitude", "longitude"]}, "UpdateAddressDto": {"type": "object", "properties": {"receiverName": {"type": "string", "description": "收货人姓名", "example": "张三"}, "receiverPhone": {"type": "string", "description": "收货人电话", "example": "13812345678"}, "addressName": {"type": "string", "description": "地址名称", "example": "中关村软件园"}, "detailAddress": {"type": "string", "description": "详细地址", "example": "科技园南区科苑路XX号"}, "areaCode": {"type": "string", "description": "地区编码", "example": "440305"}, "postalCode": {"type": "string", "description": "邮政编码", "example": "518000"}, "isDefault": {"type": "number", "description": "是否设为默认地址", "example": false}, "label": {"type": "string", "description": "地址标签", "example": "家"}, "latitude": {"type": "string", "description": "纬度", "example": 22.5329}, "longitude": {"type": "string", "description": "经度", "example": 114.0544}}}, "DeliveryRangeDto": {"type": "object", "properties": {"addressName": {"type": "string", "description": "地址名称", "example": "中关村软件园"}, "detailAddress": {"type": "string", "description": "详细地址", "example": "科技园南区科苑路XX号"}, "latitude": {"type": "string", "description": "纬度", "example": 22.5329}, "longitude": {"type": "string", "description": "经度", "example": 114.0544}}, "required": ["addressName", "detail<PERSON><PERSON><PERSON>"]}, "CreateDeliverySettingsDto": {"type": "object", "properties": {"storeName": {"type": "string", "description": "超市名称", "example": "初鲜果味超市"}, "latitude": {"type": "number", "description": "纬度", "example": 22.5329}, "longitude": {"type": "number", "description": "经度", "example": 114.0544}, "deliveryRadius": {"type": "number", "description": "配送半径(km)", "example": 5}}, "required": ["storeName", "latitude", "longitude", "deliveryRadius"]}, "DeliverySettings": {"type": "object", "properties": {"id": {"type": "number", "description": "配送设置ID"}, "storeName": {"type": "string", "description": "超市名称", "example": "初鲜果味超市"}, "latitude": {"type": "number", "description": "纬度", "example": 22.5329}, "longitude": {"type": "number", "description": "经度", "example": 114.0544}, "deliveryRadius": {"type": "number", "description": "配送半径(km)", "example": 5}, "isActive": {"type": "number", "description": "是否启用", "example": 1}, "createTime": {"format": "date-time", "type": "string", "description": "创建时间"}, "updateTime": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "storeName", "latitude", "longitude", "deliveryRadius", "isActive", "createTime", "updateTime"]}, "UpdateDeliverySettingsDto": {"type": "object", "properties": {"storeName": {"type": "string", "description": "超市名称", "example": "初鲜果味超市"}, "latitude": {"type": "number", "description": "纬度", "example": 22.5329}, "longitude": {"type": "number", "description": "经度", "example": 114.0544}, "deliveryRadius": {"type": "number", "description": "配送半径(km)", "example": 5}}}, "CreateDeliveryTimeSlotDto": {"type": "object", "properties": {"label": {"type": "string", "description": "配送时间段", "example": "上午9:00-12:00"}, "value": {"type": "string", "description": "配送时间", "example": "10:00:00"}, "sortOrder": {"type": "number", "description": "排序", "example": 1}, "isActive": {"type": "number", "description": "是否启用", "example": 1}}, "required": ["label", "value"]}, "DeliveryTimeSlot": {"type": "object", "properties": {"id": {"type": "number", "description": "时间段ID"}, "label": {"type": "string", "description": "配送时间段", "example": "上午9:00-12:00"}, "value": {"type": "string", "description": "配送时间", "example": "10:00:00"}, "sortOrder": {"type": "number", "description": "排序", "example": 1}, "isActive": {"type": "number", "description": "是否启用", "example": 1}, "createTime": {"format": "date-time", "type": "string", "description": "创建时间"}, "updateTime": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "label", "value", "sortOrder", "isActive", "createTime", "updateTime"]}, "DeliveryTimeSlotQueryDto": {"type": "object", "properties": {"pageNum": {"type": "number", "description": "页码", "example": 1}, "pageSize": {"type": "number", "description": "每页条数", "example": 10}, "label": {"type": "string", "description": "配送时间段"}, "isActive": {"type": "number", "description": "是否启用", "example": 1}}}, "UpdateDeliveryTimeSlotDto": {"type": "object", "properties": {"label": {"type": "string", "description": "配送时间段", "example": "上午9:00-12:00"}, "value": {"type": "string", "description": "配送时间", "example": "10:00:00"}, "sortOrder": {"type": "number", "description": "排序", "example": 1}, "isActive": {"type": "number", "description": "是否启用", "example": 1}}}, "CreateOrderItemDto": {"type": "object", "properties": {"productId": {"type": "number", "description": "商品ID", "example": 1}, "quantity": {"type": "number", "description": "商品数量", "example": 2}, "specId": {"type": "number", "description": "规格ID", "example": 0}, "cartId": {"type": "number", "description": "购物车项ID", "example": 0}}, "required": ["productId", "quantity"]}, "DeliveryType": {"type": "string", "description": "配送方式", "enum": ["1", "2"]}, "CreateOrderDto": {"type": "object", "properties": {"addressId": {"type": "number", "description": "收货地址ID", "example": 1}, "items": {"description": "订单商品列表", "example": [{"productId": 1, "quantity": 2, "specId": 0}], "type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemDto"}}, "deliveryType": {"example": "1", "$ref": "#/components/schemas/DeliveryType"}, "deliveryTime": {"type": "string", "description": "预约配送时间", "example": "2024-06-20 14:00:00"}, "couponId": {"type": "number", "description": "使用的优惠券ID", "example": 1}, "remark": {"type": "string", "description": "订单备注", "example": "请尽快发货"}, "isUserInitiatedGroupBuy": {"type": "boolean", "description": "是否是用户发起的团购", "example": false}, "userGroupBuyId": {"type": "string", "description": "参与的拼团ID（参团时必填）", "example": "a1b2c3d4e5f6"}}, "required": ["addressId", "items", "deliveryType"]}, "OrderStatus": {"type": "string", "description": "订单状态", "enum": ["1", "2", "3", "4", "5", "6"]}, "UpdateOrderDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "orderId": {"type": "string", "description": "订单ID", "example": "ORDER_202401010001"}, "status": {"example": "2", "$ref": "#/components/schemas/OrderStatus"}, "cancelReason": {"type": "string", "description": "取消原因（取消订单时必填）", "example": "用户主动取消"}}, "required": ["userId", "orderId", "status"]}, "CreateRefundRequestDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "orderId": {"type": "string", "description": "订单ID", "example": "ORD20240101001"}, "refundReason": {"type": "string", "description": "退款原因", "example": "商品质量问题"}, "refundAmount": {"type": "number", "description": "申请退款金额", "example": 99.99}}, "required": ["userId", "orderId", "refundReason", "refundAmount"]}, "ConfirmRefundPickupDto": {"type": "object", "properties": {"orderId": {"type": "string", "description": "订单ID", "example": "ORD20240101001"}, "deliveryStaffId": {"type": "number", "description": "配送员ID", "example": 1}}, "required": ["orderId"]}, "PaymentMethod": {"type": "string", "description": "支付方式", "enum": ["1", "2"]}, "PaymentRequestDto": {"type": "object", "properties": {"orderId": {"type": "string", "description": "订单ID", "example": "ORDER_20241215_001"}, "paymentMethod": {"example": "1", "$ref": "#/components/schemas/PaymentMethod"}, "description": {"type": "string", "description": "支付描述", "example": "初鲜果味商品订单支付"}}, "required": ["orderId", "paymentMethod"]}, "BalanceRechargeDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "充值金额", "example": 100}, "description": {"type": "string", "description": "支付描述", "example": "余额充值"}}, "required": ["amount"]}, "UserBalanceRechargeDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "amount": {"type": "number", "description": "充值金额", "example": 100}, "remark": {"type": "string", "description": "备注", "example": "后台管理员充值"}}, "required": ["userId", "amount"]}, "ResourceDto": {"type": "object", "properties": {"algorithm": {"type": "string", "description": "加密算法", "example": "AEAD_AES_256_GCM"}, "associated_data": {"type": "string", "description": "附加数据", "example": ""}, "ciphertext": {"type": "string", "description": "数据密文", "example": ""}, "nonce": {"type": "string", "description": "随机串", "example": ""}, "original_type": {"type": "string", "description": "原始类型", "example": "transaction"}}, "required": ["algorithm", "ciphertext", "nonce", "original_type"]}, "PaymentNotifyDto": {"type": "object", "properties": {"id": {"type": "string", "description": "通知ID", "example": "55d390be-f1cf-51e2-82eb-08f1ddf442fd"}, "create_time": {"type": "string", "description": "通知创建时间", "example": "2025-07-12T18:07:12+08:00"}, "resource_type": {"type": "string", "description": "通知类型", "example": "encrypt-resource"}, "event_type": {"type": "string", "description": "回调事件类型", "example": "TRANSACTION.SUCCESS"}, "summary": {"type": "string", "description": "回调摘要", "example": "支付成功"}, "resource": {"description": "通知数据", "allOf": [{"$ref": "#/components/schemas/ResourceDto"}]}}, "required": ["id", "create_time", "resource_type", "event_type", "summary", "resource"]}, "RefundRequestDto": {"type": "object", "properties": {"paymentId": {"type": "number", "description": "支付记录ID", "example": 1}, "refundAmount": {"type": "number", "description": "退款金额", "example": 50}, "refundReason": {"type": "string", "description": "退款原因", "example": "用户主动退款"}}, "required": ["paymentId", "refundAmount", "refundReason"]}, "CreateCouponDto": {"type": "object", "properties": {"name": {"type": "string", "description": "优惠券名称", "example": "新人专享满100减10元"}, "type": {"type": "string", "description": "优惠券类型 1满减券 2折扣券 3无门槛券", "example": "1"}, "status": {"type": "string", "description": "优惠券状态 0无效 1有效", "example": "1", "default": "1"}, "description": {"type": "string", "description": "优惠券描述", "example": "仅限生鲜水果类商品使用"}, "conditionAmount": {"type": "number", "description": "满减条件金额(分)", "example": 10000, "default": 0}, "discountAmount": {"type": "number", "description": "优惠金额(分)，满减券和无门槛券必填", "example": 1000}, "discountRate": {"type": "number", "description": "折扣率(0-100)，折扣券必填", "example": 80}, "goodsType": {"type": "string", "description": "可使用商品范围 1全部商品 2指定商品 3指定商品类别", "example": "1", "default": "1"}, "goodsIds": {"type": "string", "description": "商品ID列表，当goodsType=2时必填", "example": "1,2,3"}, "categoryIds": {"type": "string", "description": "商品类别ID列表，当goodsType=3时必填", "example": "1,2"}, "startTime": {"format": "date-time", "type": "string", "description": "有效期开始时间", "example": "2024-07-01 00:00:00"}, "endTime": {"format": "date-time", "type": "string", "description": "有效期结束时间", "example": "2024-07-31 23:59:59"}, "totalCount": {"type": "number", "description": "优惠券总数量，0表示无限制", "example": 100, "default": 0}, "perUserLimit": {"type": "number", "description": "每人限领数量", "example": 1, "default": 1}, "remark": {"type": "string", "description": "备注", "example": "双11活动优惠券"}, "distributeType": {"type": "string", "description": "发放方式 1公开领取 2指定发放", "example": "1", "default": "1"}}, "required": ["name", "type", "status", "conditionAmount", "goodsType", "startTime", "endTime", "totalCount", "perUserLimit", "distributeType"]}, "CouponResponse": {"type": "object", "properties": {"code": {"type": "number", "description": "状态码", "example": 200}, "message": {"type": "string", "description": "消息", "example": "操作成功"}, "data": {"type": "object", "description": "数据", "example": {}}, "total": {"type": "number", "description": "总数", "example": 0}}, "required": ["code", "message", "data"]}, "UpdateCouponDto": {"type": "object", "properties": {"couponId": {"type": "number", "description": "优惠券ID", "example": 1}, "name": {"type": "string", "description": "优惠券名称", "example": "新人专享满100减10元"}, "status": {"type": "string", "description": "优惠券状态 0无效 1有效", "example": "1"}, "description": {"type": "string", "description": "优惠券描述", "example": "仅限生鲜水果类商品使用"}, "conditionAmount": {"type": "number", "description": "满减条件金额(分)", "example": 10000}, "discountAmount": {"type": "number", "description": "优惠金额(分)，满减券和无门槛券使用", "example": 1000}, "discountRate": {"type": "number", "description": "折扣率(0-100)，折扣券使用", "example": 80}, "goodsType": {"type": "string", "description": "可使用商品范围 1全部商品 2指定商品 3指定商品类别", "example": "1"}, "goodsIds": {"type": "string", "description": "商品ID列表，当goodsType=2时使用", "example": "1,2,3"}, "categoryIds": {"type": "string", "description": "商品类别ID列表，当goodsType=3时使用", "example": "1,2"}, "startTime": {"format": "date-time", "type": "string", "description": "有效期开始时间", "example": "2024-07-01 00:00:00"}, "endTime": {"format": "date-time", "type": "string", "description": "有效期结束时间", "example": "2024-07-31 23:59:59"}, "totalCount": {"type": "number", "description": "优惠券总数量，0表示无限制", "example": 100}, "perUserLimit": {"type": "number", "description": "每人限领数量", "example": 1}, "remark": {"type": "string", "description": "备注", "example": "双11活动优惠券"}, "distributeType": {"type": "string", "description": "发放方式 1公开领取 2指定发放", "example": "1"}}, "required": ["couponId"]}, "ReceiveCouponDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "couponId": {"type": "number", "description": "优惠券ID", "example": 1}}, "required": ["userId", "couponId"]}, "UseCouponDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "userCouponId": {"type": "number", "description": "用户优惠券ID", "example": 1}, "orderId": {"type": "string", "description": "订单ID", "example": "ORDER2023112800001"}}, "required": ["userId", "userCouponId", "orderId"]}, "DistributeCouponDto": {"type": "object", "properties": {"couponId": {"type": "number", "description": "优惠券ID", "example": 1}, "userIds": {"description": "用户ID列表", "example": [1, 2, 3], "type": "array", "items": {"type": "string"}}, "quantity": {"type": "number", "description": "每个用户发放数量", "example": 1, "default": 1}}, "required": ["couponId", "userIds", "quantity"]}, "CreateReviewDto": {"type": "object", "properties": {"userId": {"type": "number", "description": "用户ID", "example": 1}, "productId": {"type": "number", "description": "商品ID", "example": 1}, "orderId": {"type": "string", "description": "订单ID", "example": "ORD123456"}, "specId": {"type": "number", "description": "商品规格ID", "example": 1}, "rating": {"type": "number", "description": "评分 1-5", "example": 5}, "content": {"type": "string", "description": "评价内容", "example": "水果非常新鲜，口感很好！"}, "images": {"description": "评价图片URL数组", "example": ["http://example.com/image1.jpg"], "type": "array", "items": {"type": "string"}}, "isAnonymous": {"type": "string", "description": "是否匿名评价，0-否 1-是", "example": "0"}}, "required": ["userId", "productId", "orderId", "rating", "content", "isAnonymous"]}, "ReviewReplyDto": {"type": "object", "properties": {"reply": {"type": "string", "description": "回复内容", "example": "感谢您的评价，我们会继续努力提供更好的服务！"}}, "required": ["reply"]}, "CreateFavoriteDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户ID", "example": "1001"}, "productId": {"type": "number", "description": "商品ID", "example": 1}}, "required": ["userId", "productId"]}, "CreateBannerDto": {"type": "object", "properties": {"imageUrl": {"type": "string", "description": "图片URL", "example": "https://example.com/image.jpg"}, "sortOrder": {"type": "number", "description": "排序顺序", "example": 0}, "linkUrl": {"type": "string", "description": "链接URL", "example": "https://example.com"}}, "required": ["imageUrl", "sortOrder"]}, "UpdateBannerDto": {"type": "object", "properties": {"imageUrl": {"type": "string", "description": "图片URL", "example": "https://example.com/image.jpg"}, "sortOrder": {"type": "number", "description": "排序顺序", "example": 0}, "linkUrl": {"type": "string", "description": "链接URL", "example": "https://example.com"}}}, "CreateIconNavDto": {"type": "object", "properties": {"name": {"type": "string", "description": "图标名称", "example": "新鲜水果"}, "imageUrl": {"type": "string", "description": "图片URL", "example": "https://example.com/image.jpg"}, "sortOrder": {"type": "number", "description": "排序顺序", "example": 0}, "linkUrl": {"type": "string", "description": "链接URL", "example": "https://example.com"}}, "required": ["name", "imageUrl", "sortOrder"]}, "UpdateIconNavDto": {"type": "object", "properties": {"name": {"type": "string", "description": "图标名称", "example": "新鲜水果"}, "imageUrl": {"type": "string", "description": "图片URL", "example": "https://example.com/image.jpg"}, "sortOrder": {"type": "number", "description": "排序顺序", "example": 0}, "linkUrl": {"type": "string", "description": "链接URL", "example": "https://example.com"}}}}}}