"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListConfigDto = exports.UpdateConfigDto = exports.CreateConfigDto = exports.TypeEnum = exports.StatusEnum = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const index_1 = require("../../../../common/dto/index");
var StatusEnum;
(function (StatusEnum) {
    StatusEnum["STATIC"] = "0";
    StatusEnum["DYNAMIC"] = "1";
})(StatusEnum || (exports.StatusEnum = StatusEnum = {}));
var TypeEnum;
(function (TypeEnum) {
    TypeEnum["YES"] = "Y";
    TypeEnum["NO"] = "N";
})(TypeEnum || (exports.TypeEnum = TypeEnum = {}));
class CreateConfigDto {
}
exports.CreateConfigDto = CreateConfigDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 100),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "configName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 500),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "configValue", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 100),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "configKey", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(TypeEnum),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 500),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(StatusEnum),
    __metadata("design:type", String)
], CreateConfigDto.prototype, "status", void 0);
class UpdateConfigDto extends CreateConfigDto {
}
exports.UpdateConfigDto = UpdateConfigDto;
__decorate([
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateConfigDto.prototype, "configId", void 0);
class ListConfigDto extends index_1.PagingDto {
}
exports.ListConfigDto = ListConfigDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 100),
    __metadata("design:type", String)
], ListConfigDto.prototype, "configName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 100),
    __metadata("design:type", String)
], ListConfigDto.prototype, "configKey", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(TypeEnum),
    __metadata("design:type", String)
], ListConfigDto.prototype, "configType", void 0);
//# sourceMappingURL=index.js.map