"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MainService = void 0;
const common_1 = require("@nestjs/common");
const result_1 = require("../../common/utils/result");
const result_2 = require("../../common/utils/result");
const user_service_1 = require("../system/user/user.service");
const loginlog_service_1 = require("../monitor/loginlog/loginlog.service");
const axios_service_1 = require("../common/axios/axios.service");
const menu_service_1 = require("../system/menu/menu.service");
let MainService = class MainService {
    constructor(userService, loginlogService, axiosService, menuService) {
        this.userService = userService;
        this.loginlogService = loginlogService;
        this.axiosService = axiosService;
        this.menuService = menuService;
    }
    async login(user, clientInfo) {
        const loginLog = {
            ...clientInfo,
            status: '0',
            msg: '',
        };
        try {
            const loginLocation = await this.axiosService.getIpAddress(clientInfo.ipaddr);
            loginLog.loginLocation = loginLocation;
        }
        catch (error) { }
        const loginRes = await this.userService.login(user, loginLog);
        loginLog.status = loginRes.code === result_2.SUCCESS_CODE ? '0' : '1';
        loginLog.msg = loginRes.msg;
        this.loginlogService.create(loginLog);
        return loginRes;
    }
    async logout(clientInfo) {
        const loginLog = {
            ...clientInfo,
            status: '0',
            msg: '退出成功',
        };
        try {
            const loginLocation = await this.axiosService.getIpAddress(clientInfo.ipaddr);
            loginLog.loginLocation = loginLocation;
        }
        catch (error) { }
        this.loginlogService.create(loginLog);
        return result_1.ResultData.ok();
    }
    async register(user) {
        return await this.userService.register(user);
    }
    loginRecord() { }
    async getRouters(userId) {
        const menus = await this.menuService.getMenuListByUserId(userId);
        return result_1.ResultData.ok(menus);
    }
};
exports.MainService = MainService;
exports.MainService = MainService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        loginlog_service_1.LoginlogService,
        axios_service_1.AxiosService,
        menu_service_1.MenuService])
], MainService);
//# sourceMappingURL=main.service.js.map