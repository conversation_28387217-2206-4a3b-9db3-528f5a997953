import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { OperlogService } from 'src/module/monitor/operlog/operlog.service';
export declare class OperlogInterceptor implements NestInterceptor {
    readonly logService: OperlogService;
    private readonly reflector;
    constructor(logService: OperlogService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
