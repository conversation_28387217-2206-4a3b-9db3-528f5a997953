"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenConstants = void 0;
class GenConstants {
}
exports.GenConstants = GenConstants;
GenConstants.TPL_CRUD = 'crud';
GenConstants.TPL_TREE = 'tree';
GenConstants.TPL_SUB = 'sub';
GenConstants.TREE_CODE = 'treeCode';
GenConstants.TREE_PARENT_CODE = 'treeParentCode';
GenConstants.TREE_NAME = 'treeName';
GenConstants.PARENT_MENU_ID = 'parentMenuId';
GenConstants.PARENT_MENU_NAME = 'parentMenuName';
GenConstants.COLUMNTYPE_STR = ['char', 'varchar', 'nvarchar', 'varchar2'];
GenConstants.COLUMNTYPE_TEXT = ['tinytext', 'text', 'mediumtext', 'longtext'];
GenConstants.COLUMNTYPE_TIME = ['datetime', 'time', 'date', 'timestamp'];
GenConstants.COLUMNTYPE_NUMBER = ['tinyint', 'smallint', 'mediumint', 'int', 'number', 'integer', 'bit', 'bigint', 'float', 'double', 'decimal'];
GenConstants.COLUMNNAME_NOT_INSERT = ['id', 'create_by', 'create_time', 'del_flag'];
GenConstants.COLUMNNAME_NOT_EDIT = ['id', 'create_by', 'create_time', 'del_flag'];
GenConstants.COLUMNNAME_NOT_LIST = ['id', 'create_by', 'create_time', 'del_flag', 'update_by', 'update_time'];
GenConstants.COLUMNNAME_NOT_QUERY = ['id', 'create_by', 'create_time', 'del_flag', 'update_by', 'update_time', 'remark'];
GenConstants.BASE_ENTITY = ['createBy', 'createTime', 'updateBy', 'updateTime', 'remark'];
GenConstants.TREE_ENTITY = ['parentName', 'parentId', 'orderNum', 'ancestors', 'children'];
GenConstants.HTML_INPUT = 'input';
GenConstants.HTML_TEXTAREA = 'textarea';
GenConstants.HTML_SELECT = 'select';
GenConstants.HTML_RADIO = 'radio';
GenConstants.HTML_CHECKBOX = 'checkbox';
GenConstants.HTML_DATETIME = 'datetime';
GenConstants.HTML_IMAGE_UPLOAD = 'imageUpload';
GenConstants.HTML_FILE_UPLOAD = 'fileUpload';
GenConstants.HTML_EDITOR = 'editor';
GenConstants.TYPE_STRING = 'String';
GenConstants.TYPE_INTEGER = 'Integer';
GenConstants.TYPE_LONG = 'Long';
GenConstants.TYPE_DOUBLE = 'Double';
GenConstants.TYPE_BIGDECIMAL = 'BigDecimal';
GenConstants.TYPE_DATE = 'Date';
GenConstants.QUERY_EQ = 'EQ';
GenConstants.QUERY_NE = 'NE';
GenConstants.QUERY_GT = 'GT';
GenConstants.QUERY_GTE = 'GTE';
GenConstants.QUERY_LT = 'LT';
GenConstants.QUERY_LTE = 'LTE';
GenConstants.QUERY_LIKE = 'LIKE';
GenConstants.QUERY_BETWEEN = 'BETWEEN';
GenConstants.REQUIRE = '1';
GenConstants.NOT_REQUIRE = '0';
GenConstants.TYPE_NUMBER = 'Number';
//# sourceMappingURL=gen.constant.js.map