"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateIconNavDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateIconNavDto {
}
exports.UpdateIconNavDto = UpdateIconNavDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图标名称', example: '新鲜水果', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '图标名称必须是字符串' }),
    __metadata("design:type", String)
], UpdateIconNavDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL', example: 'https://example.com/image.jpg', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '图片URL必须是字符串' }),
    __metadata("design:type", String)
], UpdateIconNavDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序', example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序顺序必须是数字' }),
    __metadata("design:type", Number)
], UpdateIconNavDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '链接URL', example: 'https://example.com', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '链接URL必须是字符串' }),
    __metadata("design:type", String)
], UpdateIconNavDto.prototype, "linkUrl", void 0);
//# sourceMappingURL=update-iconNav.dto.js.map