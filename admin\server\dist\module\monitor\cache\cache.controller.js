"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cache_service_1 = require("./cache.service");
let CacheController = class CacheController {
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    getInfo() {
        return this.cacheService.getInfo();
    }
    getNames() {
        return this.cacheService.getNames();
    }
    getKeys(id) {
        return this.cacheService.getKeys(id);
    }
    getValue(params) {
        return this.cacheService.getValue(params);
    }
    clearCacheName(cacheName) {
        return this.cacheService.clearCacheName(cacheName);
    }
    clearCacheKey(cacheKey) {
        return this.cacheService.clearCacheKey(cacheKey);
    }
    clearCacheAll() {
        return this.cacheService.clearCacheAll();
    }
};
exports.CacheController = CacheController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '缓存监控信息',
    }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getInfo", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '缓存列表',
    }),
    (0, common_1.Get)('/getNames'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getNames", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '键名列表',
    }),
    (0, common_1.Get)('/getKeys/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getKeys", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '缓存内容',
    }),
    (0, common_1.Get)('/getValue/:cacheName/:cacheKey'),
    __param(0, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getValue", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '清理缓存名称',
    }),
    (0, common_1.Delete)('/clearCacheName/:cacheName'),
    __param(0, (0, common_1.Param)('cacheName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "clearCacheName", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '清理缓存键名',
    }),
    (0, common_1.Delete)('/clearCacheKey/:cacheKey'),
    __param(0, (0, common_1.Param)('cacheKey')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "clearCacheKey", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '清理全部',
    }),
    (0, common_1.Delete)('/clearCacheAll'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "clearCacheAll", null);
exports.CacheController = CacheController = __decorate([
    (0, swagger_1.ApiTags)('缓存管理'),
    (0, common_1.Controller)('monitor/cache'),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], CacheController);
//# sourceMappingURL=cache.controller.js.map