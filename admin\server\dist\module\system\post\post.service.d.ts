import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { SysPostEntity } from './entities/post.entity';
import { Response } from 'express';
import { CreatePostDto, UpdatePostDto, ListPostDto } from './dto/index';
export declare class PostService {
    private readonly sysPostEntityRep;
    constructor(sysPostEntityRep: Repository<SysPostEntity>);
    create(createPostDto: CreatePostDto): Promise<ResultData>;
    findAll(query: ListPostDto): Promise<ResultData>;
    findOne(postId: number): Promise<ResultData>;
    update(updatePostDto: UpdatePostDto): Promise<ResultData>;
    remove(postIds: string[]): Promise<ResultData>;
    export(res: Response, body: ListPostDto): Promise<void>;
}
