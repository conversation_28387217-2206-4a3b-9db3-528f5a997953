"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const common_1 = require("@nestjs/common");
const upload_service_1 = require("./upload.service");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const index_1 = require("./dto/index");
const result_1 = require("../../common/utils/result");
let UploadController = class UploadController {
    constructor(uploadService) {
        this.uploadService = uploadService;
    }
    async singleFileUpload(file) {
        const res = await this.uploadService.singleFileUpload(file);
        return result_1.ResultData.ok(res);
    }
    getChunkUploadId() {
        return this.uploadService.getChunkUploadId();
    }
    chunkFileUpload(file, body) {
        return this.uploadService.chunkFileUpload(file, body);
    }
    chunkMergeFile(body) {
        return this.uploadService.chunkMergeFile(body);
    }
    getChunkUploadResult(query) {
        return this.uploadService.getChunkUploadResult(query.uploadId);
    }
    getAuthorization(query) {
        return this.uploadService.getAuthorization(query.key);
    }
    async deleteFile(fileUrl) {
        const result = await this.uploadService.deleteFile(fileUrl);
        return result_1.ResultData.ok({ success: result });
    }
};
exports.UploadController = UploadController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '文件上传',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.FileUploadDto,
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "singleFileUpload", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取切片上传任务Id',
    }),
    (0, swagger_1.ApiBody)({
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Get)('/chunk/uploadId'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UploadController.prototype, "getChunkUploadId", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '文件切片上传',
    }),
    (0, swagger_1.ApiBody)({
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Post)('/chunk'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ChunkFileDto]),
    __metadata("design:returntype", void 0)
], UploadController.prototype, "chunkFileUpload", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '合并切片',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ChunkMergeFileDto,
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Post)('/chunk/merge'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ChunkMergeFileDto]),
    __metadata("design:returntype", void 0)
], UploadController.prototype, "chunkMergeFile", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取切片上传结果',
    }),
    (0, swagger_1.ApiQuery)({
        type: index_1.uploadIdDto,
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Get)('/chunk/result'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UploadController.prototype, "getChunkUploadResult", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取cos上传密钥',
    }),
    (0, swagger_1.ApiBody)({
        required: true,
    }),
    (0, common_1.Get)('/cos/authorization'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UploadController.prototype, "getAuthorization", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '删除文件',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'fileUrl',
        description: '文件URL',
        required: true,
    }),
    (0, common_1.HttpCode)(200),
    (0, common_1.Get)('/delete'),
    __param(0, (0, common_1.Query)('fileUrl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "deleteFile", null);
exports.UploadController = UploadController = __decorate([
    (0, swagger_1.ApiTags)('通用-文件上传'),
    (0, common_1.Controller)('common/upload'),
    __metadata("design:paramtypes", [upload_service_1.UploadService])
], UploadController);
//# sourceMappingURL=upload.controller.js.map