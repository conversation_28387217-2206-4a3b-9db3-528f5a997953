"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FootprintModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const footprint_controller_1 = require("./footprint.controller");
const footprint_service_1 = require("./footprint.service");
const footprint_entity_1 = require("./entities/footprint.entity");
const product_entity_1 = require("../product/entities/product.entity");
const upload_entity_1 = require("../../upload/entities/upload.entity");
let FootprintModule = class FootprintModule {
};
exports.FootprintModule = FootprintModule;
exports.FootprintModule = FootprintModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([footprint_entity_1.Footprint, product_entity_1.ProductEntity, upload_entity_1.SysUploadEntity])],
        controllers: [footprint_controller_1.FootprintController],
        providers: [footprint_service_1.FootprintService],
        exports: [footprint_service_1.FootprintService],
    })
], FootprintModule);
//# sourceMappingURL=footprint.module.js.map