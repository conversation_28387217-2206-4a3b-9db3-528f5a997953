"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BannerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const banner_entity_1 = require("./entities/banner.entity");
const upload_service_1 = require("../../upload/upload.service");
let BannerService = BannerService_1 = class BannerService {
    constructor(bannerRepository, uploadService) {
        this.bannerRepository = bannerRepository;
        this.uploadService = uploadService;
        this.logger = new common_1.Logger(BannerService_1.name);
    }
    async create(createBannerDto, userId) {
        this.logger.log(`创建轮播图: ${JSON.stringify(createBannerDto)}`);
        const banner = this.bannerRepository.create({
            ...createBannerDto,
            createBy: userId,
            updateBy: userId,
        });
        return await this.bannerRepository.save(banner);
    }
    async findAll(queryParams) {
        this.logger.log(`查询轮播图列表: ${JSON.stringify(queryParams)}`);
        const { pageNum, pageSize, imageUrl } = queryParams;
        const skip = (pageNum - 1) * pageSize;
        const whereConditions = {};
        if (imageUrl) {
            whereConditions.imageUrl = (0, typeorm_2.Like)(`%${imageUrl}%`);
        }
        const [items, count] = await this.bannerRepository.findAndCount({
            where: whereConditions,
            order: { sortOrder: 'ASC', createTime: 'DESC' },
            skip,
            take: pageSize,
        });
        return {
            rows: items,
            total: count,
            pageNum: Number(pageNum),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(count / pageSize),
        };
    }
    async findOne(bannerId) {
        this.logger.log(`查询轮播图详情: ${bannerId}`);
        return await this.bannerRepository.findOne({ where: { bannerId } });
    }
    async update(bannerId, updateBannerDto, userId) {
        this.logger.log(`更新轮播图: ${bannerId}, ${JSON.stringify(updateBannerDto)}`);
        const banner = await this.bannerRepository.findOne({ where: { bannerId } });
        if (!banner) {
            this.logger.warn(`轮播图不存在: ${bannerId}`);
            return null;
        }
        if (updateBannerDto.imageUrl && updateBannerDto.imageUrl !== banner.imageUrl) {
            this.logger.log(`轮播图图片已更新，删除旧图片: ${banner.imageUrl}`);
            const deleteResult = await this.uploadService.deleteFile(banner.imageUrl);
            this.logger.log(`旧图片删除结果: ${deleteResult ? '成功' : '失败'}`);
        }
        Object.assign(banner, updateBannerDto, { updateBy: userId });
        return await this.bannerRepository.save(banner);
    }
    async remove(bannerId, userId) {
        this.logger.log(`删除轮播图: ${bannerId}`);
        const banner = await this.bannerRepository.findOne({ where: { bannerId } });
        if (!banner) {
            this.logger.warn(`轮播图不存在: ${bannerId}`);
            return false;
        }
        if (banner.imageUrl) {
            this.logger.log(`删除轮播图图片: ${banner.imageUrl}`);
            const deleteResult = await this.uploadService.deleteFile(banner.imageUrl);
            this.logger.log(`图片删除结果: ${deleteResult ? '成功' : '失败'}`);
        }
        await this.bannerRepository.delete(bannerId);
        this.logger.log(`轮播图数据已硬删除: ${bannerId}`);
        return true;
    }
    async getMiniBanners() {
        this.logger.log('获取小程序端轮播图列表');
        return await this.bannerRepository.find({
            order: { sortOrder: 'ASC' },
        });
    }
};
exports.BannerService = BannerService;
exports.BannerService = BannerService = BannerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(banner_entity_1.BannerEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        upload_service_1.UploadService])
], BannerService);
//# sourceMappingURL=banner.service.js.map