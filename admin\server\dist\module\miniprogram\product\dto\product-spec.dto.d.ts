export declare class CreateProductSpecDto {
    specId?: number;
    name: string;
    price: number;
    groupBuyPrice?: number;
    value: string;
    stock: number;
    image?: string;
    status?: number;
    isDefault?: number;
}
export declare class UpdateProductSpecDto {
    name?: string;
    price?: number;
    groupBuyPrice?: number;
    value?: string;
    stock?: number;
    image?: string;
    status?: number;
    isDefault?: number;
}
export declare class ProductSpecResponseDto {
    specId: number;
    productId: number;
    name: string;
    price: number;
    groupBuyPrice: number | null;
    value: string;
    stock: number;
    image: string;
    status: number;
    isDefault: number;
    createTime: Date;
    updateTime: Date;
}
