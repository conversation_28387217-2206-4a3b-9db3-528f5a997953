import { UserType } from 'src/module/system/user/dto/user';
export declare const User: (...dataOrPipes: unknown[]) => ParameterDecorator;
export type UserDto = UserType;
export declare const NotRequireAuth: () => import("@nestjs/common").CustomDecorator<string>;
export declare const UserTool: (...dataOrPipes: unknown[]) => ParameterDecorator;
export type UserToolType = {
    injectCreate: <T extends {
        [key: string]: any;
    }>(data: T) => T & {
        createBy?: string;
    };
    injectUpdate: <T extends {
        [key: string]: any;
    }>(data: T) => T & {
        updateBy?: string;
    };
};
