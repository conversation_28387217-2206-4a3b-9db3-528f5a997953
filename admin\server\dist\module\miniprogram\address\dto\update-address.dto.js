"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryRangeDto = exports.AddressQueryDto = exports.SetDefaultAddressDto = exports.UpdateAddressDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const create_address_dto_1 = require("./create-address.dto");
class UpdateAddressDto extends (0, swagger_1.PartialType)(create_address_dto_1.CreateAddressDto) {
}
exports.UpdateAddressDto = UpdateAddressDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人姓名', example: '张三', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '收货人姓名必须是字符串' }),
    (0, class_validator_1.Length)(2, 50, { message: '收货人姓名长度为2-50个字符' }),
    __metadata("design:type", String)
], UpdateAddressDto.prototype, "receiverName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人电话', example: '13812345678', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '收货人电话必须是字符串' }),
    (0, class_validator_1.Length)(11, 20, { message: '请输入正确的手机号码' }),
    __metadata("design:type", String)
], UpdateAddressDto.prototype, "receiverPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '地址名称', example: '中关村软件园', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '地址名称必须是字符串' }),
    (0, class_validator_1.Length)(2, 100, { message: '地址名称长度为2-100个字符' }),
    __metadata("design:type", String)
], UpdateAddressDto.prototype, "addressName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '详细地址', example: '科技园南区科苑路XX号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '详细地址必须是字符串' }),
    (0, class_validator_1.Length)(5, 200, { message: '详细地址长度为5-200个字符' }),
    __metadata("design:type", String)
], UpdateAddressDto.prototype, "detailAddress", void 0);
class SetDefaultAddressDto {
}
exports.SetDefaultAddressDto = SetDefaultAddressDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否设为默认地址', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '是否默认地址必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '是否默认地址值错误' }),
    (0, class_validator_1.Max)(1, { message: '是否默认地址值错误' }),
    __metadata("design:type", Number)
], SetDefaultAddressDto.prototype, "isDefault", void 0);
class AddressQueryDto {
}
exports.AddressQueryDto = AddressQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '地址标签', example: '家', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '地址标签必须是字符串' }),
    __metadata("design:type", String)
], AddressQueryDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否默认地址', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '是否默认地址必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '是否默认地址值错误' }),
    (0, class_validator_1.Max)(1, { message: '是否默认地址值错误' }),
    __metadata("design:type", Number)
], AddressQueryDto.prototype, "isDefault", void 0);
class DeliveryRangeDto {
}
exports.DeliveryRangeDto = DeliveryRangeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '地址名称', example: '中关村软件园' }),
    (0, class_validator_1.IsNotEmpty)({ message: '地址名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '地址名称必须是字符串' }),
    __metadata("design:type", String)
], DeliveryRangeDto.prototype, "addressName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '详细地址', example: '科技园南区科苑路XX号' }),
    (0, class_validator_1.IsNotEmpty)({ message: '详细地址不能为空' }),
    (0, class_validator_1.IsString)({ message: '详细地址必须是字符串' }),
    __metadata("design:type", String)
], DeliveryRangeDto.prototype, "detailAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度', example: 22.5329, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '纬度必须是字符串或数字' }),
    __metadata("design:type", String)
], DeliveryRangeDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度', example: 114.0544, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '经度必须是字符串或数字' }),
    __metadata("design:type", String)
], DeliveryRangeDto.prototype, "longitude", void 0);
//# sourceMappingURL=update-address.dto.js.map