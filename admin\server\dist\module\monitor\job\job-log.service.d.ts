import { Repository } from 'typeorm';
import { JobLog } from './entities/job-log.entity';
import { ListJobLogDto } from './dto/create-job.dto';
import { ResultData } from 'src/common/utils/result';
import { Response } from 'express';
export declare class JobLogService {
    private jobLogRepository;
    constructor(jobLogRepository: Repository<JobLog>);
    list(query: ListJobLogDto): Promise<ResultData>;
    addJobLog(jobLog: Partial<JobLog>): Promise<ResultData>;
    clean(): Promise<ResultData>;
    export(res: Response, body: ListJobLogDto): Promise<void>;
}
