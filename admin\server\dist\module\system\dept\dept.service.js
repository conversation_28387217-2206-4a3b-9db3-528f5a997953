"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeptService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const dept_entity_1 = require("./entities/dept.entity");
const index_1 = require("./dto/index");
const index_2 = require("../../../common/utils/index");
const index_3 = require("../../../common/enum/index");
const redis_decorator_1 = require("../../../common/decorators/redis.decorator");
let DeptService = class DeptService {
    constructor(sysDeptEntityRep) {
        this.sysDeptEntityRep = sysDeptEntityRep;
    }
    async create(createDeptDto) {
        if (createDeptDto.parentId) {
            const parent = await this.sysDeptEntityRep.findOne({
                where: {
                    deptId: createDeptDto.parentId,
                    delFlag: '0',
                },
                select: ['ancestors'],
            });
            if (!parent) {
                return result_1.ResultData.fail(500, '父级部门不存在');
            }
            const ancestors = parent.ancestors ? `${parent.ancestors},${createDeptDto.parentId}` : `${createDeptDto.parentId}`;
            Object.assign(createDeptDto, { ancestors: ancestors });
        }
        await this.sysDeptEntityRep.save(createDeptDto);
        return result_1.ResultData.ok();
    }
    async findAll(query) {
        const entity = this.sysDeptEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.deptName) {
            entity.andWhere(`entity.deptName LIKE "%${query.deptName}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        const res = await entity.getMany();
        return result_1.ResultData.ok(res);
    }
    async findOne(deptId) {
        const data = await this.sysDeptEntityRep.findOne({
            where: {
                deptId: deptId,
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(data);
    }
    async findDeptIdsByDataScope(deptId, dataScope) {
        try {
            const entity = this.sysDeptEntityRep.createQueryBuilder('dept');
            entity.where('dept.delFlag = :delFlag', { delFlag: '0' });
            if (dataScope === index_3.DataScopeEnum.DATA_SCOPE_DEPT) {
                this.addQueryForDeptDataScope(entity, deptId);
            }
            else if (dataScope === index_3.DataScopeEnum.DATA_SCOPE_DEPT_AND_CHILD) {
                this.addQueryForDeptAndChildDataScope(entity, deptId);
            }
            else if (dataScope === index_3.DataScopeEnum.DATA_SCOPE_SELF) {
                return [];
            }
            const list = await entity.getMany();
            return list.map((item) => item.deptId);
        }
        catch (error) {
            console.error('Failed to query department IDs:', error);
            throw new Error('Querying department IDs failed');
        }
    }
    addQueryForDeptDataScope(queryBuilder, deptId) {
        queryBuilder.andWhere('dept.deptId = :deptId', { deptId: deptId });
    }
    addQueryForDeptAndChildDataScope(queryBuilder, deptId) {
        queryBuilder
            .andWhere('dept.ancestors LIKE :ancestors', {
            ancestors: `%${deptId}%`,
        })
            .orWhere('dept.deptId = :deptId', { deptId: deptId });
    }
    async findListExclude(id) {
        const data = await this.sysDeptEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(data);
    }
    async update(updateDeptDto) {
        if (updateDeptDto.parentId && updateDeptDto.parentId !== 0) {
            const parent = await this.sysDeptEntityRep.findOne({
                where: {
                    deptId: updateDeptDto.parentId,
                    delFlag: '0',
                },
                select: ['ancestors'],
            });
            if (!parent) {
                return result_1.ResultData.fail(500, '父级部门不存在');
            }
            const ancestors = parent.ancestors ? `${parent.ancestors},${updateDeptDto.parentId}` : `${updateDeptDto.parentId}`;
            Object.assign(updateDeptDto, { ancestors: ancestors });
        }
        await this.sysDeptEntityRep.update({ deptId: updateDeptDto.deptId }, updateDeptDto);
        return result_1.ResultData.ok();
    }
    async remove(deptId) {
        const data = await this.sysDeptEntityRep.update({ deptId: deptId }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async deptTree() {
        const res = await this.sysDeptEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        const tree = (0, index_2.ListToTree)(res, (m) => m.deptId, (m) => m.deptName);
        return tree;
    }
};
exports.DeptService = DeptService;
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_3.CacheEnum.SYS_DEPT_KEY, '*'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateDeptDto]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "create", null);
__decorate([
    (0, redis_decorator_1.Cacheable)(index_3.CacheEnum.SYS_DEPT_KEY, 'findOne:{deptId}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "findOne", null);
__decorate([
    (0, redis_decorator_1.Cacheable)(index_3.CacheEnum.SYS_DEPT_KEY, 'findDeptIdsByDataScope:{deptId}-{dataScope}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "findDeptIdsByDataScope", null);
__decorate([
    (0, redis_decorator_1.Cacheable)(index_3.CacheEnum.SYS_DEPT_KEY, 'findListExclude'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "findListExclude", null);
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_3.CacheEnum.SYS_DEPT_KEY, '*'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateDeptDto]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "update", null);
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_3.CacheEnum.SYS_DEPT_KEY, '*'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "remove", null);
__decorate([
    (0, redis_decorator_1.Cacheable)(index_3.CacheEnum.SYS_DEPT_KEY, 'deptTree'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DeptService.prototype, "deptTree", null);
exports.DeptService = DeptService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(dept_entity_1.SysDeptEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], DeptService);
//# sourceMappingURL=dept.service.js.map