export declare class CreateRefundRequestDto {
    userId: number;
    orderId: string;
    refundReason: string;
    refundAmount: number;
}
export declare class RefundRequestQueryDto {
    page: number;
    pageSize: number;
    status?: string;
    orderId?: string;
    userId?: number;
}
export declare class ProcessRefundRequestDto {
    requestId: number;
    action: 'approve' | 'reject';
    adminComment?: string;
}
export declare class RefundRequestListResponseDto {
    id: number;
    orderId: string;
    userId: number;
    userNickname?: string;
    refundReason: string;
    refundAmount: number;
    status: string;
    statusText: string;
    adminComment?: string;
    requestTime: Date;
    processTime?: Date;
    orderTotalAmount?: number;
    orderStatus?: string;
}
export declare enum RefundRequestStatus {
    PENDING = "1",
    REFUNDED = "2",
    REJECTED = "3"
}
export declare const RefundRequestStatusText: {
    "1": string;
    "2": string;
    "3": string;
};
export declare class ConfirmRefundPickupDto {
    orderId: string;
    deliveryStaffId?: number;
}
