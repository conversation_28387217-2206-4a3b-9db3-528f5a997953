import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare class CreateUserDto {
    deptId?: number;
    email: string;
    nickName: string;
    userName: string;
    password: string;
    phonenumber?: string;
    postIds?: Array<number>;
    roleIds?: Array<number>;
    status?: string;
    sex?: string;
    remark?: string;
    postSort?: number;
}
declare const UpdateUserDto_base: import("@nestjs/common").Type<Partial<CreateUserDto>>;
export declare class UpdateUserDto extends UpdateUserDto_base {
    userId: number;
}
export declare class ChangeStatusDto {
    userId: number;
    status: string;
}
export declare class ListUserDto extends PagingDto {
    deptId?: string;
    nickName?: string;
    email?: string;
    userName?: string;
    phonenumber?: string;
    status?: string;
}
export declare class ResetPwdDto {
    userId: number;
    password: string;
}
export declare class AllocatedListDto extends PagingDto {
    userName?: string;
    phonenumber?: string;
    roleId?: string;
}
export declare class UpdateProfileDto {
    nickName: string;
    email: string;
    phonenumber: string;
    sex: string;
    avatar?: string;
}
export declare class UpdatePwdDto {
    oldPassword: string;
    newPassword: string;
}
export {};
