import { Repository } from 'typeorm';
import { DeliverySettings } from './entities/delivery-settings.entity';
import { DeliveryTimeSlot } from './entities/delivery-time-slot.entity';
import { CreateDeliverySettingsDto, UpdateDeliverySettingsDto, DeliverySettingsQueryDto, CreateDeliveryTimeSlotDto, UpdateDeliveryTimeSlotDto, DeliveryTimeSlotQueryDto } from './dto';
import { ResultData } from 'src/common/utils/result';
export declare class DeliverySettingsService {
    private readonly deliverySettingsRepository;
    private readonly deliveryTimeSlotRepository;
    private readonly logger;
    constructor(deliverySettingsRepository: Repository<DeliverySettings>, deliveryTimeSlotRepository: Repository<DeliveryTimeSlot>);
    create(createDeliverySettingsDto: CreateDeliverySettingsDto): Promise<ResultData>;
    findAll(queryDto: DeliverySettingsQueryDto): Promise<ResultData>;
    findOne(id: number): Promise<ResultData>;
    update(id: number, updateDeliverySettingsDto: UpdateDeliverySettingsDto): Promise<ResultData>;
    remove(id: number): Promise<ResultData>;
    getUniqueSettings(): Promise<ResultData>;
    calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number;
    checkDeliveryRange(latitude: number, longitude: number): Promise<ResultData>;
    createTimeSlot(createDto: CreateDeliveryTimeSlotDto): Promise<ResultData>;
    findAllTimeSlots(queryDto: DeliveryTimeSlotQueryDto): Promise<ResultData>;
    findOneTimeSlot(id: number): Promise<ResultData>;
    updateTimeSlot(id: number, updateDto: UpdateDeliveryTimeSlotDto): Promise<ResultData>;
    removeTimeSlot(id: number): Promise<ResultData>;
    getActiveTimeSlots(): Promise<ResultData>;
}
