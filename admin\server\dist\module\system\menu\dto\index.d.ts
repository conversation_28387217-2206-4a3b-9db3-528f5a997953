export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare enum MenuTypeEnum {
    M = "M",
    C = "C",
    F = "F"
}
export declare class CreateMenuDto {
    menuName: string;
    orderNum: number;
    parentId: number;
    path?: string;
    query: string;
    component?: string;
    icon?: string;
    menuType: string;
    isCache: string;
    isFrame: string;
    status: string;
    visible: string;
    perms: string;
}
export declare class UpdateMenuDto extends CreateMenuDto {
    menuId: number;
}
export declare class ListDeptDto {
    menuName?: string;
    status?: string;
}
