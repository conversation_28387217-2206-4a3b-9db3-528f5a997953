import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 订单查询DTO
 */
export class OrderQueryDto {
  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  pageNum?: number = 1;

  @ApiProperty({ description: '每页数量', example: 10, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  pageSize?: number = 10;

  @ApiProperty({
    description: '订单状态',
    example: '1',
    enum: ['1', '2', '3', '4', '5', '6', '7'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单状态必须是字符串' })
  @IsEnum(['-1', '1', '2', '3', '4', '5', '6', '7'], {
    message: '订单状态只能是：1待支付 2待发货 3配送中 4已完成 5已取消 6团购失败已退款 7退款中',
  })
  status?: string;

  @ApiProperty({
    description: '订单类型',
    example: '1',
    enum: ['1', '2'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: '订单类型必须是字符串' })
  @IsEnum(['1', '2'], { message: '订单类型只能是：1普通订单 2团购订单' })
  orderType?: string;

  @ApiProperty({ description: '用户ID', example: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '用户ID必须是数字' })
  @Min(1, { message: '用户ID必须大于0' })
  userId?: number;

  @ApiProperty({ description: '订单ID关键词', example: 'ORD', required: false })
  @IsOptional()
  @IsString({ message: '订单ID关键词必须是字符串' })
  orderId?: string;

  @ApiProperty({ description: '收货人姓名', example: '张三', required: false })
  @IsOptional()
  @IsString({ message: '收货人姓名必须是字符串' })
  receiverName?: string;

  @ApiProperty({ description: '收货人电话', example: '13800138000', required: false })
  @IsOptional()
  @IsString({ message: '收货人电话必须是字符串' })
  receiverPhone?: string;

  @ApiProperty({ description: '手机号尾号(后4位)', example: '8000', required: false })
  @IsOptional()
  @IsString({ message: '手机号尾号必须是字符串' })
  phoneLastFour?: string;

  @ApiProperty({ description: '开始时间', example: '2024-06-01', required: false })
  @IsOptional()
  @IsString({ message: '开始时间必须是字符串' })
  startTime?: string;

  @ApiProperty({ description: '结束时间', example: '2024-06-30', required: false })
  @IsOptional()
  @IsString({ message: '结束时间必须是字符串' })
  endTime?: string;

  @ApiProperty({
    description: '配送方式',
    example: '1',
    enum: ['1', '2'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: '配送方式必须是字符串' })
  @IsEnum(['1', '2'], { message: '配送方式只能是：1配送 2自提' })
  deliveryType?: string;

  @ApiProperty({ description: '按预约配送时间排序（ASC/DESC）', example: 'DESC', required: false })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'], { message: '排序方式只能是ASC或DESC' })
  orderByDeliveryTime?: 'ASC' | 'DESC';
}
