"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthStrategy = void 0;
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../common/redis/redis.service");
const index_1 = require("../../../common/enum/index");
let AuthStrategy = class AuthStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(config, redisService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKey: config.get('jwt.secretkey'),
        });
        this.config = config;
        this.redisService = redisService;
    }
    async validate(payload) {
        const user = await this.redisService.get(`${index_1.CacheEnum.LOGIN_TOKEN_KEY}${payload.uuid}`);
        if (!user)
            throw new common_1.UnauthorizedException('登录已过期，请重新登录');
        return user;
    }
};
exports.AuthStrategy = AuthStrategy;
exports.AuthStrategy = AuthStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        redis_service_1.RedisService])
], AuthStrategy);
//# sourceMappingURL=auth.strategy.js.map