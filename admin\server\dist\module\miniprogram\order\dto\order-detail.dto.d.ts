export declare class OrderItemDetailDto {
    itemId: number;
    productId: number;
    productName: string;
    specId: number;
    productImage: string;
    quantity: number;
    price: number;
    totalPrice: number;
}
export declare class OrderDetailDto {
    orderId: string;
    userId: number;
    addressId: number;
    userGroupBuyId: number | null;
    isGroupInitiator: number;
    orderType: string;
    totalAmount: number;
    discountAmount: number;
    finalAmount: number;
    deliveryType: string;
    deliveryTime: Date | null;
    status: string;
    paymentTime: Date | null;
    shipmentTime: Date | null;
    completionTime: Date | null;
    cancelReason: string | null;
    receiverName: string;
    receiverPhone: string;
    receiverAddress: string;
    createTime: Date;
    updateTime: Date;
    remark: string | null;
    orderItems: OrderItemDetailDto[];
    user?: {
        userId: number;
        nickname: string;
        phone: string;
        avatar: string;
    };
}
