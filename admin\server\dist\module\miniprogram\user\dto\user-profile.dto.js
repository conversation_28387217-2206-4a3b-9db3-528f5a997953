"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStatsDto = exports.UserQueryDto = exports.UserProfileDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UserProfileDto {
}
exports.UserProfileDto = UserProfileDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信OpenID' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户性别：0-未知，1-男，2-女' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号码' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户邮箱' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户生日' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "birthday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户地区' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户等级' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户积分' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "points", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户余额' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "balance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '累计消费金额' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单数量' }),
    __metadata("design:type", Number)
], UserProfileDto.prototype, "orderCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户类型：0-普通用户，1-VIP用户' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "userType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP到期时间' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "vipExpireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户标签' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否接收推送：0-否，1-是' }),
    __metadata("design:type", String)
], UserProfileDto.prototype, "pushEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    __metadata("design:type", Date)
], UserProfileDto.prototype, "lastLoginTime", void 0);
class UserQueryDto {
}
exports.UserQueryDto = UserQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号码', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户等级', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UserQueryDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户类型：0-普通用户，1-VIP用户', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "userType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0-正常，1-停用', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UserQueryDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', required: false, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UserQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', required: false, default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UserQueryDto.prototype, "pageSize", void 0);
class UserStatsDto {
}
exports.UserStatsDto = UserStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '足迹数量' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "footprintCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收藏数量' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "favoriteCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券数量' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "couponCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户余额' }),
    __metadata("design:type", Number)
], UserStatsDto.prototype, "balance", void 0);
//# sourceMappingURL=user-profile.dto.js.map