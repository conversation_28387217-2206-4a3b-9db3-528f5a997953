"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysNoticeEntity = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
let SysNoticeEntity = class SysNoticeEntity extends base_1.BaseEntity {
};
exports.SysNoticeEntity = SysNoticeEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'notice_id', comment: '公告ID' }),
    __metadata("design:type", Number)
], SysNoticeEntity.prototype, "noticeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'notice_title', length: 50, default: '', comment: '公告标题' }),
    __metadata("design:type", String)
], SysNoticeEntity.prototype, "noticeTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'notice_type', length: 1, comment: '公告类型' }),
    __metadata("design:type", String)
], SysNoticeEntity.prototype, "noticeType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'longtext', name: 'notice_content', default: null, comment: '公告内容' }),
    __metadata("design:type", String)
], SysNoticeEntity.prototype, "noticeContent", void 0);
exports.SysNoticeEntity = SysNoticeEntity = __decorate([
    (0, typeorm_1.Entity)('sys_notice', {
        comment: '通知公告表',
    })
], SysNoticeEntity);
//# sourceMappingURL=notice.entity.js.map