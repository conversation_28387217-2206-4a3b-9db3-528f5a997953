import Redis from 'ioredis';
export declare class RedisService {
    private readonly client;
    constructor(client: Redis);
    getClient(): Redis;
    getInfo(): Promise<{}>;
    skipFind(data: {
        key: string;
        pageSize: number;
        pageNum: number;
    }): Promise<string[]>;
    getDbSize(): Promise<number>;
    commandStats(): Promise<any[]>;
    set(key: string, val: any, ttl?: number): Promise<'OK' | null>;
    mget(keys: string[]): Promise<any[]>;
    get(key: string): Promise<any>;
    del(keys: string | string[]): Promise<number>;
    ttl(key: string): Promise<number | null>;
    keys(key?: string): Promise<string[]>;
    hset(key: string, field: string, value: string): Promise<string | number | null>;
    hmset(key: string, data: Record<string, string | number | boolean>, expire?: number): Promise<number | any>;
    hget(key: string, field: string): Promise<number | string | null>;
    hvals(key: string): Promise<string[]>;
    hGetAll(key: string): Promise<Record<string, string>>;
    hdel(key: string, fields: string | string[]): Promise<string[] | number>;
    hdelAll(key: string): Promise<string[] | number>;
    lLength(key: string): Promise<number>;
    lSet(key: string, index: number, val: string): Promise<'OK' | null>;
    lIndex(key: string, index: number): Promise<string | null>;
    lRange(key: string, start: number, stop: number): Promise<string[] | null>;
    lLeftPush(key: string, ...val: string[]): Promise<number>;
    lLeftPushIfPresent(key: string, ...val: string[]): Promise<number>;
    lLeftInsert(key: string, pivot: string, val: string): Promise<number>;
    lRightInsert(key: string, pivot: string, val: string): Promise<number>;
    lRightPush(key: string, ...val: string[]): Promise<number>;
    lRightPushIfPresent(key: string, ...val: string[]): Promise<number>;
    lLeftPop(key: string): Promise<string>;
    lRightPop(key: string): Promise<string>;
    lTrim(key: string, start: number, stop: number): Promise<'OK' | null>;
    lRemove(key: string, count: number, val: string): Promise<number>;
    lPoplPush(sourceKey: string, destinationKey: string, timeout: number): Promise<string>;
    reset(): Promise<number>;
}
