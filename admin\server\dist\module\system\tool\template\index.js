"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.index = void 0;
const api_js_js_1 = require("./vue/api.js.js");
const indexVue_vue_js_1 = require("./vue/indexVue.vue.js");
const dialogVue_vue_js_1 = require("./vue/dialogVue.vue.js");
const entity_js_1 = require("./nestjs/entity.js");
const dto_js_1 = require("./nestjs/dto.js");
const controller_js_1 = require("./nestjs/controller.js");
const module_js_1 = require("./nestjs/module.js");
const service_js_1 = require("./nestjs/service.js");
const templates = {
    'tool/template/nestjs/entity.ts.vm': entity_js_1.entityTem,
    'tool/template/nestjs/dto.ts.vm': dto_js_1.dtoTem,
    'tool/template/nestjs/controller.ts.vm': controller_js_1.controllerTem,
    'tool/template/nestjs/service.ts.vm': service_js_1.serviceTem,
    'tool/template/nestjs/module.ts.vm': module_js_1.moduleTem,
    'tool/template/vue/api.js.vm': api_js_js_1.apiTempalte,
    'tool/template/vue/indexVue.vue.vm': indexVue_vue_js_1.indexVue,
    'tool/template/vue/dialogVue.vue.vm': dialogVue_vue_js_1.dialogVue,
};
const index = (options) => {
    const result = {};
    for (const [path, templateFunc] of Object.entries(templates)) {
        result[path] = templateFunc(options);
    }
    return result;
};
exports.index = index;
//# sourceMappingURL=index.js.map