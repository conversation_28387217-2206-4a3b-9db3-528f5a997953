"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrderController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const order_service_1 = require("./order.service");
const create_order_dto_1 = require("./dto/create-order.dto");
const update_order_dto_1 = require("./dto/update-order.dto");
const order_query_dto_1 = require("./dto/order-query.dto");
const order_statistics_dto_1 = require("./dto/order-statistics.dto");
const auth_guard_1 = require("../../../common/guards/auth.guard");
const user_decorator_1 = require("../../system/user/user.decorator");
const result_1 = require("../../../common/utils/result");
const order_status_enum_1 = require("./enum/order-status.enum");
const refund_request_dto_1 = require("./dto/refund-request.dto");
let OrderController = OrderController_1 = class OrderController {
    constructor(orderService) {
        this.orderService = orderService;
        this.logger = new common_1.Logger(OrderController_1.name);
    }
    async createOrder(userId, createOrderDto) {
        this.logger.log(`创建订单请求: UserId=${userId}, Data=${JSON.stringify(createOrderDto)}`);
        try {
            return await this.orderService.createOrder(userId, createOrderDto);
        }
        catch (error) {
            this.logger.error(`创建订单失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `创建订单失败: ${error.message}`);
        }
    }
    async getUserOrderStats(userId) {
        this.logger.log(`获取用户订单统计请求: UserId=${userId}`);
        return await this.orderService.getUserOrderStats(userId);
    }
    async getUserOrders(queryDto) {
        this.logger.log(`获取用户订单列表请求: Query=${JSON.stringify(queryDto)}`);
        return await this.orderService.getUserOrders(queryDto);
    }
    async getOrderDetail(userId, orderId) {
        this.logger.log(`获取订单详情请求: UserId=${userId}, OrderId=${orderId}`);
        return await this.orderService.getOrderDetail(orderId, userId);
    }
    async cancelOrder(userId, orderId, body) {
        this.logger.log(`取消订单请求: UserId=${userId}, OrderId=${orderId}, Reason=${body.reason}`);
        return await this.orderService.cancelOrder(orderId, userId, body.reason);
    }
    async updateOrderStatus(updateOrderDto) {
        this.logger.log(`更新订单状态请求: UserId=${updateOrderDto.userId}, OrderId=${updateOrderDto.orderId}, Data=${JSON.stringify(updateOrderDto)}`);
        try {
            return await this.orderService.updateOrderStatus(updateOrderDto.orderId, updateOrderDto, updateOrderDto.userId);
        }
        catch (error) {
            this.logger.error(`更新订单状态失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `更新订单状态失败: ${error.message}`);
        }
    }
    async findAll(queryDto) {
        this.logger.log(`管理员查询订单列表请求: ${JSON.stringify(queryDto)}`);
        return await this.orderService.findAll(queryDto);
    }
    async findSelfPickupOrders(queryDto) {
        this.logger.log(`管理员查询自提订单列表请求: ${JSON.stringify(queryDto)}`);
        return await this.orderService.findSelfPickupOrders(queryDto);
    }
    async getAdminOrderDetail(orderId) {
        this.logger.log(`管理员获取订单详情请求: OrderId=${orderId}`);
        return await this.orderService.getOrderDetail(orderId);
    }
    async completeOrder(body) {
        const { orderId, userId } = body;
        this.logger.log(`用户完成订单请求: OrderId=${orderId}, UserId=${userId}`);
        try {
            const updateOrderDto = new update_order_dto_1.UpdateOrderDto();
            updateOrderDto.status = order_status_enum_1.OrderStatus.COMPLETED;
            return await this.orderService.updateOrderStatus(orderId, updateOrderDto, userId);
        }
        catch (error) {
            this.logger.error(`完成订单失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `完成订单失败: ${error.message}`);
        }
    }
    async updateAdminOrderStatus(orderId, updateOrderDto) {
        this.logger.log(`管理员更新订单状态请求: OrderId=${orderId}, Data=${JSON.stringify(updateOrderDto)}`);
        return await this.orderService.updateOrderStatus(orderId, updateOrderDto);
    }
    async getOrderStats() {
        this.logger.log(`获取订单统计请求`);
        return await this.orderService.getOrderStats();
    }
    async getDashboardStatistics(queryDto) {
        this.logger.log(`获取仪表板统计数据请求: ${JSON.stringify(queryDto)}`);
        return await this.orderService.getDetailedOrderStatistics(queryDto);
    }
    async debugTables() {
        this.logger.log(`调试：检查数据库表和数据`);
        try {
            const tablesResult = await this.orderService.debugCheckTables();
            return result_1.ResultData.ok(tablesResult);
        }
        catch (error) {
            this.logger.error(`调试检查失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, `调试检查失败: ${error.message}`);
        }
    }
    async deliverOrder(body) {
        this.logger.log(`订单发货请求: OrderId=${body.orderId}`);
        try {
            return await this.orderService.deliverOrder(body.orderId);
        }
        catch (error) {
            this.logger.error(`订单发货失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `订单发货失败: ${error.message}`);
        }
    }
    async refundOrder(body) {
        this.logger.log(`订单退款请求: OrderId=${body.orderId}`);
        try {
            return await this.orderService.refundOrder(body.orderId, body.refundReason);
        }
        catch (error) {
            this.logger.error(`订单退款失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `订单退款失败: ${error.message}`);
        }
    }
    async exportOrders(queryDto) {
        this.logger.log(`导出订单数据请求: ${JSON.stringify(queryDto)}`);
        return await this.orderService.exportOrders(queryDto);
    }
    async batchCancelOrders(body) {
        this.logger.log(`批量取消订单请求: OrderIds=${body.orderIds.join(',')}`);
        try {
            return await this.orderService.batchCancelOrders(body.orderIds);
        }
        catch (error) {
            this.logger.error(`批量取消订单失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                return result_1.ResultData.fail(error.getStatus(), error.message);
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `批量取消订单失败: ${error.message}`);
        }
    }
    async getOrderDetailForAdmin(orderId) {
        this.logger.log(`管理端获取订单详情请求: OrderId=${orderId}`);
        return await this.orderService.getOrderDetail(orderId);
    }
    async checkGroupBuyStatus(activityId) {
        this.logger.log(`调试检查团购活动状态: ActivityId=${activityId}`);
        try {
            const result = await this.orderService.checkGroupBuyStatus(activityId);
            return result;
        }
        catch (error) {
            this.logger.error(`检查团购活动状态失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, `检查失败: ${error.message}`);
        }
    }
    async createRefundRequest(createRefundRequestDto) {
        this.logger.log(`提交退款申请: UserId=${createRefundRequestDto.userId}, OrderId=${createRefundRequestDto.orderId}`);
        try {
            return await this.orderService.createRefundRequest(createRefundRequestDto.userId, createRefundRequestDto.orderId, createRefundRequestDto);
        }
        catch (error) {
            this.logger.error(`提交退款申请失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `提交退款申请失败: ${error.message}`);
        }
    }
    async confirmRefundPickup(confirmRefundPickupDto) {
        this.logger.log(`配送员确认取货: OrderId=${confirmRefundPickupDto.orderId}, DeliveryStaffId=${confirmRefundPickupDto.deliveryStaffId}`);
        try {
            return await this.orderService.confirmRefundPickup(confirmRefundPickupDto.orderId, confirmRefundPickupDto.deliveryStaffId);
        }
        catch (error) {
            this.logger.error(`配送员确认取货失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            return result_1.ResultData.fail(common_1.HttpStatus.INTERNAL_SERVER_ERROR, `确认取货失败: ${error.message}`);
        }
    }
};
exports.OrderController = OrderController;
__decorate([
    (0, common_1.Post)(':userId/create'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建订单（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '订单创建成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_order_dto_1.CreateOrderDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "createOrder", null);
__decorate([
    (0, common_1.Get)(':userId/stats'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户订单统计（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getUserOrderStats", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户订单列表（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [order_query_dto_1.OrderQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getUserOrders", null);
__decorate([
    (0, common_1.Get)(':userId/detail/:orderId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取订单详情（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getOrderDetail", null);
__decorate([
    (0, common_1.Put)(':userId/cancel/:orderId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '取消订单（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('orderId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "cancelOrder", null);
__decorate([
    (0, common_1.Put)('status'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新订单状态（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_order_dto_1.UpdateOrderDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "updateOrderStatus", null);
__decorate([
    (0, common_1.Get)('admin/list'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询订单列表（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [order_query_dto_1.OrderQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('admin/self-pickup/list'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询自提订单列表（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [order_query_dto_1.OrderQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "findSelfPickupOrders", null);
__decorate([
    (0, common_1.Get)('admin/detail/:orderId'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取订单详情（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getAdminOrderDetail", null);
__decorate([
    (0, common_1.Post)('complete'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '完成订单（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '订单完成成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "completeOrder", null);
__decorate([
    (0, common_1.Put)('admin/status/:orderId'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新订单状态（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('orderId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_order_dto_1.UpdateOrderDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "updateAdminOrderStatus", null);
__decorate([
    (0, common_1.Get)('admin/stats'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取订单统计（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getOrderStats", null);
__decorate([
    (0, common_1.Get)('admin/dashboard/statistics'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取仪表板详细统计数据（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [order_statistics_dto_1.OrderStatisticsQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getDashboardStatistics", null);
__decorate([
    (0, common_1.Get)('admin/debug/tables'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '调试：检查数据库表和数据（调试用）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "debugTables", null);
__decorate([
    (0, common_1.Post)('deliver'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '订单发货（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '发货成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "deliverOrder", null);
__decorate([
    (0, common_1.Post)('refund'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '订单退款（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "refundOrder", null);
__decorate([
    (0, common_1.Get)('export'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '导出订单数据（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '导出成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [order_query_dto_1.OrderQueryDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "exportOrders", null);
__decorate([
    (0, common_1.Post)('batch/cancel'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '批量取消订单（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "batchCancelOrders", null);
__decorate([
    (0, common_1.Get)('detail/:orderId'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取订单详情（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "getOrderDetailForAdmin", null);
__decorate([
    (0, common_1.Get)('debug/check-group-buy/:activityId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '检查团购活动状态（调试用）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('activityId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "checkGroupBuyStatus", null);
__decorate([
    (0, common_1.Post)('refund-request'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '提交退款申请（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款申请提交成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refund_request_dto_1.CreateRefundRequestDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "createRefundRequest", null);
__decorate([
    (0, common_1.Post)('admin/refund/confirm-pickup'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '配送员确认取货（已完成订单退款流程-小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取货确认成功，退款已完成' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refund_request_dto_1.ConfirmRefundPickupDto]),
    __metadata("design:returntype", Promise)
], OrderController.prototype, "confirmRefundPickup", null);
exports.OrderController = OrderController = OrderController_1 = __decorate([
    (0, swagger_1.ApiTags)('订单管理'),
    (0, common_1.Controller)('miniprogram/order'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [order_service_1.OrderService])
], OrderController);
//# sourceMappingURL=order.controller.js.map