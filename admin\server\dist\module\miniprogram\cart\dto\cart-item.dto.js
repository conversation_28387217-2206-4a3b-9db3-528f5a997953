"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CartItemDto {
}
exports.CartItemDto = CartItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '购物车ID' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "cartId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称' }),
    __metadata("design:type", String)
], CartItemDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品价格' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "productPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品图片' }),
    __metadata("design:type", Array)
], CartItemDto.prototype, "productImages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格名称' }),
    __metadata("design:type", String)
], CartItemDto.prototype, "specName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格值' }),
    __metadata("design:type", String)
], CartItemDto.prototype, "specValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格图片' }),
    __metadata("design:type", String)
], CartItemDto.prototype, "specImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '库存数量' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "productStock", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '购买数量' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '小计金额' }),
    __metadata("design:type", Number)
], CartItemDto.prototype, "subtotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品状态(0=正常, 1=下架)' }),
    __metadata("design:type", String)
], CartItemDto.prototype, "productStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '添加时间' }),
    __metadata("design:type", Date)
], CartItemDto.prototype, "createTime", void 0);
//# sourceMappingURL=cart-item.dto.js.map