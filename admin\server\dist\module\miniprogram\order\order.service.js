"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrderService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const order_entity_1 = require("./entities/order.entity");
const order_item_entity_1 = require("./entities/order-item.entity");
const update_order_dto_1 = require("./dto/update-order.dto");
const result_1 = require("../../../common/utils/result");
const order_status_enum_1 = require("./enum/order-status.enum");
const typeorm_3 = require("typeorm");
const index_1 = require("../../../common/utils/index");
const a_calc_1 = require("a-calc");
const payment_service_1 = require("../payment/payment.service");
const payment_entity_1 = require("../payment/entities/payment.entity");
const user_entity_1 = require("../user/entities/user.entity");
const notification_gateway_1 = require("../../notification/notification.gateway");
const refund_request_entity_1 = require("./entities/refund-request.entity");
const refund_request_dto_1 = require("./dto/refund-request.dto");
let OrderService = OrderService_1 = class OrderService {
    constructor(orderRepository, paymentRepository, userRepository, refundRequestRepository, dataSource, paymentService, notificationGateway) {
        this.orderRepository = orderRepository;
        this.paymentRepository = paymentRepository;
        this.userRepository = userRepository;
        this.refundRequestRepository = refundRequestRepository;
        this.dataSource = dataSource;
        this.paymentService = paymentService;
        this.notificationGateway = notificationGateway;
        this.logger = new common_1.Logger(OrderService_1.name);
    }
    generateOrderId() {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const randomStr = Math.random().toString().slice(2, 8);
        return `ORD${dateStr}${randomStr}`;
    }
    async createOrder(userId, createOrderDto) {
        this.logger.log(`[DEBUG] 创建订单: UserId=${userId}, Data=${JSON.stringify(createOrderDto)}`);
        return await this.dataSource.transaction(async (manager) => {
            try {
                this.logger.log(`[DEBUG] 验证地址信息: AddressId=${createOrderDto.addressId}`);
                const address = await manager.query('SELECT * FROM miniprogram_user_address WHERE address_id = ? AND user_id = ? AND del_flag = "0"', [createOrderDto.addressId, userId]);
                if (!address || address.length === 0) {
                    throw new common_1.HttpException('收货地址不存在', common_1.HttpStatus.BAD_REQUEST);
                }
                this.logger.log(`[DEBUG] 验证商品信息: ItemsCount=${createOrderDto.items.length}`);
                const orderItems = [];
                let totalAmount = 0;
                let orderType = order_status_enum_1.OrderType.NORMAL;
                let userGroupBuyId = null;
                let isGroupInitiator = 0;
                if (createOrderDto.isUserInitiatedGroupBuy) {
                    orderType = order_status_enum_1.OrderType.GROUP_BUY;
                    userGroupBuyId = this.generateGroupBuyId();
                    isGroupInitiator = 1;
                    this.logger.log(`[DEBUG] 用户发起的团购，生成团购ID: ${userGroupBuyId}, 设置为团购发起人`);
                }
                else if (createOrderDto.userGroupBuyId) {
                    orderType = order_status_enum_1.OrderType.GROUP_BUY;
                    userGroupBuyId = createOrderDto.userGroupBuyId;
                    this.logger.log(`[DEBUG] 用户参与团购，团购ID: ${userGroupBuyId}`);
                    const existingGroupBuy = await manager.query('SELECT * FROM orders WHERE user_group_buy_id = ? AND order_type = ? AND status != ? AND del_flag = "0"', [
                        userGroupBuyId,
                        order_status_enum_1.OrderType.GROUP_BUY,
                        order_status_enum_1.OrderStatus.CANCELLED,
                    ]);
                    if (!existingGroupBuy || existingGroupBuy.length === 0) {
                        throw new common_1.HttpException('参与的团购不存在或已结束', common_1.HttpStatus.BAD_REQUEST);
                    }
                }
                const isGroupBuy = orderType === order_status_enum_1.OrderType.GROUP_BUY;
                for (const item of createOrderDto.items) {
                    const product = await manager.query('SELECT product_id, name, price, status, images, category_id FROM products WHERE product_id = ? AND status = 1', [item.productId]);
                    if (!product || product.length === 0) {
                        throw new common_1.HttpException(`商品不存在或已下架: ID=${item.productId}`, common_1.HttpStatus.BAD_REQUEST);
                    }
                    let price = parseFloat(product[0].price);
                    const specId = item.specId || 0;
                    let specName = '默认规格';
                    let specValue = '默认';
                    if (specId > 0) {
                        this.logger.log(`[DEBUG] 检查商品规格信息: ProductId=${item.productId}, SpecId=${specId}`);
                        const specQuery = 'SELECT spec_id, name, value, price, stock, status, group_buy_price FROM product_specs WHERE spec_id = ? AND product_id = ?';
                        const specs = await manager.query(specQuery, [specId, item.productId]);
                        if (!specs || specs.length === 0) {
                            throw new common_1.HttpException(`商品规格不存在: 商品ID=${item.productId}, 规格ID=${specId}`, common_1.HttpStatus.BAD_REQUEST);
                        }
                        const specInfo = specs[0];
                        if (specInfo.status === 0) {
                            throw new common_1.HttpException(`商品规格已禁用: 商品ID=${item.productId}, 规格=${specInfo.name}`, common_1.HttpStatus.BAD_REQUEST);
                        }
                        if (specInfo.stock < item.quantity) {
                            throw new common_1.HttpException(`商品规格库存不足: ${product[0].name} - ${specInfo.name}, 当前库存: ${specInfo.stock}, 需要: ${item.quantity}`, common_1.HttpStatus.BAD_REQUEST);
                        }
                        if (isGroupBuy && specInfo.group_buy_price) {
                            price = parseFloat(specInfo.group_buy_price);
                            this.logger.log(`[DEBUG] 使用规格团购价格: ${price}`);
                        }
                        else {
                            price = parseFloat(specInfo.price);
                            this.logger.log(`[DEBUG] 使用规格原价: ${price}`);
                        }
                        specName = specInfo.name || '默认规格';
                        specValue = specInfo.value || '默认';
                        this.logger.log(`[DEBUG] 保存规格信息: 规格名称=${specName}, 规格值=${specValue}`);
                        await manager.query('UPDATE product_specs SET stock = stock - ?, sales_count = sales_count + ? WHERE spec_id = ?', [item.quantity, item.quantity, specId]);
                    }
                    else {
                        const defaultSpecQuery = 'SELECT spec_id, name, value, price, stock, status, group_buy_price FROM product_specs WHERE product_id = ? AND is_default = 1';
                        const defaultSpecs = await manager.query(defaultSpecQuery, [item.productId]);
                        if (defaultSpecs && defaultSpecs.length > 0) {
                            const specInfo = defaultSpecs[0];
                            if (specInfo.stock < item.quantity) {
                                throw new common_1.HttpException(`商品默认规格库存不足: ${product[0].name}, 当前库存: ${specInfo.stock}, 需要: ${item.quantity}`, common_1.HttpStatus.BAD_REQUEST);
                            }
                            if (isGroupBuy && specInfo.group_buy_price) {
                                price = parseFloat(specInfo.group_buy_price);
                                this.logger.log(`[DEBUG] 使用默认规格团购价格: ${price}`);
                            }
                            specName = specInfo.name || '默认规格';
                            specValue = specInfo.value || '默认';
                            this.logger.log(`[DEBUG] 保存默认规格信息: 规格名称=${specName}, 规格值=${specValue}`);
                            await manager.query('UPDATE product_specs SET stock = stock - ?, sales_count = sales_count + ? WHERE spec_id = ?', [item.quantity, item.quantity, specInfo.spec_id]);
                        }
                    }
                    const totalPrice = parseFloat((0, a_calc_1.calc)(`${price} * ${item.quantity} | =2`));
                    totalAmount = parseFloat((0, a_calc_1.calc)(`${totalAmount} + ${totalPrice} | =2`));
                    this.logger.log(`[DEBUG] 商品单价: ${price}元，数量: ${item.quantity}，小计: ${totalPrice}元`);
                    let originalPrice = parseFloat(product[0].price);
                    let groupBuyPrice = null;
                    if (specId > 0) {
                        const specQuery = 'SELECT price, group_buy_price FROM product_specs WHERE spec_id = ? AND product_id = ?';
                        const specPriceInfo = await manager.query(specQuery, [specId, item.productId]);
                        if (specPriceInfo && specPriceInfo.length > 0) {
                            originalPrice = parseFloat(specPriceInfo[0].price);
                            if (specPriceInfo[0].group_buy_price) {
                                groupBuyPrice = parseFloat(specPriceInfo[0].group_buy_price);
                            }
                        }
                    }
                    const productName = product[0].name;
                    const productImages = product[0].images || '';
                    const productImage = productImages ? productImages.split(',')[0] : '';
                    const categoryId = product[0].category_id || null;
                    let categoryName = '';
                    if (categoryId) {
                        const categoryQuery = 'SELECT name FROM product_categories WHERE category_id = ?';
                        const categoryInfo = await manager.query(categoryQuery, [categoryId]);
                        if (categoryInfo && categoryInfo.length > 0) {
                            categoryName = categoryInfo[0].name;
                        }
                    }
                    orderItems.push({
                        productId: item.productId,
                        productName: productName,
                        productImage: productImage,
                        productImages: productImages,
                        categoryId: categoryId,
                        categoryName: categoryName,
                        specId: specId,
                        specName: specName,
                        specValue: specValue,
                        quantity: item.quantity,
                        price: price,
                        totalPrice: totalPrice,
                        originalPrice: originalPrice,
                        groupBuyPrice: groupBuyPrice,
                    });
                    await manager.query('UPDATE products SET sales_count = sales_count + ? WHERE product_id = ?', [item.quantity, item.productId]);
                }
                let discountAmount = 0;
                let userCouponId = null;
                if (createOrderDto.couponId) {
                    this.logger.log(`[DEBUG] 处理优惠券: CouponId=${createOrderDto.couponId}`);
                    try {
                        const userCouponQuery = `
              SELECT uc.id, uc.user_id, uc.coupon_id, uc.status, 
                     c.type, c.condition_amount, c.discount_amount, c.discount_rate, 
                     c.goods_type, c.goods_ids, c.category_ids, c.start_time, c.end_time
              FROM t_user_coupon uc
              JOIN t_coupon c ON uc.coupon_id = c.id
              WHERE uc.user_id = ? AND uc.coupon_id = ? 
                AND uc.status = '0' AND uc.del_flag = '0'
                AND c.status = '1' AND c.del_flag = '0'
                AND c.start_time <= NOW() AND c.end_time >= NOW()
            `;
                        const userCoupon = await manager.query(userCouponQuery, [userId, createOrderDto.couponId]);
                        if (!userCoupon || userCoupon.length === 0) {
                            throw new common_1.HttpException('优惠券不存在或已使用', common_1.HttpStatus.BAD_REQUEST);
                        }
                        const coupon = userCoupon[0];
                        userCouponId = coupon.id;
                        const conditionAmountInYuan = parseFloat((0, a_calc_1.calc)(`${coupon.condition_amount} / 100 | =2`));
                        if (coupon.condition_amount > 0 && totalAmount < conditionAmountInYuan) {
                            throw new common_1.HttpException(`订单金额未满足优惠券使用条件，需满${conditionAmountInYuan}元`, common_1.HttpStatus.BAD_REQUEST);
                        }
                        if (coupon.goods_type !== '1') {
                            if (coupon.goods_type === '2' && coupon.goods_ids) {
                                const goodsIds = coupon.goods_ids.split(',').map((id) => parseInt(id.trim()));
                                const hasValidProduct = orderItems.some((item) => goodsIds.includes(item.productId));
                                if (!hasValidProduct) {
                                    throw new common_1.HttpException('当前订单商品不适用于此优惠券', common_1.HttpStatus.BAD_REQUEST);
                                }
                            }
                            else if (coupon.goods_type === '3' && coupon.category_ids) {
                                const categoryIds = coupon.category_ids.split(',').map((id) => parseInt(id.trim()));
                                const hasValidCategory = orderItems.some((item) => item.categoryId && categoryIds.includes(item.categoryId));
                                if (!hasValidCategory) {
                                    throw new common_1.HttpException('当前订单商品分类不适用于此优惠券', common_1.HttpStatus.BAD_REQUEST);
                                }
                            }
                        }
                        if (coupon.type === '1') {
                            discountAmount = parseFloat((0, a_calc_1.calc)(`${coupon.discount_amount} / 100 | =2`));
                            this.logger.log(`[DEBUG] 使用满减券，优惠金额: ${discountAmount}元`);
                        }
                        else if (coupon.type === '2') {
                            const discountRate = parseFloat((0, a_calc_1.calc)(`${coupon.discount_rate} / 100 | =2`));
                            discountAmount = parseFloat((0, a_calc_1.calc)(`${totalAmount} * (1 - ${discountRate}) | =2`));
                            this.logger.log(`[DEBUG] 使用折扣券，折扣率: ${discountRate}，优惠金额: ${discountAmount}元`);
                        }
                        else if (coupon.type === '3') {
                            discountAmount = parseFloat((0, a_calc_1.calc)(`${coupon.discount_amount} / 100 | =2`));
                            this.logger.log(`[DEBUG] 使用无门槛券，优惠金额: ${discountAmount}元`);
                        }
                        if (discountAmount > totalAmount) {
                            discountAmount = totalAmount;
                            this.logger.log(`[DEBUG] 优惠金额超过订单总金额，调整为: ${discountAmount}元`);
                        }
                        this.logger.log(`[DEBUG] 订单总金额: ${totalAmount}元，优惠金额: ${discountAmount}元，最终金额: ${(0, a_calc_1.calc)(`${totalAmount} - ${discountAmount} | =2`)}元`);
                    }
                    catch (error) {
                        if (error instanceof common_1.HttpException) {
                            throw error;
                        }
                        this.logger.error(`[ERROR] 处理优惠券失败: ${error.message}`, error.stack);
                        throw new common_1.HttpException('处理优惠券失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                    }
                }
                const finalAmount = parseFloat((0, a_calc_1.calc)(`${totalAmount} - ${discountAmount} | =2`));
                const orderId = this.generateOrderId();
                this.logger.log(`[DEBUG] 生成订单ID: ${orderId}`);
                const order = new order_entity_1.OrderEntity();
                order.orderId = orderId;
                order.userId = userId;
                order.addressId = createOrderDto.addressId;
                order.orderType = orderType;
                order.userGroupBuyId = userGroupBuyId;
                order.isGroupInitiator = isGroupInitiator;
                order.totalAmount = totalAmount;
                order.discountAmount = discountAmount;
                order.finalAmount = finalAmount;
                order.deliveryType = createOrderDto.deliveryType;
                order.deliveryTime = createOrderDto.deliveryTime ? new Date(createOrderDto.deliveryTime) : null;
                order.status = order_status_enum_1.OrderStatus.PENDING_PAYMENT;
                order.createTime = new Date();
                order.updateTime = new Date();
                order.remark = createOrderDto.remark || null;
                order.receiverName = address[0].receiver_name;
                order.receiverPhone = address[0].receiver_phone;
                order.receiverAddress = this.formatAddress(address[0]);
                order.createBy = userId.toString();
                order.updateBy = userId.toString();
                const savedOrder = await manager.save(order_entity_1.OrderEntity, order);
                this.logger.log(`[DEBUG] 创建订单商品: Count=${orderItems.length}`);
                for (const itemData of orderItems) {
                    const orderItem = new order_item_entity_1.OrderItemEntity();
                    orderItem.orderId = orderId;
                    orderItem.productId = itemData.productId;
                    orderItem.productName = itemData.productName;
                    orderItem.productImage = itemData.productImage;
                    orderItem.productImages = itemData.productImages;
                    orderItem.categoryId = itemData.categoryId;
                    orderItem.categoryName = itemData.categoryName;
                    orderItem.specId = itemData.specId;
                    orderItem.specName = itemData.specName;
                    orderItem.specValue = itemData.specValue;
                    orderItem.quantity = itemData.quantity;
                    orderItem.price = itemData.price;
                    orderItem.totalPrice = itemData.totalPrice;
                    orderItem.originalPrice = itemData.originalPrice;
                    orderItem.groupBuyPrice = itemData.groupBuyPrice;
                    await manager.save(order_item_entity_1.OrderItemEntity, orderItem);
                }
                if (userCouponId) {
                    this.logger.log(`[DEBUG] 更新优惠券使用状态: UserCouponId=${userCouponId}, OrderId=${orderId}`);
                    await manager.query(`
            UPDATE t_user_coupon 
            SET status = '1', use_time = NOW(), order_id = ?, update_time = NOW(), update_by = ? 
            WHERE id = ? AND user_id = ? AND status = '0'
          `, [orderId, userId.toString(), userCouponId, userId]);
                }
                const cartIds = createOrderDto.items.filter((item) => item.cartId).map((item) => item.cartId);
                if (cartIds.length > 0) {
                    this.logger.log(`[DEBUG] 清空购物车中的相关商品，购物车项ID: ${cartIds.join(',')}`);
                    try {
                        await manager.query(`UPDATE shopping_cart SET del_flag = '1', update_time = NOW(), update_by = ? 
               WHERE user_id = ? AND cart_id IN (${cartIds.map(() => '?').join(',')})`, [userId.toString(), userId, ...cartIds]);
                    }
                    catch (error) {
                        this.logger.error(`[DEBUG] 更新购物车记录失败：${error.message}`);
                        try {
                            await manager.query(`DELETE FROM shopping_cart WHERE user_id = ? AND cart_id IN (${cartIds.map(() => '?').join(',')})`, [userId, ...cartIds]);
                        }
                        catch (deleteError) {
                            this.logger.error(`[DEBUG] 删除购物车记录也失败：${deleteError.message}`);
                        }
                    }
                }
                else {
                    this.logger.log(`[DEBUG] 没有提供购物车项ID，尝试通过商品ID和规格ID清理购物车`);
                    const productSpecs = createOrderDto.items.map((item) => ({
                        productId: item.productId,
                        specId: item.specId || 0,
                    }));
                    for (const { productId, specId } of productSpecs) {
                        try {
                            await manager.query(`UPDATE shopping_cart SET del_flag = '1', update_time = NOW(), update_by = ? 
                 WHERE user_id = ? AND product_id = ? AND spec_id = ?`, [userId.toString(), userId, productId, specId]);
                        }
                        catch (error) {
                            this.logger.error(`[DEBUG] 更新购物车记录失败：${error.message}`);
                        }
                    }
                }
                const responseData = {
                    orderId: orderId,
                    totalAmount: totalAmount,
                    finalAmount: finalAmount,
                    orderStatus: order_status_enum_1.OrderStatus.PENDING_PAYMENT,
                    createTime: new Date(),
                };
                if (isGroupBuy) {
                    responseData.groupBuyId = userGroupBuyId;
                    responseData.isInitiator = isGroupInitiator === 1;
                }
                return result_1.ResultData.ok(responseData, '订单创建成功');
            }
            catch (error) {
                this.logger.error(`[ERROR] 创建订单失败: ${error.message}`, error.stack);
                if (error instanceof common_1.HttpException) {
                    return result_1.ResultData.fail(error.getStatus(), error.message);
                }
                throw new common_1.HttpException('创建订单失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
    async getUserOrders(queryDto) {
        this.logger.log(`[DEBUG] 获取用户订单列表: Query=${JSON.stringify(queryDto)}`);
        try {
            const queryBuilder = this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.orderItems', 'orderItems')
                .leftJoinAndSelect('orderItems.product', 'product')
                .leftJoinAndSelect('order.user', 'user')
                .leftJoinAndSelect('order.address', 'address')
                .leftJoin('refund_requests', 'refundRequest', 'refundRequest.order_id = order.orderId')
                .addSelect([
                'refundRequest.status AS refund_status',
                'refundRequest.refund_reason AS refund_reason',
                'refundRequest.request_time AS refund_request_time'
            ])
                .where('order.delFlag = :delFlag', { delFlag: '0' });
            if (queryDto.userId) {
                queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
            }
            if (queryDto.deliveryType) {
                queryBuilder.andWhere('order.deliveryType = :deliveryType', { deliveryType: queryDto.deliveryType });
            }
            if (queryDto.status) {
                if (queryDto.status == '-1') {
                    queryBuilder.andWhere('order.status IN (:...statuses)', { statuses: [5, 6] });
                }
                else {
                    queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
                }
            }
            if (queryDto.orderType) {
                queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
            }
            if (queryDto.orderByDeliveryTime) {
                queryBuilder.orderBy('order.deliveryTime', queryDto.orderByDeliveryTime);
            }
            else {
                queryBuilder.orderBy('order.createTime', 'DESC');
            }
            const pageNum = queryDto.pageNum || 1;
            const pageSize = queryDto.pageSize || 10;
            queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
            const [orders, total] = await queryBuilder.getManyAndCount();
            const groupBuyOrderIds = this.extractGroupBuyIds(orders);
            const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);
            const orderIds = orders.map(order => order.orderId);
            let paymentInfoMap = new Map();
            if (orderIds.length > 0) {
                this.logger.log(`[DEBUG] 批量查询支付信息: 订单数=${orderIds.length}, OrderIds=${orderIds.join(',')}`);
                const paymentResults = await this.dataSource.query(`SELECT order_id, payment_method, payment_time, payment_amount 
           FROM payments 
           WHERE order_id IN (${orderIds.map(() => '?').join(',')}) AND payment_status = '2'
           ORDER BY create_time DESC`, orderIds);
                this.logger.log(`[DEBUG] 支付信息查询结果: 找到${paymentResults.length}条记录`);
                this.logger.log(`[DEBUG] 支付记录详情: ${JSON.stringify(paymentResults)}`);
                const processedOrderIds = new Set();
                paymentResults.forEach(payment => {
                    if (!processedOrderIds.has(payment.order_id)) {
                        paymentInfoMap.set(payment.order_id, payment);
                        processedOrderIds.add(payment.order_id);
                        this.logger.log(`[DEBUG] 为订单 ${payment.order_id} 设置支付方式: ${payment.payment_method}`);
                    }
                });
                this.logger.log(`[DEBUG] 支付信息映射完成: 映射了${paymentInfoMap.size}个订单的支付信息`);
            }
            const processedOrders = orders.map((order) => {
                const paymentInfo = paymentInfoMap.get(order.orderId);
                this.logger.log(`[DEBUG] 处理订单 ${order.orderId}: 支付信息=${JSON.stringify(paymentInfo)}, 支付方式=${paymentInfo?.payment_method}`);
                const orderData = {
                    orderId: order.orderId,
                    userId: order.userId,
                    orderType: order.orderType,
                    totalAmount: order.totalAmount,
                    discountAmount: order.discountAmount,
                    finalAmount: order.finalAmount,
                    deliveryType: order.deliveryType,
                    deliveryTime: order.deliveryTime,
                    status: order.status,
                    receiverName: order.receiverName,
                    receiverPhone: order.receiverPhone,
                    receiverAddress: order.receiverAddress,
                    receiverLatitude: order.address?.latitude || null,
                    receiverLongitude: order.address?.longitude || null,
                    createTime: order.createTime,
                    paymentTime: order.paymentTime,
                    shipmentTime: order.shipmentTime,
                    completionTime: order.completionTime,
                    remark: order.remark,
                    items: [],
                    userGroupBuyId: order.userGroupBuyId,
                    isGroupInitiator: order.isGroupInitiator === 1,
                    hasReviewed: order.reviewStatus === '1',
                    paymentMethod: paymentInfo?.payment_method || null,
                    paymentAmount: paymentInfo?.payment_amount || null,
                    refundType: order.refundType || null,
                    refundStatus: order.refund_status || null,
                    refundReason: order.refund_reason || null,
                    refundRequestTime: order.refund_request_time || null,
                };
                if (order.orderItems && order.orderItems.length > 0) {
                    orderData.items = order.orderItems.map((item) => {
                        const { productImage, imageList } = this.processProductImages(item.product, new Map());
                        return {
                            itemId: item.itemId,
                            productId: item.productId,
                            productName: item.product ? item.product.name : '未知商品',
                            productImage: productImage,
                            imageList: imageList,
                            price: item.price,
                            quantity: item.quantity,
                            totalPrice: item.totalPrice,
                            specId: item.specId,
                            specName: item.specName || '默认规格',
                            specValue: item.specValue || '默认',
                            originalPrice: item.originalPrice,
                            groupBuyPrice: item.groupBuyPrice,
                            categoryId: item.categoryId,
                            categoryName: item.categoryName,
                        };
                    });
                }
                if (order.user) {
                    orderData['user'] = {
                        userId: order.user.userId,
                        nickname: order.user.nickname,
                        avatar: order.user.avatar,
                        gender: order.user.gender,
                    };
                }
                if (order.orderType === order_status_enum_1.OrderType.GROUP_BUY && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
                    orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
                    this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
                }
                return orderData;
            });
            this.logger.log(`[DEBUG] 获取用户订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
            return result_1.ResultData.ok({
                list: processedOrders,
                total,
                pageNum,
                pageSize,
            });
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取用户订单列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取订单列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrderDetail(orderId, userId) {
        this.logger.log(`[DEBUG] 获取订单详情: OrderId=${orderId}, UserId=${userId}`);
        try {
            const order = await this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.orderItems', 'orderItems')
                .leftJoinAndSelect('orderItems.product', 'product')
                .leftJoinAndSelect('order.user', 'user')
                .where('order.orderId = :orderId', { orderId })
                .andWhere('order.delFlag = :delFlag', { delFlag: '0' })
                .andWhere(userId ? 'order.userId = :userId' : '1=1', userId ? { userId } : {})
                .getOne();
            if (!order) {
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            this.logger.log(`[DEBUG] 查询支付信息 SQL: SELECT payment_method, payment_time, payment_amount FROM payments WHERE order_id = '${orderId}' AND payment_status = "2" ORDER BY create_time DESC LIMIT 1`);
            const paymentResult = await this.dataSource.query('SELECT payment_method, payment_time, payment_amount FROM payments WHERE order_id = ? AND payment_status = "2" ORDER BY create_time DESC LIMIT 1', [orderId]);
            this.logger.log(`[DEBUG] 支付信息查询结果: ${JSON.stringify(paymentResult)}`);
            const paymentInfo = paymentResult && paymentResult.length > 0 ? paymentResult[0] : null;
            this.logger.log(`[DEBUG] 处理后的支付信息: ${JSON.stringify(paymentInfo)}`);
            this.logger.log(`[DEBUG] 最终支付方式: ${paymentInfo?.payment_method}`);
            const orderDetail = {
                orderId: order.orderId,
                userId: order.userId,
                orderType: order.orderType,
                totalAmount: order.totalAmount,
                discountAmount: order.discountAmount,
                finalAmount: order.finalAmount,
                deliveryType: order.deliveryType,
                deliveryTime: order.deliveryTime,
                status: order.status,
                receiverName: order.receiverName,
                receiverPhone: order.receiverPhone,
                receiverAddress: order.receiverAddress,
                createTime: order.createTime,
                paymentTime: order.paymentTime,
                shipmentTime: order.shipmentTime,
                completionTime: order.completionTime,
                cancelReason: order.cancelReason,
                remark: order.remark,
                userGroupBuyId: order.userGroupBuyId,
                isGroupInitiator: order.isGroupInitiator === 1,
                items: [],
                paymentMethod: paymentInfo?.payment_method || null,
                paymentAmount: paymentInfo?.payment_amount || null,
                refundType: order.refundType || null,
            };
            if (order.user) {
                orderDetail['user'] = {
                    userId: order.user.userId,
                    nickname: order.user.nickname,
                    phone: order.user.phone,
                    avatar: order.user.avatar,
                };
            }
            if (order.orderItems && order.orderItems.length > 0) {
                orderDetail.items = order.orderItems.map((item) => {
                    const { productImage, imageList } = this.processProductImages(item.product, new Map());
                    return {
                        itemId: item.itemId,
                        productId: item.productId,
                        productName: item.product ? item.product.name : '未知商品',
                        productImage: productImage,
                        imageList: imageList,
                        price: item.price,
                        quantity: item.quantity,
                        totalPrice: item.totalPrice,
                        specId: item.specId,
                        specName: item.specName || '默认规格',
                        specValue: item.specValue || '默认',
                        originalPrice: item.originalPrice,
                        groupBuyPrice: item.groupBuyPrice,
                        categoryId: item.categoryId,
                        categoryName: item.categoryName,
                    };
                });
            }
            if (order.orderType === order_status_enum_1.OrderType.GROUP_BUY && order.userGroupBuyId) {
                const groupBuyParticipantsMap = await this.getGroupBuyParticipants([order.userGroupBuyId]);
                if (groupBuyParticipantsMap.has(order.userGroupBuyId)) {
                    const participants = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
                    orderDetail['groupBuyParticipants'] = participants;
                    this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${orderId}, ParticipantsCount=${participants.length}`);
                }
            }
            this.logger.log(`[DEBUG] 获取订单详情成功: OrderId=${orderId}`);
            return result_1.ResultData.ok(orderDetail);
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取订单详情失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('获取订单详情失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateOrderStatus(orderId, updateOrderDto, operatorId) {
        this.logger.log(`[DEBUG] 更新订单状态: OrderId=${orderId}, Data=${JSON.stringify(updateOrderDto)}, OperatorId=${operatorId}`);
        try {
            const order = await this.orderRepository.findOne({
                where: { orderId, delFlag: '0' },
            });
            if (!order) {
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const currentStatus = order.status;
            const newStatus = updateOrderDto.status;
            if (!this.isValidStatusTransition(currentStatus, newStatus)) {
                throw new common_1.HttpException(`订单状态不能从 ${currentStatus} 变更为 ${newStatus}`, common_1.HttpStatus.BAD_REQUEST);
            }
            order.status = newStatus;
            order.updateTime = new Date();
            order.updateBy = operatorId?.toString() || order.updateBy;
            const now = new Date();
            switch (newStatus) {
                case order_status_enum_1.OrderStatus.PENDING_SHIPMENT:
                    order.paymentTime = now;
                    break;
                case order_status_enum_1.OrderStatus.SHIPPING:
                    order.shipmentTime = now;
                    break;
                case order_status_enum_1.OrderStatus.COMPLETED:
                    order.completionTime = now;
                    break;
                case order_status_enum_1.OrderStatus.CANCELLED:
                case order_status_enum_1.OrderStatus.GROUP_BUY_FAILED:
                    order.cancelReason = updateOrderDto.cancelReason || '系统取消';
                    break;
            }
            const updatedOrder = await this.orderRepository.save(order);
            if (currentStatus === order_status_enum_1.OrderStatus.PENDING_PAYMENT && newStatus === order_status_enum_1.OrderStatus.PENDING_SHIPMENT) {
                try {
                    this.notificationGateway.sendNewOrderNotification({
                        orderId: updatedOrder.orderId,
                        totalAmount: updatedOrder.totalAmount,
                        finalAmount: updatedOrder.finalAmount,
                        orderType: updatedOrder.orderType === order_status_enum_1.OrderType.GROUP_BUY ? '团购订单' : '普通订单',
                        createTime: updatedOrder.createTime,
                        paymentTime: updatedOrder.paymentTime,
                        userInfo: {
                            userId: updatedOrder.userId
                        }
                    });
                    this.logger.log(`[DEBUG] 支付成功通知发送成功: OrderId=${orderId}`);
                }
                catch (error) {
                    this.logger.error(`[ERROR] 发送支付成功通知失败: ${error.message}`, error.stack);
                }
            }
            this.logger.log(`[DEBUG] 订单状态更新成功: OrderId=${orderId}, Status=${newStatus}`);
            return result_1.ResultData.ok(updatedOrder, '订单状态更新成功');
        }
        catch (error) {
            this.logger.error(`[ERROR] 更新订单状态失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('更新订单状态失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateOrderEvaluationStatus(orderId, userId, status) {
        this.logger.log(`[DEBUG] 更新订单评价状态: OrderId=${orderId}, UserId=${userId}, Status=${status}`);
        try {
            const order = await this.orderRepository.findOne({
                where: { orderId, userId, delFlag: '0' },
            });
            if (!order) {
                throw new common_1.HttpException('订单不存在或无权操作', common_1.HttpStatus.NOT_FOUND);
            }
            order.reviewStatus = status;
            order.updateTime = new Date();
            order.updateBy = userId.toString();
            await this.orderRepository.save(order);
            this.logger.log(`[DEBUG] 订单评价状态更新成功: OrderId=${orderId}, ReviewStatus=${status}`);
            return result_1.ResultData.ok(null, '订单评价状态更新成功');
        }
        catch (error) {
            this.logger.error(`[ERROR] 更新订单评价状态失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('更新订单评价状态失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async cancelOrder(orderId, userId, reason) {
        this.logger.log(`[DEBUG] 取消订单: OrderId=${orderId}, UserId=${userId}, Reason=${reason}`);
        try {
            const order = await this.orderRepository.findOne({
                where: { orderId, userId, delFlag: '0' },
            });
            if (!order) {
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (order.status !== order_status_enum_1.OrderStatus.PENDING_PAYMENT) {
                throw new common_1.HttpException('只有待支付的订单可以取消', common_1.HttpStatus.BAD_REQUEST);
            }
            const updateOrderDto = new update_order_dto_1.UpdateOrderDto();
            updateOrderDto.status = order_status_enum_1.OrderStatus.CANCELLED;
            updateOrderDto.cancelReason = reason || '用户主动取消';
            return await this.updateOrderStatus(orderId, updateOrderDto, userId);
        }
        catch (error) {
            this.logger.error(`[ERROR] 取消订单失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('取消订单失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(queryDto) {
        this.logger.log(`[DEBUG] 管理员查询订单列表: ${JSON.stringify(queryDto)}`);
        try {
            const queryBuilder = this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.orderItems', 'orderItems')
                .leftJoinAndSelect('orderItems.product', 'product')
                .leftJoinAndSelect('order.user', 'user')
                .where('order.delFlag = :delFlag', { delFlag: '0' });
            if (queryDto.userId) {
                queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
            }
            if (queryDto.orderId) {
                queryBuilder.andWhere('order.orderId LIKE :orderId', { orderId: `%${queryDto.orderId}%` });
            }
            if (queryDto.status) {
                queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
            }
            if (queryDto.orderType) {
                queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
            }
            if (queryDto.receiverName) {
                queryBuilder.andWhere('order.receiverName LIKE :receiverName', {
                    receiverName: `%${queryDto.receiverName}%`,
                });
            }
            if (queryDto.receiverPhone) {
                queryBuilder.andWhere('order.receiverPhone LIKE :receiverPhone', {
                    receiverPhone: `%${queryDto.receiverPhone}%`,
                });
            }
            if (queryDto.startTime) {
                queryBuilder.andWhere('order.createTime >= :startTime', { startTime: new Date(queryDto.startTime) });
            }
            if (queryDto.endTime) {
                queryBuilder.andWhere('order.createTime <= :endTime', { endTime: new Date(queryDto.endTime + ' 23:59:59') });
            }
            const pageNum = queryDto.pageNum || 1;
            const pageSize = queryDto.pageSize || 10;
            queryBuilder
                .orderBy('order.createTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [orders, total] = await queryBuilder.getManyAndCount();
            const groupBuyOrderIds = this.extractGroupBuyIds(orders);
            const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);
            const processedOrders = orders.map((order) => {
                const orderData = {
                    orderId: order.orderId,
                    userId: order.userId,
                    orderType: order.orderType,
                    totalAmount: order.totalAmount,
                    discountAmount: order.discountAmount,
                    finalAmount: order.finalAmount,
                    deliveryType: order.deliveryType,
                    deliveryTime: order.deliveryTime,
                    status: order.status,
                    receiverName: order.receiverName,
                    receiverPhone: order.receiverPhone,
                    receiverAddress: order.receiverAddress,
                    createTime: order.createTime,
                    paymentTime: order.paymentTime,
                    shipmentTime: order.shipmentTime,
                    completionTime: order.completionTime,
                    cancelReason: order.cancelReason,
                    remark: order.remark,
                    items: [],
                    userGroupBuyId: order.userGroupBuyId,
                    isGroupInitiator: order.isGroupInitiator === 1,
                    paymentMethod: null,
                    paymentAmount: null,
                    refundType: order.refundType || null,
                };
                if (order.user) {
                    orderData['user'] = {
                        userId: order.user.userId,
                        nickname: order.user.nickname,
                        phone: order.user.phone,
                        avatar: order.user.avatar,
                    };
                }
                if (order.orderItems && order.orderItems.length > 0) {
                    orderData.items = order.orderItems.map((item) => {
                        const { productImage, imageList } = this.processProductImages(item.product, new Map());
                        return {
                            itemId: item.itemId,
                            productId: item.productId,
                            productName: item.product ? item.product.name : '未知商品',
                            productImage: productImage,
                            imageList: imageList,
                            price: item.price,
                            quantity: item.quantity,
                            totalPrice: item.totalPrice,
                            specId: item.specId,
                            specName: item.specName || '默认规格',
                            specValue: item.specValue || '默认',
                            originalPrice: item.originalPrice,
                            groupBuyPrice: item.groupBuyPrice,
                            categoryId: item.categoryId,
                            categoryName: item.categoryName,
                        };
                    });
                }
                if (order.orderType === order_status_enum_1.OrderType.GROUP_BUY && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
                    orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
                    this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
                }
                return orderData;
            });
            this.logger.log(`[DEBUG] 管理员查询订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
            return result_1.ResultData.ok({
                list: processedOrders,
                total,
                pageNum,
                pageSize,
            });
        }
        catch (error) {
            this.logger.error(`[ERROR] 管理员查询订单列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询订单列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findSelfPickupOrders(queryDto) {
        this.logger.log(`[DEBUG] 管理员查询自提订单列表: ${JSON.stringify(queryDto)}`);
        try {
            const queryBuilder = this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.orderItems', 'orderItems')
                .leftJoinAndSelect('orderItems.product', 'product')
                .leftJoinAndSelect('order.user', 'user')
                .leftJoin('payments', 'payment', 'payment.order_id = order.orderId AND payment.payment_status = :paymentStatus', { paymentStatus: '2' })
                .addSelect(['payment.payment_method', 'payment.payment_time', 'payment.payment_amount'])
                .where('order.delFlag = :delFlag', { delFlag: '0' })
                .andWhere('order.deliveryType = :deliveryType', { deliveryType: '2' });
            if (queryDto.userId) {
                queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
            }
            if (queryDto.orderId) {
                queryBuilder.andWhere('order.orderId LIKE :orderId', { orderId: `%${queryDto.orderId}%` });
            }
            if (queryDto.status) {
                queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
            }
            if (queryDto.orderType) {
                queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
            }
            if (queryDto.receiverName) {
                queryBuilder.andWhere('order.receiverName LIKE :receiverName', {
                    receiverName: `%${queryDto.receiverName}%`,
                });
            }
            if (queryDto.receiverPhone) {
                queryBuilder.andWhere('order.receiverPhone LIKE :receiverPhone', {
                    receiverPhone: `%${queryDto.receiverPhone}%`,
                });
            }
            if (queryDto.phoneLastFour) {
                queryBuilder.andWhere('user.phone LIKE :phoneLastFour', {
                    phoneLastFour: `%${queryDto.phoneLastFour}`,
                });
                this.logger.log(`[DEBUG] 根据用户手机号尾号搜索: ${queryDto.phoneLastFour}`);
            }
            if (queryDto.startTime) {
                queryBuilder.andWhere('order.createTime >= :startTime', { startTime: new Date(queryDto.startTime) });
            }
            if (queryDto.endTime) {
                queryBuilder.andWhere('order.createTime <= :endTime', { endTime: new Date(queryDto.endTime + ' 23:59:59') });
            }
            const pageNum = queryDto.pageNum || 1;
            const pageSize = queryDto.pageSize || 10;
            queryBuilder
                .orderBy('order.createTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [orders, total] = await queryBuilder.getManyAndCount();
            const groupBuyOrderIds = this.extractGroupBuyIds(orders);
            const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);
            const processedOrders = orders.map((order) => {
                const orderData = {
                    orderId: order.orderId,
                    userId: order.userId,
                    orderType: order.orderType,
                    totalAmount: order.totalAmount,
                    discountAmount: order.discountAmount,
                    finalAmount: order.finalAmount,
                    deliveryType: order.deliveryType,
                    deliveryTime: order.deliveryTime,
                    status: order.status,
                    receiverName: order.receiverName,
                    receiverPhone: order.receiverPhone,
                    receiverAddress: order.receiverAddress,
                    createTime: order.createTime,
                    paymentTime: order.paymentTime,
                    shipmentTime: order.shipmentTime,
                    completionTime: order.completionTime,
                    cancelReason: order.cancelReason,
                    remark: order.remark,
                    items: [],
                    userGroupBuyId: order.userGroupBuyId,
                    isGroupInitiator: order.isGroupInitiator === 1,
                    paymentMethod: null,
                    paymentAmount: null,
                    refundType: order.refundType || null,
                };
                if (order.user) {
                    orderData['user'] = {
                        userId: order.user.userId,
                        nickname: order.user.nickname,
                        phone: order.user.phone,
                        avatar: order.user.avatar,
                    };
                }
                if (order.orderItems && order.orderItems.length > 0) {
                    orderData.items = order.orderItems.map((item) => {
                        const { productImage, imageList } = this.processProductImages(item.product, new Map());
                        return {
                            itemId: item.itemId,
                            productId: item.productId,
                            productName: item.product ? item.product.name : '未知商品',
                            productImage: productImage,
                            imageList: imageList,
                            price: item.price,
                            quantity: item.quantity,
                            totalPrice: item.totalPrice,
                            specId: item.specId,
                            specName: item.specName || '默认规格',
                            specValue: item.specValue || '默认',
                            originalPrice: item.originalPrice,
                            groupBuyPrice: item.groupBuyPrice,
                            categoryId: item.categoryId,
                            categoryName: item.categoryName,
                        };
                    });
                }
                if (order.orderType === '2' && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
                    orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
                    this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
                }
                return orderData;
            });
            this.logger.log(`[DEBUG] 管理员查询自提订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
            return result_1.ResultData.ok({
                rows: processedOrders,
                total,
                pageNum: Number(pageNum),
                pageSize: Number(pageSize),
                totalPages: Math.ceil(total / pageSize),
            });
        }
        catch (error) {
            this.logger.error(`[ERROR] 管理员查询自提订单列表失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('查询自提订单列表失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrderStats() {
        this.logger.log(`[DEBUG] 获取订单统计`);
        try {
            const totalCount = await this.orderRepository.count({ where: { delFlag: '0' } });
            const statusCounts = await this.dataSource.query(`
        SELECT status, COUNT(*) as count
        FROM orders
        WHERE del_flag = '0'
        GROUP BY status
      `);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const todayCount = await this.orderRepository.count({
                where: {
                    delFlag: '0',
                    createTime: (0, typeorm_3.MoreThanOrEqual)(today),
                },
            });
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const monthCount = await this.orderRepository.count({
                where: {
                    delFlag: '0',
                    createTime: (0, typeorm_3.MoreThanOrEqual)(firstDayOfMonth),
                },
            });
            const totalSalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
      `);
            const totalSales = totalSalesResult[0]?.total || 0;
            const todaySalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
        AND DATE(create_time) = CURDATE()
      `);
            const todaySales = todaySalesResult[0]?.total || 0;
            const monthSalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
      `);
            const monthSales = monthSalesResult[0]?.total || 0;
            const statusMap = {};
            statusCounts.forEach((item) => {
                statusMap[item.status] = item.count;
            });
            return result_1.ResultData.ok({
                totalCount,
                todayCount,
                monthCount,
                totalSales,
                todaySales,
                monthSales,
                statusCounts: statusMap,
            });
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取订单统计失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取订单统计失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDetailedOrderStatistics(query) {
        this.logger.log(`[DEBUG] 获取详细订单统计: ${JSON.stringify(query)}`);
        try {
            const { period = 'day', range = 7, topCount = 10 } = query;
            const salesTrend = await this.getSalesTrend(period, range);
            const hotProducts = await this.getHotProducts(topCount);
            const summary = await this.getSummaryStatistics();
            const result = {
                salesTrend,
                hotProducts,
                summary,
            };
            return result_1.ResultData.ok(result);
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取详细订单统计失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取详细订单统计失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSalesTrend(period, range) {
        try {
            let dateFormat;
            let dateFunction;
            switch (period) {
                case 'day':
                    dateFormat = '%Y-%m-%d';
                    dateFunction = 'DATE';
                    break;
                case 'week':
                    dateFormat = '%Y-第%u周';
                    dateFunction = 'YEARWEEK';
                    break;
                case 'month':
                    dateFormat = '%Y-%m';
                    dateFunction = 'DATE_FORMAT';
                    break;
                default:
                    dateFormat = '%Y-%m-%d';
                    dateFunction = 'DATE';
            }
            let dateCondition;
            let selectDate;
            let groupBy;
            switch (period) {
                case 'day':
                    dateCondition = 'DATE(create_time) = CURDATE()';
                    selectDate = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
                    groupBy = 'DATE(create_time), HOUR(create_time)';
                    break;
                case 'week':
                    dateCondition = 'YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)';
                    selectDate = 'DATE(create_time)';
                    groupBy = 'DATE(create_time)';
                    break;
                case 'month':
                    dateCondition = 'YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW())';
                    selectDate = 'DATE(create_time)';
                    groupBy = 'DATE(create_time)';
                    break;
                default:
                    dateCondition = 'DATE(create_time) = CURDATE()';
                    selectDate = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
                    groupBy = 'DATE(create_time), HOUR(create_time)';
            }
            const sql = `
        SELECT 
          ${selectDate} as date,
          0 as sales,
          COUNT(*) as orderCount
        FROM orders 
        WHERE del_flag = '0' 
          AND status = '4'
          AND ${dateCondition}
        GROUP BY ${groupBy}
        ORDER BY ${groupBy}
      `;
            this.logger.log(`[DEBUG] 销售趋势SQL: ${sql}`);
            const result = await this.dataSource.query(sql);
            this.logger.log(`[DEBUG] 销售趋势查询结果: ${JSON.stringify(result)}`);
            const processedResult = result.map((item) => {
                let dateStr = item.date;
                if (typeof item.date === 'object' && item.date instanceof Date) {
                    const year = item.date.getFullYear();
                    const month = String(item.date.getMonth() + 1).padStart(2, '0');
                    const day = String(item.date.getDate()).padStart(2, '0');
                    dateStr = `${year}-${month}-${day}`;
                }
                else if (typeof item.date === 'string' && item.date.includes('T')) {
                    dateStr = item.date.split('T')[0];
                }
                return {
                    date: dateStr,
                    sales: parseFloat(item.sales) || 0,
                    orderCount: parseInt(item.orderCount) || 0,
                };
            });
            this.logger.log(`[DEBUG] 处理后的销售趋势: ${JSON.stringify(processedResult)}`);
            return processedResult;
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取销售趋势失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async getHotProducts(topCount) {
        try {
            const sql = `
        SELECT 
          oi.product_id as productId,
          oi.product_name as productName,
          oi.category_name as categoryName,
          SUM(oi.total_price) as totalSales,
          SUM(oi.quantity) as totalQuantity,
          COUNT(DISTINCT oi.order_id) as orderCount,
          MAX(oi.product_image) as productImage
        FROM order_items oi
        INNER JOIN orders o ON oi.order_id = o.order_id
        WHERE o.del_flag = '0' 
          AND o.status IN ('2', '3', '4')
        GROUP BY oi.product_id, oi.product_name, oi.category_name
        ORDER BY totalSales DESC, totalQuantity DESC
        LIMIT ?
      `;
            this.logger.log(`[DEBUG] 热销商品SQL: ${sql}`);
            const result = await this.dataSource.query(sql, [topCount]);
            this.logger.log(`[DEBUG] 热销商品查询结果: ${JSON.stringify(result)}`);
            const processedResult = result.map((item) => ({
                productId: parseInt(item.productId),
                productName: item.productName || '未知商品',
                categoryName: item.categoryName || '未分类',
                totalSales: parseFloat(item.totalSales) || 0,
                totalQuantity: parseInt(item.totalQuantity) || 0,
                orderCount: parseInt(item.orderCount) || 0,
                productImage: item.productImage || '',
            }));
            this.logger.log(`[DEBUG] 处理后的热销商品: ${JSON.stringify(processedResult)}`);
            return processedResult;
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取热销商品失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async getSummaryStatistics() {
        try {
            const currentSalesResult = await this.dataSource.query(`
        SELECT COALESCE(SUM(final_amount), 0) as total
        FROM orders
        WHERE del_flag = '0' AND status IN ('2', '3', '4')
      `);
            const currentOrdersResult = await this.dataSource.query(`
        SELECT 
          COUNT(*) as totalOrders,
          SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as completedOrders,
          SUM(CASE WHEN status IN ('1', '2', '3') THEN 1 ELSE 0 END) as pendingOrders,
          SUM(CASE WHEN status IN ('5', '6') THEN 1 ELSE 0 END) as cancelledOrders
        FROM orders
        WHERE del_flag = '0'
      `);
            const lastMonthSalesResult = await this.dataSource.query(`
        SELECT COALESCE(SUM(final_amount), 0) as total
        FROM orders
        WHERE del_flag = '0' AND status IN ('2', '3', '4')
          AND create_time >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 MONTH)
          AND create_time < DATE_SUB(NOW(), INTERVAL 1 MONTH)
      `);
            const lastMonthOrdersResult = await this.dataSource.query(`
        SELECT COUNT(*) as totalOrders
        FROM orders
        WHERE del_flag = '0' 
          AND create_time >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 MONTH)
          AND create_time < DATE_SUB(NOW(), INTERVAL 1 MONTH)
      `);
            this.logger.log(`[DEBUG] 当前销售额: ${JSON.stringify(currentSalesResult)}`);
            this.logger.log(`[DEBUG] 上月销售额: ${JSON.stringify(lastMonthSalesResult)}`);
            const totalSales = parseFloat(currentSalesResult[0]?.total) || 0;
            const orderCounts = currentOrdersResult[0];
            const totalOrders = parseInt(orderCounts?.totalOrders) || 0;
            const lastMonthSales = parseFloat(lastMonthSalesResult[0]?.total) || 0;
            const lastMonthOrders = parseInt(lastMonthOrdersResult[0]?.totalOrders) || 0;
            const salesGrowth = lastMonthSales > 0 ? Math.round(((totalSales - lastMonthSales) / lastMonthSales) * 100 * 10) / 10 : 0;
            const ordersGrowth = lastMonthOrders > 0 ? Math.round(((totalOrders - lastMonthOrders) / lastMonthOrders) * 100 * 10) / 10 : 0;
            const result = {
                totalSales,
                totalOrders,
                completedOrders: parseInt(orderCounts?.completedOrders) || 0,
                pendingOrders: parseInt(orderCounts?.pendingOrders) || 0,
                cancelledOrders: parseInt(orderCounts?.cancelledOrders) || 0,
                salesGrowth,
                ordersGrowth,
                completedGrowth: 0,
            };
            this.logger.log(`[DEBUG] 汇总统计结果: ${JSON.stringify(result)}`);
            return result;
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取汇总统计失败: ${error.message}`, error.stack);
            return {
                totalSales: 0,
                totalOrders: 0,
                completedOrders: 0,
                pendingOrders: 0,
                cancelledOrders: 0,
                salesGrowth: 0,
                ordersGrowth: 0,
                completedGrowth: 0,
            };
        }
    }
    async debugCheckTables() {
        try {
            const tableExistsResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = 'orders'
      `);
            const orderCountResult = await this.dataSource.query(`
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN del_flag = '0' THEN 1 END) as active_count,
               COUNT(CASE WHEN del_flag = '0' AND status IN ('2', '3', '4') THEN 1 END) as paid_count
        FROM orders
      `);
            const recentOrdersResult = await this.dataSource.query(`
        SELECT order_id, user_id, total_amount, final_amount, status, del_flag, create_time
        FROM orders 
        ORDER BY create_time DESC 
        LIMIT 10
      `);
            const orderItemCountResult = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM order_items
      `);
            return {
                tableExists: tableExistsResult[0]?.count > 0,
                orderCounts: orderCountResult[0],
                recentOrders: recentOrdersResult,
                orderItemCount: orderItemCountResult[0]?.count || 0,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`[ERROR] 调试检查失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getUserOrderStats(userId) {
        this.logger.log(`[DEBUG] 获取用户订单统计: UserId=${userId}`);
        try {
            const totalOrders = await this.orderRepository.count({
                where: {
                    userId,
                    delFlag: '0',
                },
            });
            const statusStats = await this.orderRepository
                .createQueryBuilder('order')
                .select('order.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .where('order.delFlag = :delFlag', { delFlag: '0' })
                .andWhere('order.userId = :userId', { userId })
                .groupBy('order.status')
                .getRawMany();
            const allStatuses = {
                [order_status_enum_1.OrderStatus.PENDING_PAYMENT]: 0,
                [order_status_enum_1.OrderStatus.PENDING_SHIPMENT]: 0,
                [order_status_enum_1.OrderStatus.SHIPPING]: 0,
                [order_status_enum_1.OrderStatus.COMPLETED]: 0,
                [order_status_enum_1.OrderStatus.CANCELLED]: 0,
                [order_status_enum_1.OrderStatus.GROUP_BUY_FAILED]: 0,
            };
            statusStats.forEach((item) => {
                allStatuses[item.status] = parseInt(item.count);
            });
            const totalConsumption = await this.orderRepository
                .createQueryBuilder('order')
                .select('SUM(order.finalAmount)', 'total')
                .where('order.delFlag = :delFlag', { delFlag: '0' })
                .andWhere('order.userId = :userId', { userId })
                .andWhere('order.status = :status', { status: order_status_enum_1.OrderStatus.COMPLETED })
                .getRawOne();
            const stats = Object.entries(allStatuses).map(([status, count]) => ({
                status,
                count,
                statusText: this.getStatusText(status),
            }));
            this.logger.log(`[DEBUG] 获取用户订单统计成功: ${JSON.stringify(stats)}`);
            return result_1.ResultData.ok(stats);
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取用户订单统计失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('获取用户订单统计失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    isValidStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            [order_status_enum_1.OrderStatus.PENDING_PAYMENT]: [order_status_enum_1.OrderStatus.PENDING_SHIPMENT, order_status_enum_1.OrderStatus.CANCELLED, order_status_enum_1.OrderStatus.GROUP_BUY_FAILED],
            [order_status_enum_1.OrderStatus.PENDING_SHIPMENT]: [order_status_enum_1.OrderStatus.SHIPPING, order_status_enum_1.OrderStatus.CANCELLED, order_status_enum_1.OrderStatus.COMPLETED],
            [order_status_enum_1.OrderStatus.SHIPPING]: [order_status_enum_1.OrderStatus.COMPLETED],
            [order_status_enum_1.OrderStatus.COMPLETED]: [],
            [order_status_enum_1.OrderStatus.CANCELLED]: [],
            [order_status_enum_1.OrderStatus.GROUP_BUY_FAILED]: [],
        };
        return validTransitions[currentStatus]?.includes(newStatus) || false;
    }
    formatAddress(address) {
        const addressName = address.address_name || '';
        const detailAddress = address.detail_address || '';
        return `${addressName}${detailAddress}`;
    }
    async checkGroupBuyStatus(activityId) {
        this.logger.log(`[DEBUG] 检查团购活动状态: ActivityId=${activityId}`);
        try {
            const orders = await this.orderRepository.find({
                where: {
                    userGroupBuyId: activityId.toString(),
                    orderType: order_status_enum_1.OrderType.GROUP_BUY,
                    delFlag: '0',
                },
            });
            if (!orders || orders.length === 0) {
                return result_1.ResultData.fail(404, '团购活动不存在');
            }
            const participantsMap = await this.getGroupBuyParticipants([activityId.toString()]);
            const participants = participantsMap.get(activityId.toString()) || [];
            return result_1.ResultData.ok({
                activityId: activityId,
                status: 'active',
                participants: participants,
                participantCount: participants.length,
            });
        }
        catch (error) {
            this.logger.error(`[ERROR] 检查团购活动状态失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '检查团购活动状态失败');
        }
    }
    generateGroupBuyId() {
        try {
            const uuid = (0, index_1.GenerateUUID)();
            this.logger.log(`[DEBUG] 生成新的团购ID(UUID): ${uuid}`);
            return uuid;
        }
        catch (error) {
            this.logger.error(`[ERROR] 生成团购ID失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('生成团购ID失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    processProductImages(product, imageMap) {
        let productImage = '';
        let imageList = [];
        if (product && product.images) {
            const imageUrls = product.images.split(',').filter((url) => url.trim());
            if (imageUrls.length > 0) {
                productImage = imageUrls[0];
                imageList = imageUrls;
            }
        }
        return { productImage, imageList };
    }
    async getGroupBuyParticipants(groupBuyIds) {
        this.logger.log(`[DEBUG] 查询团购订单参与者信息: GroupBuyIds=${groupBuyIds.join(',')}`);
        const groupBuyParticipantsMap = new Map();
        if (!groupBuyIds.length) {
            return groupBuyParticipantsMap;
        }
        try {
            const participantOrders = await this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.user', 'user')
                .where('order.userGroupBuyId IN (:...groupBuyIds)', { groupBuyIds })
                .andWhere('order.orderType = :orderType', { orderType: order_status_enum_1.OrderType.GROUP_BUY })
                .andWhere('order.delFlag = :delFlag', { delFlag: '0' })
                .getMany();
            participantOrders.forEach((order) => {
                if (!order.userGroupBuyId)
                    return;
                if (!groupBuyParticipantsMap.has(order.userGroupBuyId)) {
                    groupBuyParticipantsMap.set(order.userGroupBuyId, []);
                }
                const participantInfo = {
                    userId: order.userId,
                    orderId: order.orderId,
                    isInitiator: order.isGroupInitiator === 1,
                    joinTime: order.createTime,
                    nickname: order.user?.nickname || '未知用户',
                    avatar: order.user?.avatar || '',
                    gender: order.user?.gender || '0',
                };
                groupBuyParticipantsMap.get(order.userGroupBuyId)?.push(participantInfo);
            });
            return groupBuyParticipantsMap;
        }
        catch (error) {
            this.logger.error(`[ERROR] 获取团购订单参与者信息失败: ${error.message}`, error.stack);
            return new Map();
        }
    }
    extractGroupBuyIds(orders) {
        const groupBuyOrderIds = [];
        orders.forEach((order) => {
            if (order.orderType === order_status_enum_1.OrderType.GROUP_BUY && order.userGroupBuyId) {
                groupBuyOrderIds.push(order.userGroupBuyId);
            }
        });
        return [...new Set(groupBuyOrderIds)];
    }
    async deliverOrder(orderId) {
        try {
            this.logger.log(`[DEBUG] 订单发货: OrderId=${orderId}`);
            const order = await this.orderRepository.findOne({
                where: { orderId: orderId },
            });
            if (!order) {
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (order.status !== '2') {
                throw new common_1.HttpException('只有待发货状态的订单才能发货', common_1.HttpStatus.BAD_REQUEST);
            }
            order.status = '3';
            order.shipmentTime = new Date();
            order.updateBy = 'admin';
            order.updateTime = new Date();
            await this.orderRepository.save(order);
            this.logger.log(`[DEBUG] 订单发货成功: OrderId=${orderId}`);
            return result_1.ResultData.ok(null, '订单发货成功');
        }
        catch (error) {
            this.logger.error(`[ERROR] 订单发货失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('订单发货失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refundOrder(orderId, refundReason, refundType = '3') {
        try {
            this.logger.log(`[DEBUG] 订单退款: OrderId=${orderId}, Reason=${refundReason}, RefundType=${refundType}`);
            const order = await this.orderRepository.findOne({
                where: { orderId: orderId },
            });
            if (!order) {
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (refundType === '2') {
                if (order.status !== '7') {
                    throw new common_1.HttpException('配送员确认取货只能处理退款中的订单', common_1.HttpStatus.BAD_REQUEST);
                }
            }
            else {
                if (!['2', '3'].includes(order.status)) {
                    throw new common_1.HttpException('只有待发货或配送中的订单才能退款', common_1.HttpStatus.BAD_REQUEST);
                }
            }
            const payment = await this.paymentRepository.findOne({
                where: {
                    orderId: orderId,
                    paymentStatus: '2',
                },
                order: { createTime: 'DESC' },
            });
            if (!payment) {
                throw new common_1.HttpException('未找到有效的支付记录', common_1.HttpStatus.NOT_FOUND);
            }
            this.logger.log(`[DEBUG] 处理退款: PaymentId=${payment.paymentId}, Method=${payment.paymentMethod}`);
            const refundResult = await this.processRefund(payment, order.userId, refundReason);
            if (!refundResult.success) {
                throw new common_1.HttpException(refundResult.message || '退款处理失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            order.status = '5';
            order.cancelReason = refundReason || '管理员退款';
            order.refundReason = refundReason;
            order.refundType = refundType;
            order.refundTime = new Date();
            order.updateBy = 'admin';
            order.updateTime = new Date();
            await this.orderRepository.save(order);
            this.logger.log(`[DEBUG] 订单退款成功: OrderId=${orderId}`);
            return result_1.ResultData.ok(refundResult, '订单退款成功');
        }
        catch (error) {
            this.logger.error(`[ERROR] 订单退款失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('订单退款失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processRefund(payment, userId, refundReason) {
        try {
            const refundRequest = {
                paymentId: payment.paymentId,
                refundAmount: Number(payment.paymentAmount),
                refundReason: refundReason || '管理员退款',
            };
            const refundResult = await this.paymentService.requestRefund(refundRequest, userId);
            if (refundResult.code === 200) {
                return { success: true, data: refundResult.data };
            }
            else {
                return { success: false, message: refundResult.msg || '退款失败' };
            }
        }
        catch (error) {
            this.logger.error(`[ERROR] 退款失败: ${error.message}`, error.stack);
            return { success: false, message: `退款失败: ${error.message}` };
        }
    }
    async exportOrders(queryDto) {
        try {
            this.logger.log(`[DEBUG] 导出订单数据: ${JSON.stringify(queryDto)}`);
            const queryBuilder = this.orderRepository
                .createQueryBuilder('order')
                .leftJoinAndSelect('order.user', 'user')
                .leftJoinAndSelect('order.orderItems', 'orderItems')
                .where('order.delFlag = :delFlag', { delFlag: '0' });
            if (queryDto.orderId) {
                queryBuilder.andWhere('order.orderId LIKE :orderId', {
                    orderId: `%${queryDto.orderId}%`,
                });
            }
            if (queryDto.status) {
                queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
            }
            if (queryDto.orderType) {
                queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
            }
            const orders = await queryBuilder.orderBy('order.createTime', 'DESC').getMany();
            const exportData = orders.map((order) => ({
                订单号: order.orderId,
                用户昵称: order.user?.nickname || '-',
                订单类型: order.orderType === '1' ? '普通订单' : '团购订单',
                订单总额: order.totalAmount,
                优惠金额: order.discountAmount,
                实付金额: order.finalAmount,
                订单状态: this.getStatusText(order.status),
                配送方式: order.deliveryType === '1' ? '配送' : '自提',
                收货人: order.receiverName,
                收货电话: order.receiverPhone,
                收货地址: order.receiverAddress,
                下单时间: order.createTime,
                支付时间: order.paymentTime || '-',
                发货时间: order.shipmentTime || '-',
                完成时间: order.completionTime || '-',
                取消原因: order.cancelReason || '-',
            }));
            return result_1.ResultData.ok(exportData, `成功导出 ${exportData.length} 条订单数据`);
        }
        catch (error) {
            this.logger.error(`[ERROR] 导出订单数据失败: ${error.message}`, error.stack);
            throw new common_1.HttpException('导出订单数据失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async batchCancelOrders(orderIds) {
        try {
            this.logger.log(`[DEBUG] 批量取消订单: OrderIds=${orderIds.join(',')}`);
            if (!orderIds || orderIds.length === 0) {
                throw new common_1.HttpException('订单ID不能为空', common_1.HttpStatus.BAD_REQUEST);
            }
            const orders = await this.orderRepository.find({
                where: {
                    orderId: (0, typeorm_2.In)(orderIds),
                    delFlag: '0',
                },
            });
            if (orders.length !== orderIds.length) {
                throw new common_1.HttpException('部分订单不存在', common_1.HttpStatus.BAD_REQUEST);
            }
            const invalidOrders = orders.filter((order) => !['1', '2'].includes(order.status));
            if (invalidOrders.length > 0) {
                const invalidOrderIds = invalidOrders.map((order) => order.orderId);
                throw new common_1.HttpException(`订单 ${invalidOrderIds.join(', ')} 状态不允许取消`, common_1.HttpStatus.BAD_REQUEST);
            }
            const updateResult = await this.orderRepository.update({ orderId: (0, typeorm_2.In)(orderIds) }, {
                status: '5',
                cancelReason: '管理员批量取消',
                updateBy: 'admin',
                updateTime: new Date(),
            });
            this.logger.log(`[DEBUG] 批量取消订单成功: 影响行数=${updateResult.affected}`);
            return result_1.ResultData.ok(null, `成功取消 ${updateResult.affected} 个订单`);
        }
        catch (error) {
            this.logger.error(`[ERROR] 批量取消订单失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('批量取消订单失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    getStatusText(status) {
        const statusMap = {
            '1': '待支付',
            '2': '待发货',
            '3': '配送中',
            '4': '已完成',
            '5': '已取消',
            '6': '团购失败已退款',
        };
        return statusMap[status] || status;
    }
    async createRefundRequest(userId, orderId, createRefundRequestDto) {
        return await this.dataSource.transaction(async (manager) => {
            try {
                this.logger.log(`[DEBUG] 创建退款申请: UserId=${userId}, OrderId=${orderId}, Data=${JSON.stringify(createRefundRequestDto)}`);
                const order = await manager.findOne(order_entity_1.OrderEntity, {
                    where: { orderId, userId, delFlag: '0' },
                });
                if (!order) {
                    throw new common_1.HttpException('订单不存在或无权限', common_1.HttpStatus.NOT_FOUND);
                }
                if (order.status === order_status_enum_1.OrderStatus.SHIPPING) {
                    throw new common_1.HttpException('配送中的订单无法申请退款', common_1.HttpStatus.BAD_REQUEST);
                }
                if (![order_status_enum_1.OrderStatus.PENDING_SHIPMENT, order_status_enum_1.OrderStatus.COMPLETED].includes(order.status)) {
                    throw new common_1.HttpException('只有待发货或已完成的订单才能申请退款', common_1.HttpStatus.BAD_REQUEST);
                }
                if (order.status === order_status_enum_1.OrderStatus.REFUNDING || order.status === order_status_enum_1.OrderStatus.CANCELLED) {
                    throw new common_1.HttpException('该订单已在退款流程中或已取消', common_1.HttpStatus.BAD_REQUEST);
                }
                if (createRefundRequestDto.refundAmount > Number(order.finalAmount)) {
                    throw new common_1.HttpException('退款金额不能超过订单实付金额', common_1.HttpStatus.BAD_REQUEST);
                }
                if (order.status === order_status_enum_1.OrderStatus.PENDING_SHIPMENT) {
                    this.logger.log(`[DEBUG] 待发货订单申请退款，立即执行退款: OrderId=${orderId}`);
                    const refundResult = await this.refundOrder(orderId, createRefundRequestDto.refundReason, '1');
                    if (refundResult.code !== 200) {
                        throw new common_1.HttpException('退款处理失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                    }
                    order.status = order_status_enum_1.OrderStatus.CANCELLED;
                    order.refundReason = createRefundRequestDto.refundReason;
                    order.refundType = '1';
                    order.refundTime = new Date();
                    await manager.save(order_entity_1.OrderEntity, order);
                    const refundRequest = manager.create(refund_request_entity_1.RefundRequestEntity, {
                        orderId,
                        userId,
                        refundReason: createRefundRequestDto.refundReason,
                        refundAmount: createRefundRequestDto.refundAmount,
                        status: refund_request_dto_1.RefundRequestStatus.REFUNDED,
                        requestTime: new Date(),
                        processTime: new Date(),
                    });
                    await manager.save(refund_request_entity_1.RefundRequestEntity, refundRequest);
                    this.logger.log(`[DEBUG] 待发货订单退款成功: OrderId=${orderId}`);
                    return result_1.ResultData.ok(refundRequest, '退款申请已提交并自动处理完成');
                }
                else if (order.status === order_status_enum_1.OrderStatus.COMPLETED) {
                    this.logger.log(`[DEBUG] 已完成订单申请退款，等待配送员取货: OrderId=${orderId}`);
                    order.status = order_status_enum_1.OrderStatus.REFUNDING;
                    order.refundReason = createRefundRequestDto.refundReason;
                    order.refundType = '2';
                    await manager.save(order_entity_1.OrderEntity, order);
                    const refundRequest = manager.create(refund_request_entity_1.RefundRequestEntity, {
                        orderId,
                        userId,
                        refundReason: createRefundRequestDto.refundReason,
                        refundAmount: createRefundRequestDto.refundAmount,
                        status: refund_request_dto_1.RefundRequestStatus.PENDING,
                        requestTime: new Date(),
                    });
                    await manager.save(refund_request_entity_1.RefundRequestEntity, refundRequest);
                    this.logger.log(`[DEBUG] 已完成订单退款申请创建成功: OrderId=${orderId}`);
                    return result_1.ResultData.ok(refundRequest, '退款申请已提交，等待配送员上门取货后完成退款');
                }
            }
            catch (error) {
                this.logger.error(`[ERROR] 创建退款申请失败: ${error.message}`, error.stack);
                if (error instanceof common_1.HttpException) {
                    throw error;
                }
                throw new common_1.HttpException('创建退款申请失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
    async confirmRefundPickup(orderId, deliveryStaffId) {
        return await this.dataSource.transaction(async (manager) => {
            try {
                this.logger.log(`[DEBUG] 配送员确认取货: OrderId=${orderId}, DeliveryStaffId=${deliveryStaffId}`);
                const order = await manager.findOne(order_entity_1.OrderEntity, {
                    where: { orderId, status: order_status_enum_1.OrderStatus.REFUNDING, delFlag: '0' },
                });
                if (!order) {
                    throw new common_1.HttpException('订单不存在或状态不正确', common_1.HttpStatus.NOT_FOUND);
                }
                const refundRequest = await manager.findOne(refund_request_entity_1.RefundRequestEntity, {
                    where: { orderId, status: refund_request_dto_1.RefundRequestStatus.PENDING },
                });
                if (!refundRequest) {
                    throw new common_1.HttpException('未找到对应的退款申请记录', common_1.HttpStatus.NOT_FOUND);
                }
                const refundResult = await this.refundOrder(orderId, order.refundReason, '2');
                if (refundResult.code !== 200) {
                    throw new common_1.HttpException('退款处理失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                }
                order.status = order_status_enum_1.OrderStatus.CANCELLED;
                order.refundTime = new Date();
                order.updateBy = deliveryStaffId ? `delivery_${deliveryStaffId}` : 'delivery_staff';
                order.updateTime = new Date();
                await manager.save(order_entity_1.OrderEntity, order);
                refundRequest.status = refund_request_dto_1.RefundRequestStatus.REFUNDED;
                refundRequest.processTime = new Date();
                await manager.save(refund_request_entity_1.RefundRequestEntity, refundRequest);
                this.logger.log(`[DEBUG] 配送员确认取货成功，退款完成: OrderId=${orderId}`);
                return result_1.ResultData.ok(refundRequest, '取货确认成功，退款已完成');
            }
            catch (error) {
                this.logger.error(`[ERROR] 配送员确认取货失败: ${error.message}`, error.stack);
                if (error instanceof common_1.HttpException) {
                    throw error;
                }
                throw new common_1.HttpException('确认取货失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
    async processRefundRequest_DEPRECATED(processDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log(`[DEBUG] 处理退款申请: ${JSON.stringify(processDto)}`);
            const refundRequest = await queryRunner.manager.findOne(refund_request_entity_1.RefundRequestEntity, {
                where: { id: processDto.requestId },
                relations: ['order'],
            });
            if (!refundRequest) {
                throw new common_1.HttpException('退款申请不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (refundRequest.status !== refund_request_dto_1.RefundRequestStatus.PENDING) {
                throw new common_1.HttpException('该退款申请已被处理', common_1.HttpStatus.BAD_REQUEST);
            }
            const now = new Date();
            if (processDto.action === 'approve') {
                this.logger.log(`[DEBUG] 批准退款申请: RequestId=${processDto.requestId}, OrderId=${refundRequest.orderId}`);
                const refundResult = await this.refundOrder(refundRequest.orderId, processDto.adminComment || '管理员批准退款申请');
                if (refundResult.code === 200) {
                    await queryRunner.manager.update(refund_request_entity_1.RefundRequestEntity, { id: processDto.requestId }, {
                        status: refund_request_dto_1.RefundRequestStatus.REFUNDED,
                        adminComment: processDto.adminComment || '退款申请已批准',
                        processTime: now,
                    });
                    await queryRunner.commitTransaction();
                    this.logger.log(`[DEBUG] 退款申请批准成功: RequestId=${processDto.requestId}`);
                    return result_1.ResultData.ok(null, '退款申请批准成功，资金已退回');
                }
                else {
                    throw new common_1.HttpException(refundResult.msg || '退款处理失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
            else if (processDto.action === 'reject') {
                this.logger.log(`[DEBUG] 拒绝退款申请: RequestId=${processDto.requestId}`);
                await queryRunner.manager.update(refund_request_entity_1.RefundRequestEntity, { id: processDto.requestId }, {
                    status: refund_request_dto_1.RefundRequestStatus.REJECTED,
                    adminComment: processDto.adminComment || '退款申请被拒绝',
                    processTime: now,
                });
                await queryRunner.commitTransaction();
                this.logger.log(`[DEBUG] 退款申请拒绝成功: RequestId=${processDto.requestId}`);
                return result_1.ResultData.ok(null, '退款申请已拒绝');
            }
            throw new common_1.HttpException('无效的处理动作', common_1.HttpStatus.BAD_REQUEST);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`[ERROR] 处理退款申请失败: ${error.message}`, error.stack);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('处理退款申请失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.OrderService = OrderService;
exports.OrderService = OrderService = OrderService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_entity_1.OrderEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(payment_entity_1.PaymentEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.MiniprogramUser)),
    __param(3, (0, typeorm_1.InjectRepository)(refund_request_entity_1.RefundRequestEntity)),
    __param(5, (0, common_1.Inject)((0, common_1.forwardRef)(() => payment_service_1.PaymentService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        payment_service_1.PaymentService,
        notification_gateway_1.NotificationGateway])
], OrderService);
//# sourceMappingURL=order.service.js.map