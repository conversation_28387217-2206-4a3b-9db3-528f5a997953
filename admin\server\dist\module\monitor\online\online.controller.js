"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineController = void 0;
const common_1 = require("@nestjs/common");
const online_service_1 = require("./online.service");
const swagger_1 = require("@nestjs/swagger");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let OnlineController = class OnlineController {
    constructor(onlineService) {
        this.onlineService = onlineService;
    }
    findAll(query) {
        return this.onlineService.findAll(query);
    }
    delete(token) {
        return this.onlineService.delete(token);
    }
};
exports.OnlineController = OnlineController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '在线用户-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.OnlineListDto,
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:online:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], OnlineController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '在线用户-强退',
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:online:forceLogout'),
    (0, common_1.Delete)('/:token'),
    __param(0, (0, common_1.Param)('token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OnlineController.prototype, "delete", null);
exports.OnlineController = OnlineController = __decorate([
    (0, swagger_1.ApiTags)('系统监控-在线用户'),
    (0, common_1.Controller)('monitor/online'),
    __metadata("design:paramtypes", [online_service_1.OnlineService])
], OnlineController);
//# sourceMappingURL=online.controller.js.map