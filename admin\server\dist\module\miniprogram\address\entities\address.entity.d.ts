import { BaseEntity } from '../../../../common/entities/base';
import { MiniprogramUser } from '../../user/entities/user.entity';
export declare class UserAddress extends BaseEntity {
    addressId: number;
    userId: number;
    receiverName: string;
    receiverPhone: string;
    addressName: string;
    detailAddress: string;
    areaCode: string;
    postalCode: string;
    isDefault: number;
    label: string;
    latitude: string;
    longitude: string;
    user: MiniprogramUser;
}
