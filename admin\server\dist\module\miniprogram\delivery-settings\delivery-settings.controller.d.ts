import { DeliverySettingsService } from './delivery-settings.service';
import { CreateDeliverySettingsDto, UpdateDeliverySettingsDto, DeliverySettingsQueryDto, CreateDeliveryTimeSlotDto, UpdateDeliveryTimeSlotDto, DeliveryTimeSlotQueryDto } from './dto';
export declare class DeliverySettingsController {
    private readonly deliverySettingsService;
    private readonly logger;
    constructor(deliverySettingsService: DeliverySettingsService);
    create(createDeliverySettingsDto: CreateDeliverySettingsDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(queryDto: DeliverySettingsQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    update(id: number, updateDeliverySettingsDto: UpdateDeliverySettingsDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(id: number): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: number): Promise<import("../../../common/utils/result").ResultData>;
    getUniqueSettings(): Promise<import("../../../common/utils/result").ResultData>;
    checkDeliveryRange(body: {
        latitude: number;
        longitude: number;
    }): Promise<import("../../../common/utils/result").ResultData>;
    createTimeSlot(createDto: CreateDeliveryTimeSlotDto): Promise<import("../../../common/utils/result").ResultData>;
    findAllTimeSlots(queryDto: DeliveryTimeSlotQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    findOneTimeSlot(id: number): Promise<import("../../../common/utils/result").ResultData>;
    updateTimeSlot(id: number, updateDto: UpdateDeliveryTimeSlotDto): Promise<import("../../../common/utils/result").ResultData>;
    removeTimeSlot(id: number): Promise<import("../../../common/utils/result").ResultData>;
    getActiveTimeSlots(): Promise<import("../../../common/utils/result").ResultData>;
}
