import { ConfigService } from '@nestjs/config';
import { RedisService } from 'src/module/common/redis/redis.service';
declare const AuthStrategy_base: new (...args: any[]) => any;
export declare class AuthStrategy extends AuthStrategy_base {
    private readonly config;
    private readonly redisService;
    constructor(config: ConfigService, redisService: RedisService);
    validate(payload: {
        uuid: string;
        userId: string;
        iat: Date;
    }): Promise<any>;
}
export {};
