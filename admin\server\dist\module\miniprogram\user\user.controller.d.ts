import { Request } from 'express';
import { UserService } from './user.service';
import { WechatPhoneAuthDto } from './dto/create-user.dto';
import { UpdateUserDto, AdminUpdateUserDto } from './dto/update-user.dto';
import { UserQueryDto } from './dto/user-profile.dto';
import { ResultData } from '../../../common/utils/result';
export declare class UserController {
    private readonly userService;
    private readonly logger;
    constructor(userService: UserService);
    wechatPhoneAuth(wechatPhoneAuthDto: WechatPhoneAuthDto, req: Request): Promise<ResultData>;
    getUserProfile(userId: number): Promise<ResultData>;
    updateUserProfile(userId: number, updateUserDto: UpdateUserDto): Promise<ResultData>;
    getUserList(queryDto: UserQueryDto): Promise<ResultData>;
    adminUpdateUser(userId: number, updateDto: AdminUpdateUserDto): Promise<ResultData>;
    getUserStats(userId: number): Promise<ResultData>;
}
