"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerService = void 0;
const common_1 = require("@nestjs/common");
const result_1 = require("../../../common/utils/result");
const os_1 = __importStar(require("os"));
const path_1 = __importDefault(require("path"));
const nodeDiskInfo = __importStar(require("node-disk-info"));
let ServerService = class ServerService {
    async getInfo() {
        const cpu = this.getCpuInfo();
        const mem = this.getMemInfo();
        const sys = {
            computerName: os_1.default.hostname(),
            computerIp: this.getServerIP(),
            userDir: path_1.default.resolve(__dirname, '..', '..', '..', '..'),
            osName: os_1.default.platform(),
            osArch: os_1.default.arch(),
        };
        const sysFiles = await this.getDiskStatus();
        const data = {
            cpu,
            mem,
            sys,
            sysFiles,
        };
        return result_1.ResultData.ok(data);
    }
    async getDiskStatus() {
        const disks = await nodeDiskInfo.getDiskInfoSync();
        const sysFiles = disks.map((disk) => {
            return {
                dirName: disk._mounted,
                typeName: disk._filesystem,
                total: this.bytesToGB(disk._blocks) + 'GB',
                used: this.bytesToGB(disk._used) + 'GB',
                free: this.bytesToGB(disk._available) + 'GB',
                usage: ((disk._used / disk._blocks || 0) * 100).toFixed(2),
            };
        });
        return sysFiles;
    }
    getServerIP() {
        const nets = (0, os_1.networkInterfaces)();
        for (const name of Object.keys(nets)) {
            for (const net of nets[name]) {
                if (net.family === 'IPv4' && !net.internal) {
                    return net.address;
                }
            }
        }
    }
    getCpuInfo() {
        const cpus = os_1.default.cpus();
        const cpuInfo = cpus.reduce((info, cpu) => {
            info.cpuNum += 1;
            info.user += cpu.times.user;
            info.sys += cpu.times.sys;
            info.idle += cpu.times.idle;
            info.total += cpu.times.user + cpu.times.sys + cpu.times.idle;
            return info;
        }, { user: 0, sys: 0, idle: 0, total: 0, cpuNum: 0 });
        const cpu = {
            cpuNum: cpuInfo.cpuNum,
            total: cpuInfo.total,
            sys: ((cpuInfo.sys / cpuInfo.total) * 100).toFixed(2),
            used: ((cpuInfo.user / cpuInfo.total) * 100).toFixed(2),
            wait: 0.0,
            free: ((cpuInfo.idle / cpuInfo.total) * 100).toFixed(2),
        };
        return cpu;
    }
    getMemInfo() {
        const totalMemory = os_1.default.totalmem();
        const freeMemory = os_1.default.freemem();
        const usedMemory = totalMemory - freeMemory;
        const memoryUsagePercentage = (((totalMemory - freeMemory) / totalMemory) * 100).toFixed(2);
        const mem = {
            total: this.bytesToGB(totalMemory),
            used: this.bytesToGB(usedMemory),
            free: this.bytesToGB(freeMemory),
            usage: memoryUsagePercentage,
        };
        return mem;
    }
    bytesToGB(bytes) {
        const gb = bytes / (1024 * 1024 * 1024);
        return gb.toFixed(2);
    }
};
exports.ServerService = ServerService;
exports.ServerService = ServerService = __decorate([
    (0, common_1.Injectable)()
], ServerService);
//# sourceMappingURL=server.service.js.map