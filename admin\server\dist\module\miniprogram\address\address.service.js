"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const address_entity_1 = require("./entities/address.entity");
const delivery_settings_service_1 = require("../delivery-settings/delivery-settings.service");
let AddressService = class AddressService {
    constructor(addressRepository, deliverySettingsService) {
        this.addressRepository = addressRepository;
        this.deliverySettingsService = deliverySettingsService;
    }
    async create(userId, createAddressDto) {
        try {
            console.log(`用户 ${userId} 开始创建地址:`, JSON.stringify(createAddressDto));
            const deliveryCheck = await this.checkDeliveryRange({
                addressName: createAddressDto.addressName,
                detailAddress: createAddressDto.detailAddress,
                latitude: createAddressDto.latitude,
                longitude: createAddressDto.longitude,
            });
            if (!deliveryCheck.inRange) {
                throw new common_1.BadRequestException(deliveryCheck.message || '该地址不在配送范围内');
            }
            if (createAddressDto.isDefault === 1) {
                await this.clearDefaultAddress(userId);
                console.log(`已清除用户 ${userId} 的其他默认地址`);
            }
            const address = this.addressRepository.create({
                ...createAddressDto,
                userId,
                createBy: `user_${userId}`,
                updateBy: `user_${userId}`,
            });
            const savedAddress = await this.addressRepository.save(address);
            console.log(`地址创建成功，地址ID: ${savedAddress.addressId}`);
            return savedAddress;
        }
        catch (error) {
            console.error('创建地址失败:', error.message, error.stack);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('创建地址失败');
        }
    }
    async findByUserId(userId, queryDto) {
        try {
            console.log(`获取用户 ${userId} 的地址列表，查询条件:`, JSON.stringify(queryDto));
            const queryBuilder = this.addressRepository.createQueryBuilder('address').where('address.userId = :userId', { userId }).andWhere('address.delFlag = :delFlag', { delFlag: '0' });
            if (queryDto?.label) {
                queryBuilder.andWhere('address.label = :label', { label: queryDto.label });
            }
            if (queryDto?.isDefault !== undefined) {
                queryBuilder.andWhere('address.isDefault = :isDefault', { isDefault: queryDto.isDefault });
            }
            queryBuilder.orderBy('address.isDefault', 'DESC').addOrderBy('address.createTime', 'DESC');
            const addresses = await queryBuilder.getMany();
            console.log(`用户 ${userId} 共有 ${addresses.length} 个地址`);
            return addresses;
        }
        catch (error) {
            console.error('获取地址列表失败:', error.message, error.stack);
            throw new common_1.BadRequestException('获取地址列表失败');
        }
    }
    async findById(addressId, userId) {
        try {
            console.log(`获取地址详情，地址ID: ${addressId}${userId ? `，用户ID: ${userId}` : ''}`);
            const whereCondition = {
                addressId,
                delFlag: '0',
            };
            if (userId) {
                whereCondition.userId = userId;
            }
            const address = await this.addressRepository.findOne({
                where: whereCondition,
            });
            if (!address) {
                console.warn(`地址不存在${userId ? '或无权限访问' : ''}，地址ID: ${addressId}${userId ? `，用户ID: ${userId}` : ''}`);
                throw new common_1.NotFoundException('地址不存在');
            }
            console.log(`地址详情获取成功:`, JSON.stringify(address));
            return address;
        }
        catch (error) {
            console.error('获取地址详情失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('获取地址详情失败');
        }
    }
    async update(addressId, userId, updateAddressDto) {
        try {
            console.log(`用户 ${userId} 更新地址 ${addressId}:`, JSON.stringify(updateAddressDto));
            const existingAddress = await this.findById(addressId, userId);
            if (updateAddressDto.addressName || updateAddressDto.detailAddress || updateAddressDto.latitude || updateAddressDto.longitude) {
                const addressName = updateAddressDto.addressName || existingAddress.addressName;
                const detailAddress = updateAddressDto.detailAddress || existingAddress.detailAddress;
                const latitude = updateAddressDto.latitude || existingAddress.latitude;
                const longitude = updateAddressDto.longitude || existingAddress.longitude;
                const deliveryCheck = await this.checkDeliveryRange({
                    addressName,
                    detailAddress,
                    latitude,
                    longitude,
                });
                if (!deliveryCheck.inRange) {
                    throw new common_1.BadRequestException(deliveryCheck.message || '该地址不在配送范围内');
                }
            }
            if (updateAddressDto.isDefault === 1) {
                await this.clearDefaultAddress(userId);
                console.log(`已清除用户 ${userId} 的其他默认地址`);
            }
            await this.addressRepository.update({ addressId, userId }, {
                ...updateAddressDto,
                updateBy: `user_${userId}`,
            });
            const updatedAddress = await this.findById(addressId, userId);
            console.log(`地址更新成功:`, JSON.stringify(updatedAddress));
            return updatedAddress;
        }
        catch (error) {
            console.error('更新地址失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('更新地址失败');
        }
    }
    async setDefault(addressId, userId) {
        try {
            console.log(`用户 ${userId} 设置默认地址: ${addressId}`);
            const address = await this.findById(addressId, userId);
            await this.clearDefaultAddress(userId);
            await this.addressRepository.update({ addressId, userId }, {
                isDefault: 1,
                updateBy: `user_${userId}`,
            });
            const updatedAddress = await this.findById(addressId, userId);
            console.log(`默认地址设置成功:`, JSON.stringify(updatedAddress));
            return updatedAddress;
        }
        catch (error) {
            console.error('设置默认地址失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('设置默认地址失败');
        }
    }
    async remove(addressId, userId) {
        try {
            console.log(`用户 ${userId} 删除地址: ${addressId}`);
            await this.findById(addressId, userId);
            await this.addressRepository.update({ addressId, userId }, {
                delFlag: '1',
                updateBy: `user_${userId}`,
            });
            console.log(`地址删除成功，地址ID: ${addressId}`);
        }
        catch (error) {
            console.error('删除地址失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('删除地址失败');
        }
    }
    async getDefaultAddress(userId) {
        try {
            console.log(`获取用户 ${userId} 的默认地址`);
            const defaultAddress = await this.addressRepository.findOne({
                where: {
                    userId,
                    isDefault: 1,
                    delFlag: '0',
                },
            });
            if (defaultAddress) {
                console.log(`用户 ${userId} 的默认地址:`, JSON.stringify(defaultAddress));
            }
            else {
                console.log(`用户 ${userId} 暂无默认地址`);
            }
            return defaultAddress;
        }
        catch (error) {
            console.error('获取默认地址失败:', error.message, error.stack);
            throw new common_1.BadRequestException('获取默认地址失败');
        }
    }
    async clearDefaultAddress(userId) {
        try {
            await this.addressRepository.update({ userId, isDefault: 1 }, { isDefault: 0, updateBy: `user_${userId}` });
        }
        catch (error) {
            console.error('清除默认地址失败:', error.message, error.stack);
            throw new common_1.BadRequestException('清除默认地址失败');
        }
    }
    async getAddressStats(userId) {
        try {
            console.log(`获取用户 ${userId} 的地址统计信息`);
            const [total, defaultCount] = await Promise.all([
                this.addressRepository.count({
                    where: { userId, delFlag: '0' },
                }),
                this.addressRepository.count({
                    where: { userId, isDefault: 1, delFlag: '0' },
                }),
            ]);
            const stats = {
                total,
                hasDefault: defaultCount > 0,
            };
            console.log(`用户 ${userId} 地址统计:`, JSON.stringify(stats));
            return stats;
        }
        catch (error) {
            console.error('获取地址统计失败:', error.message, error.stack);
            throw new common_1.BadRequestException('获取地址统计失败');
        }
    }
    async checkDeliveryRange(deliveryRangeDto) {
        try {
            console.log('开始检查配送范围:', JSON.stringify(deliveryRangeDto));
            if (deliveryRangeDto.latitude && deliveryRangeDto.longitude) {
                const latitude = typeof deliveryRangeDto.latitude === 'string' ? parseFloat(deliveryRangeDto.latitude) : deliveryRangeDto.latitude;
                const longitude = typeof deliveryRangeDto.longitude === 'string' ? parseFloat(deliveryRangeDto.longitude) : deliveryRangeDto.longitude;
                const result = await this.deliverySettingsService.checkDeliveryRange(latitude, longitude);
                if (result.code !== 200 || !result.data) {
                    return {
                        inRange: false,
                        message: result.msg || '检查配送范围失败',
                    };
                }
                const rangeData = result.data;
                if (!rangeData.inRange) {
                    return {
                        inRange: false,
                        message: '该地址超出配送范围，无法提供配送服务',
                    };
                }
                return {
                    inRange: true,
                    distance: rangeData.distance,
                    message: `在配送范围内，距离${rangeData.storeName}约${rangeData.distance}公里`,
                };
            }
            console.log('没有经纬度信息，暂时允许通过');
            return {
                inRange: true,
                message: '请确保您的地址在配送范围内',
            };
        }
        catch (error) {
            console.error('检查配送范围失败:', error.message, error.stack);
            throw new common_1.BadRequestException('检查配送范围失败');
        }
    }
    calculateDistance(lat1, lon1, lat2, lon2) {
        const latitude1 = typeof lat1 === 'string' ? parseFloat(lat1) : lat1;
        const longitude1 = typeof lon1 === 'string' ? parseFloat(lon1) : lon1;
        const latitude2 = typeof lat2 === 'string' ? parseFloat(lat2) : lat2;
        const longitude2 = typeof lon2 === 'string' ? parseFloat(lon2) : lon2;
        const R = 6371;
        const dLat = this.deg2rad(latitude2 - latitude1);
        const dLon = this.deg2rad(longitude2 - longitude1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(latitude1)) * Math.cos(this.deg2rad(latitude2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        return distance;
    }
    deg2rad(deg) {
        return deg * (Math.PI / 180);
    }
    async findAll(queryParams) {
        try {
            console.log('管理员获取地址列表，查询条件:', JSON.stringify(queryParams));
            const { pageNum, pageSize, userId, receiverName, receiverPhone, addressName } = queryParams;
            const skip = (pageNum - 1) * pageSize;
            const queryBuilder = this.addressRepository.createQueryBuilder('address').where('address.delFlag = :delFlag', { delFlag: '0' }).orderBy('address.createTime', 'DESC').skip(skip).take(pageSize);
            if (userId) {
                queryBuilder.andWhere('address.userId = :userId', { userId });
            }
            if (receiverName) {
                queryBuilder.andWhere('address.receiverName LIKE :receiverName', { receiverName: `%${receiverName}%` });
            }
            if (receiverPhone) {
                queryBuilder.andWhere('address.receiverPhone LIKE :receiverPhone', { receiverPhone: `%${receiverPhone}%` });
            }
            if (addressName) {
                queryBuilder.andWhere('address.addressName LIKE :addressName', { addressName: `%${addressName}%` });
            }
            const [list, total] = await queryBuilder.getManyAndCount();
            console.log(`管理员获取地址列表成功，共 ${total} 条`);
            return [list, total];
        }
        catch (error) {
            console.error('管理员获取地址列表失败:', error.message, error.stack);
            throw new common_1.BadRequestException('获取地址列表失败');
        }
    }
    async adminUpdate(addressId, updateAddressDto) {
        try {
            console.log(`管理员更新地址 ${addressId}:`, JSON.stringify(updateAddressDto));
            const existingAddress = await this.findById(addressId);
            await this.addressRepository.update({ addressId }, {
                ...updateAddressDto,
                updateBy: 'admin',
            });
            const updatedAddress = await this.findById(addressId);
            console.log(`地址更新成功:`, JSON.stringify(updatedAddress));
            return updatedAddress;
        }
        catch (error) {
            console.error('管理员更新地址失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('更新地址失败');
        }
    }
    async adminRemove(addressId) {
        try {
            console.log(`管理员删除地址: ${addressId}`);
            await this.findById(addressId);
            await this.addressRepository.update({ addressId }, {
                delFlag: '1',
                updateBy: 'admin',
            });
            console.log(`地址删除成功，地址ID: ${addressId}`);
        }
        catch (error) {
            console.error('管理员删除地址失败:', error.message, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('删除地址失败');
        }
    }
    async getAdminAddressStats() {
        try {
            console.log('获取管理员地址统计信息');
            const totalCount = await this.addressRepository.count({
                where: {
                    delFlag: '0',
                },
            });
            const defaultCount = await this.addressRepository.count({
                where: {
                    delFlag: '0',
                    isDefault: 1,
                },
            });
            const userCount = await this.addressRepository.createQueryBuilder('address').select('address.userId').where('address.delFlag = :delFlag', { delFlag: '0' }).groupBy('address.userId').getCount();
            const provinceDistribution = await this.addressRepository
                .createQueryBuilder('address')
                .select('address.province', 'province')
                .addSelect('COUNT(*)', 'count')
                .where('address.delFlag = :delFlag', { delFlag: '0' })
                .groupBy('address.province')
                .orderBy('count', 'DESC')
                .limit(10)
                .getRawMany();
            const cityDistribution = await this.addressRepository
                .createQueryBuilder('address')
                .select('address.city', 'city')
                .addSelect('COUNT(*)', 'count')
                .where('address.delFlag = :delFlag', { delFlag: '0' })
                .groupBy('address.city')
                .orderBy('count', 'DESC')
                .limit(10)
                .getRawMany();
            const stats = {
                totalCount,
                defaultCount,
                userCount,
                provinceDistribution,
                cityDistribution,
                avgAddressPerUser: userCount > 0 ? (totalCount / userCount).toFixed(2) : 0,
            };
            console.log('地址统计信息:', JSON.stringify(stats));
            return stats;
        }
        catch (error) {
            console.error('获取地址统计信息失败:', error.message, error.stack);
            throw new common_1.BadRequestException('获取地址统计信息失败');
        }
    }
};
exports.AddressService = AddressService;
exports.AddressService = AddressService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(address_entity_1.UserAddress)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        delivery_settings_service_1.DeliverySettingsService])
], AddressService);
//# sourceMappingURL=address.service.js.map