import { Repository } from 'typeorm';
import { IconNavEntity } from './entities/iconNav.entity';
import { CreateIconNavDto } from './dto/create-iconNav.dto';
import { UpdateIconNavDto } from './dto/update-iconNav.dto';
import { QueryIconNavDto } from './dto/query-iconNav.dto';
import { UploadService } from '../../upload/upload.service';
export declare class IconNavService {
    private readonly iconNavRepository;
    private readonly uploadService;
    private readonly logger;
    constructor(iconNavRepository: Repository<IconNavEntity>, uploadService: UploadService);
    create(createIconNavDto: CreateIconNavDto, userId: string): Promise<IconNavEntity>;
    findAll(queryParams: QueryIconNavDto): Promise<{
        rows: IconNavEntity[];
        total: number;
        pageNum: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(iconId: number): Promise<IconNavEntity>;
    update(iconId: number, updateIconNavDto: UpdateIconNavDto, userId: string): Promise<IconNavEntity>;
    remove(iconId: number, userId: string): Promise<boolean>;
    getMiniIconNavs(): Promise<IconNavEntity[]>;
}
