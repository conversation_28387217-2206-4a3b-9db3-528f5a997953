"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const axios_service_1 = require("../../common/axios/axios.service");
const user_entity_1 = require("./entities/user.entity");
const result_1 = require("../../../common/utils/result");
let UserService = UserService_1 = class UserService {
    constructor(userRepository, configService, axiosService, dataSource) {
        this.userRepository = userRepository;
        this.configService = configService;
        this.axiosService = axiosService;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(UserService_1.name);
    }
    async wechatPhoneAuth(wechatPhoneAuthDto, ip) {
        try {
            this.logger.log(`微信授权手机号登录/注册开始，参数: ${JSON.stringify(wechatPhoneAuthDto)}, IP: ${ip}`);
            this.logger.log(`开始调用微信接口获取手机号，code: ${wechatPhoneAuthDto.code}`);
            const phoneInfo = await this.getWechatPhoneNumber(wechatPhoneAuthDto.code);
            if (!phoneInfo || !phoneInfo.phoneNumber) {
                throw new common_1.HttpException('获取手机号失败', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`微信授权获取到手机号: ${phoneInfo.phoneNumber}`);
            let wechatUserInfo = null;
            if (wechatPhoneAuthDto.loginCode) {
                this.logger.log(`开始获取微信用户信息，loginCode: ${wechatPhoneAuthDto.loginCode}`);
                try {
                    wechatUserInfo = await this.getWechatUserInfo(wechatPhoneAuthDto.loginCode);
                    this.logger.log(`获取微信用户信息成功: ${JSON.stringify(wechatUserInfo)}`);
                }
                catch (error) {
                    this.logger.warn(`获取微信用户信息失败: ${error.message}`, error.stack);
                }
            }
            else {
                this.logger.log(`未提供loginCode，跳过获取微信用户信息`);
            }
            this.logger.log(`开始查找用户，手机号: ${phoneInfo.phoneNumber}, openid: ${wechatUserInfo?.openid || 'null'}`);
            const findConditions = [{ phone: phoneInfo.phoneNumber }, ...(wechatUserInfo?.openid ? [{ openid: wechatUserInfo.openid }] : [])];
            this.logger.log(`查找用户条件: ${JSON.stringify(findConditions)}`);
            let user = await this.userRepository.findOne({
                where: findConditions,
            });
            this.logger.log(`查找用户结果: ${user ? `找到用户，userId: ${user.userId}` : '未找到用户'}`);
            let isNewUser = false;
            if (user) {
                this.logger.log(`用户已存在，更新信息，userId: ${user.userId}`);
                if (!user.phone) {
                    user.phone = phoneInfo.phoneNumber;
                }
                if (wechatUserInfo) {
                    user.openid = wechatUserInfo.openid;
                    user.unionid = wechatUserInfo.unionid || user.unionid;
                    if (!user.nickname || user.nickname.includes('用户')) {
                        user.nickname = wechatUserInfo.nickname || user.nickname;
                    }
                    if (!user.avatar || user.avatar === '') {
                        user.avatar = wechatUserInfo.avatarUrl || user.avatar;
                    }
                    user.gender = wechatUserInfo.gender?.toString() || user.gender;
                }
                if (wechatPhoneAuthDto.nickname && (!user.nickname || user.nickname.includes('用户'))) {
                    user.nickname = wechatPhoneAuthDto.nickname;
                }
                if (wechatPhoneAuthDto.avatar && (!user.avatar || user.avatar === '')) {
                    user.avatar = wechatPhoneAuthDto.avatar;
                }
                if (wechatPhoneAuthDto.gender) {
                    user.gender = wechatPhoneAuthDto.gender;
                }
                user.lastLoginTime = new Date();
                user.lastLoginIp = ip;
                user.updateBy = 'system';
                this.logger.log(`开始保存已存在用户的更新信息`);
                await this.userRepository.save(user);
                this.logger.log(`已存在用户信息更新成功`);
            }
            else {
                isNewUser = true;
                this.logger.log(`创建新用户，手机号: ${phoneInfo.phoneNumber}`);
                const newUserData = {
                    phone: phoneInfo.phoneNumber,
                    openid: wechatUserInfo?.openid || '',
                    unionid: wechatUserInfo?.unionid || '',
                    nickname: wechatPhoneAuthDto.nickname || wechatUserInfo?.nickname || `用户${phoneInfo.phoneNumber.slice(-4)}`,
                    avatar: wechatPhoneAuthDto.avatar || wechatUserInfo?.avatarUrl || '',
                    gender: wechatPhoneAuthDto.gender || wechatUserInfo?.gender?.toString() || '0',
                    lastLoginTime: new Date(),
                    lastLoginIp: ip,
                    createBy: 'system',
                };
                this.logger.log(`准备创建新用户，数据: ${JSON.stringify(newUserData)}`);
                user = this.userRepository.create(newUserData);
                this.logger.log(`开始保存新用户到数据库`);
                await this.userRepository.save(user);
                this.logger.log(`新用户创建成功，userId: ${user.userId}`);
            }
            this.logger.log(`开始格式化用户资料，userId: ${user.userId}`);
            const formattedUser = this.formatUserProfile(user);
            this.logger.log(`用户资料格式化完成`);
            const result = {
                user: formattedUser,
                isNewUser,
                message: isNewUser ? '注册成功' : '登录成功',
            };
            this.logger.log(`微信授权手机号登录/注册成功，返回结果: ${JSON.stringify(result)}`);
            return result;
        }
        catch (error) {
            this.logger.error(`微信授权手机号登录/注册失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getWechatPhoneNumber(code) {
        try {
            this.logger.log(`开始获取微信手机号，code: ${code}`);
            const appId = this.configService.get('wechat.appid');
            const appSecret = this.configService.get('wechat.secret');
            this.logger.log(`微信配置 - appId: ${appId}, appSecret: ${appSecret ? '已配置' : '未配置'}`);
            const tokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
            this.logger.log(`开始请求微信访问token，URL: ${tokenUrl}`);
            const tokenResponse = await this.axiosService.get(tokenUrl);
            this.logger.log(`微信访问token响应: ${JSON.stringify(tokenResponse)}`);
            if (!tokenResponse.access_token) {
                this.logger.error(`获取微信访问token失败: ${JSON.stringify(tokenResponse)}`);
                throw new common_1.HttpException('获取微信访问token失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            this.logger.log(`微信访问token获取成功: ${tokenResponse.access_token}`);
            const phoneUrl = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${tokenResponse.access_token}`;
            const phoneData = { code };
            this.logger.log(`开始请求微信手机号，URL: ${phoneUrl}, 数据: ${JSON.stringify(phoneData)}`);
            const phoneResponse = await this.axiosService.post(phoneUrl, phoneData);
            this.logger.log(`微信手机号响应: ${JSON.stringify(phoneResponse)}`);
            if (phoneResponse.errcode !== 0) {
                this.logger.error(`获取微信手机号失败: ${JSON.stringify(phoneResponse)}`);
                throw new common_1.HttpException('获取手机号失败', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`微信手机号获取成功: ${phoneResponse.phone_info.phoneNumber}`);
            return phoneResponse.phone_info;
        }
        catch (error) {
            this.logger.error(`获取微信手机号异常: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getWechatUserInfo(code) {
        try {
            this.logger.log(`开始获取微信用户信息，code: ${code}`);
            const appId = this.configService.get('wechat.appid');
            const appSecret = this.configService.get('wechat.secret');
            const userInfoUrl = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
            this.logger.log(`开始请求微信用户信息，URL: ${userInfoUrl}`);
            const response = await this.axiosService.get(userInfoUrl);
            this.logger.log(`微信用户信息响应: ${JSON.stringify(response)}`);
            if (response.errcode) {
                this.logger.error(`获取微信session失败: ${JSON.stringify(response)}`);
                throw new common_1.HttpException('获取微信用户信息失败', common_1.HttpStatus.BAD_REQUEST);
            }
            return response;
        }
        catch (error) {
            this.logger.error(`获取微信用户信息异常: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getUserProfile(userId) {
        try {
            const user = await this.userRepository.findOne({
                where: { userId },
            });
            if (!user) {
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return this.formatUserProfile(user);
        }
        catch (error) {
            this.logger.error(`获取用户资料失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateUserProfile(userId, updateUserDto) {
        try {
            const user = await this.userRepository.findOne({
                where: { userId },
            });
            if (!user) {
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            Object.assign(user, updateUserDto);
            user.updateBy = 'user';
            user.updateTime = new Date();
            await this.userRepository.save(user);
            return this.formatUserProfile(user);
        }
        catch (error) {
            this.logger.error(`更新用户资料失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getUserList(queryDto) {
        try {
            const { pageNum = 1, pageSize = 10, nickname, phone, status, startTime, endTime } = queryDto;
            const queryBuilder = this.userRepository.createQueryBuilder('user');
            if (nickname) {
                queryBuilder.andWhere('user.nickname LIKE :nickname', { nickname: `%${nickname}%` });
            }
            if (phone) {
                queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${phone}%` });
            }
            if (status !== undefined) {
                queryBuilder.andWhere('user.status = :status', { status });
            }
            if (startTime && endTime) {
                queryBuilder.andWhere('user.createTime BETWEEN :startTime AND :endTime', {
                    startTime: new Date(startTime),
                    endTime: new Date(endTime),
                });
            }
            queryBuilder.orderBy('user.createTime', 'DESC');
            queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);
            const [users, total] = await queryBuilder.getManyAndCount();
            return {
                list: users.map((user) => this.formatUserProfile(user)),
                total,
                pageNum,
                pageSize,
            };
        }
        catch (error) {
            this.logger.error(`获取用户列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async adminUpdateUser(userId, updateDto) {
        try {
            const user = await this.userRepository.findOne({
                where: { userId },
            });
            if (!user) {
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            Object.assign(user, updateDto);
            user.updateBy = 'admin';
            user.updateTime = new Date();
            await this.userRepository.save(user);
            return this.formatUserProfile(user);
        }
        catch (error) {
            this.logger.error(`管理员更新用户信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getUserStats(userId) {
        this.logger.log(`获取用户统计信息: userId=${userId}`);
        try {
            const user = await this.userRepository.findOne({
                where: { userId, delFlag: '0' },
            });
            if (!user) {
                this.logger.error(`用户不存在: userId=${userId}`);
                return result_1.ResultData.fail(404, '用户不存在');
            }
            const footprintCount = await this.dataSource.query('SELECT COUNT(*) as count FROM user_footprint WHERE user_id = ? AND del_flag = ?', [userId, '0']);
            const favoriteCount = await this.dataSource.query('SELECT COUNT(*) as count FROM user_favorites WHERE user_id = ? AND del_flag = ?', [userId.toString(), '0']);
            const couponCount = await this.dataSource.query('SELECT COUNT(*) as count FROM t_user_coupon WHERE user_id = ? AND status = ? AND del_flag = ?', [userId, '0', '0']);
            const stats = {
                footprintCount: parseInt(footprintCount[0]?.count || '0'),
                favoriteCount: parseInt(favoriteCount[0]?.count || '0'),
                couponCount: parseInt(couponCount[0]?.count || '0'),
                balance: user.balance || 0,
            };
            this.logger.log(`获取用户统计信息成功: ${JSON.stringify(stats)}`);
            return result_1.ResultData.ok(stats, '获取成功');
        }
        catch (error) {
            this.logger.error(`获取用户统计信息失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取用户统计信息失败');
        }
    }
    formatUserProfile(user) {
        return {
            userId: user.userId,
            openid: user.openid,
            unionid: user.unionid,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            email: user.email,
            gender: user.gender,
            birthday: user.birthday,
            region: user.region,
            status: user.status,
            remark: user.remark,
            lastLoginTime: user.lastLoginTime,
            createTime: user.createTime,
            userType: user.userType,
            vipExpireTime: user.vipExpireTime,
            pushEnabled: user.pushEnabled,
            tags: user.tags,
        };
    }
};
exports.UserService = UserService;
exports.UserService = UserService = UserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.MiniprogramUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        config_1.ConfigService,
        axios_service_1.AxiosService,
        typeorm_2.DataSource])
], UserService);
//# sourceMappingURL=user.service.js.map