"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PagingDto = exports.DateParamsDTO = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const index_1 = require("../enum/index");
class DateParamsDTO {
}
exports.DateParamsDTO = DateParamsDTO;
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], DateParamsDTO.prototype, "beginTime", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], DateParamsDTO.prototype, "endTime", void 0);
class PagingDto {
}
exports.PagingDto = PagingDto;
__decorate([
    (0, swagger_1.ApiProperty)({ required: true, description: '当前分页', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        return value?.toString?.() || '1';
    }),
    (0, class_validator_1.IsNumberString)(),
    __metadata("design:type", Number)
], PagingDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: true, description: '每页数量', default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        return value?.toString?.() || '10';
    }),
    (0, class_validator_1.IsNumberString)(),
    __metadata("design:type", Number)
], PagingDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: '时间范围' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", DateParamsDTO)
], PagingDto.prototype, "params", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: '排序字段' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PagingDto.prototype, "orderByColumn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: '排序规则' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(index_1.SortRuleEnum),
    __metadata("design:type", String)
], PagingDto.prototype, "isAsc", void 0);
//# sourceMappingURL=index.js.map