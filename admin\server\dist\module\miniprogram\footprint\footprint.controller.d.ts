import { FootprintService } from './footprint.service';
import { ResultData } from '../../../common/utils/result';
export declare class FootprintController {
    private readonly footprintService;
    private readonly logger;
    constructor(footprintService: FootprintService);
    findUserFootprints(userId: number, pageNum?: number, pageSize?: number): Promise<ResultData>;
    removeFootprint(footprintId: number, userId: number): Promise<ResultData>;
    removeFootprints(body: {
        userId: number;
        footprintIds: number[];
    }): Promise<ResultData>;
    clearUserFootprints(userId: number): Promise<ResultData>;
}
