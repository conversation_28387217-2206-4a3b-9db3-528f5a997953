"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserAddress = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const user_entity_1 = require("../../user/entities/user.entity");
let UserAddress = class UserAddress extends base_1.BaseEntity {
};
exports.UserAddress = UserAddress;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '地址ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'address_id', comment: '地址ID' }),
    __metadata("design:type", Number)
], UserAddress.prototype, "addressId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], UserAddress.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '收货人姓名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'receiver_name', length: 50, comment: '收货人姓名' }),
    __metadata("design:type", String)
], UserAddress.prototype, "receiverName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '收货人电话' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'receiver_phone', length: 20, comment: '收货人电话' }),
    __metadata("design:type", String)
], UserAddress.prototype, "receiverPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '地址名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'address_name', length: 100, comment: '地址名称' }),
    __metadata("design:type", String)
], UserAddress.prototype, "addressName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '详细地址' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'detail_address', length: 200, comment: '详细地址' }),
    __metadata("design:type", String)
], UserAddress.prototype, "detailAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '地区编码' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'area_code', length: 20, default: '', comment: '地区编码' }),
    __metadata("design:type", String)
], UserAddress.prototype, "areaCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '邮政编码' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'postal_code', length: 10, default: '', comment: '邮政编码' }),
    __metadata("design:type", String)
], UserAddress.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Boolean, description: '是否默认地址' }),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'is_default', default: 0, comment: '是否默认地址：0-否，1-是' }),
    __metadata("design:type", Number)
], UserAddress.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '地址标签' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'label', length: 20, default: '', comment: '地址标签：家、公司、学校等' }),
    __metadata("design:type", String)
], UserAddress.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '纬度' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'latitude', length: 20, nullable: true, comment: '纬度' }),
    __metadata("design:type", String)
], UserAddress.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '经度' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'longitude', length: 20, nullable: true, comment: '经度' }),
    __metadata("design:type", String)
], UserAddress.prototype, "longitude", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], UserAddress.prototype, "user", void 0);
exports.UserAddress = UserAddress = __decorate([
    (0, typeorm_1.Entity)('miniprogram_user_address', { comment: '用户收货地址表' })
], UserAddress);
//# sourceMappingURL=address.entity.js.map