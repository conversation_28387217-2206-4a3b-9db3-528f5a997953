"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const typeorm_1 = require("typeorm");
const common_1 = require("@nestjs/common");
const typeorm_2 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const redis_service_1 = require("../../common/redis/redis.service");
const bcrypt = __importStar(require("bcryptjs"));
const index_1 = require("../../../common/utils/index");
const export_1 = require("../../../common/utils/export");
const index_2 = require("../../../common/enum/index");
const index_3 = require("../../../common/constant/index");
const result_1 = require("../../../common/utils/result");
const index_4 = require("./dto/index");
const index_5 = require("../../main/dto/index");
const sys_user_entity_1 = require("./entities/sys-user.entity");
const user_width_post_entity_1 = require("./entities/user-width-post.entity");
const user_width_role_entity_1 = require("./entities/user-width-role.entity");
const post_entity_1 = require("../post/entities/post.entity");
const dept_entity_1 = require("../dept/entities/dept.entity");
const role_service_1 = require("../role/role.service");
const dept_service_1 = require("../dept/dept.service");
const config_service_1 = require("../config/config.service");
const redis_decorator_1 = require("../../../common/decorators/redis.decorator");
const captcha_decorator_1 = require("../../../common/decorators/captcha.decorator");
let UserService = class UserService {
    constructor(userRepo, sysDeptEntityRep, sysPostEntityRep, sysUserWithPostEntityRep, sysUserWithRoleEntityRep, roleService, deptService, jwtService, redisService, configService) {
        this.userRepo = userRepo;
        this.sysDeptEntityRep = sysDeptEntityRep;
        this.sysPostEntityRep = sysPostEntityRep;
        this.sysUserWithPostEntityRep = sysUserWithPostEntityRep;
        this.sysUserWithRoleEntityRep = sysUserWithRoleEntityRep;
        this.roleService = roleService;
        this.deptService = deptService;
        this.jwtService = jwtService;
        this.redisService = redisService;
        this.configService = configService;
    }
    async create(createUserDto) {
        const salt = bcrypt.genSaltSync(10);
        if (createUserDto.password) {
            createUserDto.password = await bcrypt.hashSync(createUserDto.password, salt);
        }
        const res = await this.userRepo.save({ ...createUserDto, userType: "10" });
        const postEntity = this.sysUserWithPostEntityRep.createQueryBuilder('postEntity');
        const postValues = createUserDto.postIds.map((id) => {
            return {
                userId: res.userId,
                postId: id,
            };
        });
        postEntity.insert().values(postValues).execute();
        const roleEntity = this.sysUserWithRoleEntityRep.createQueryBuilder('roleEntity');
        const roleValues = createUserDto.roleIds.map((id) => {
            return {
                userId: res.userId,
                roleId: id,
            };
        });
        roleEntity.insert().values(roleValues).execute();
        return result_1.ResultData.ok();
    }
    async findAll(query, user) {
        const entity = this.userRepo.createQueryBuilder('user');
        entity.where('user.delFlag = :delFlag', { delFlag: '0' });
        if (user) {
            const roles = user.roles;
            const deptIds = [];
            let dataScopeAll = false;
            let dataScopeSelf = false;
            for (let index = 0; index < roles.length; index++) {
                const role = roles[index];
                if (role.dataScope === index_2.DataScopeEnum.DATA_SCOPE_ALL) {
                    dataScopeAll = true;
                    break;
                }
                else if (role.dataScope === index_2.DataScopeEnum.DATA_SCOPE_CUSTOM) {
                    const roleWithDeptIds = await this.roleService.findRoleWithDeptIds(role.roleId);
                    deptIds.push(...roleWithDeptIds);
                }
                else if (role.dataScope === index_2.DataScopeEnum.DATA_SCOPE_DEPT || role.dataScope === index_2.DataScopeEnum.DATA_SCOPE_DEPT_AND_CHILD) {
                    const dataScopeWidthDeptIds = await this.deptService.findDeptIdsByDataScope(user.deptId, role.dataScope);
                    deptIds.push(...dataScopeWidthDeptIds);
                }
                else if (role.dataScope === index_2.DataScopeEnum.DATA_SCOPE_SELF) {
                    dataScopeSelf = true;
                }
            }
            if (!dataScopeAll) {
                if (deptIds.length > 0) {
                    entity.where('user.deptId IN (:...deptIds)', { deptIds: deptIds });
                }
                else if (dataScopeSelf) {
                    entity.where('user.userId = :userId', { userId: user.userId });
                }
            }
        }
        if (query.deptId) {
            const deptIds = await this.deptService.findDeptIdsByDataScope(+query.deptId, index_2.DataScopeEnum.DATA_SCOPE_DEPT_AND_CHILD);
            entity.andWhere('user.deptId IN (:...deptIds)', { deptIds: deptIds });
        }
        if (query.userName) {
            entity.andWhere(`user.userName LIKE "%${query.userName}%"`);
        }
        if (query.phonenumber) {
            entity.andWhere(`user.phonenumber LIKE "%${query.phonenumber}%"`);
        }
        if (query.status) {
            entity.andWhere('user.status = :status', { status: query.status });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('user.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        entity.leftJoinAndMapOne('user.dept', dept_entity_1.SysDeptEntity, 'dept', 'dept.deptId = user.deptId');
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findPostAndRoleAll() {
        const posts = await this.sysPostEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        const roles = await this.roleService.findRoles({
            where: {
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok({
            posts,
            roles,
        });
    }
    async findOne(userId) {
        const data = await this.userRepo.findOne({
            where: {
                delFlag: '0',
                userId: userId,
            },
        });
        const dept = await this.sysDeptEntityRep.findOne({
            where: {
                delFlag: '0',
                deptId: data.deptId,
            },
        });
        data['dept'] = dept;
        const postList = await this.sysUserWithPostEntityRep.find({
            where: {
                userId: userId,
            },
        });
        const postIds = postList.map((item) => item.postId);
        const allPosts = await this.sysPostEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        const roleIds = await this.getRoleIds([userId]);
        const allRoles = await this.roleService.findRoles({
            where: {
                delFlag: '0',
            },
        });
        data['roles'] = allRoles.filter((item) => roleIds.includes(item.roleId));
        return result_1.ResultData.ok({
            data,
            postIds,
            posts: allPosts,
            roles: allRoles,
            roleIds,
        });
    }
    async update(updateUserDto, userId) {
        if (updateUserDto.userId === 1)
            throw new common_1.BadRequestException('非法操作！');
        updateUserDto.roleIds = updateUserDto.roleIds.filter((v) => v != 1);
        if (updateUserDto.userId === userId) {
            delete updateUserDto.status;
        }
        if (updateUserDto?.postIds?.length > 0) {
            const hasPostId = await this.sysUserWithPostEntityRep.findOne({
                where: {
                    userId: updateUserDto.userId,
                },
                select: ['postId'],
            });
            if (hasPostId) {
                await this.sysUserWithPostEntityRep.delete({
                    userId: updateUserDto.userId,
                });
            }
            const postEntity = this.sysUserWithPostEntityRep.createQueryBuilder('postEntity');
            const postValues = updateUserDto.postIds.map((id) => {
                return {
                    userId: updateUserDto.userId,
                    postId: id,
                };
            });
            postEntity.insert().values(postValues).execute();
        }
        if (updateUserDto?.roleIds?.length > 0) {
            const hasRoletId = await this.sysUserWithRoleEntityRep.findOne({
                where: {
                    userId: updateUserDto.userId,
                },
                select: ['roleId'],
            });
            if (hasRoletId) {
                await this.sysUserWithRoleEntityRep.delete({
                    userId: updateUserDto.userId,
                });
            }
            const roleEntity = this.sysUserWithRoleEntityRep.createQueryBuilder('roleEntity');
            const roleValues = updateUserDto.roleIds.map((id) => {
                return {
                    userId: updateUserDto.userId,
                    roleId: id,
                };
            });
            roleEntity.insert().values(roleValues).execute();
        }
        delete updateUserDto.password;
        delete updateUserDto.dept;
        delete updateUserDto.roles;
        delete updateUserDto.roleIds;
        delete updateUserDto.postIds;
        const data = await this.userRepo.update({ userId: updateUserDto.userId }, updateUserDto);
        return result_1.ResultData.ok(data);
    }
    clearCacheByUserId(userId) {
        return userId;
    }
    async login(user, clientInfo) {
        const data = await this.userRepo.findOne({
            where: {
                userName: user.userName,
            },
            select: ['userId', 'password'],
        });
        this.clearCacheByUserId(data.userId);
        if (!(data && bcrypt.compareSync(user.password, data.password))) {
            return result_1.ResultData.fail(500, `帐号或密码错误`);
        }
        const userData = await this.getUserinfo(data.userId);
        if (userData.delFlag === index_2.DelFlagEnum.DELETE) {
            return result_1.ResultData.fail(500, `您已被禁用，如需正常使用请联系管理员`);
        }
        if (userData.status === index_2.StatusEnum.STOP) {
            return result_1.ResultData.fail(500, `您已被停用，如需正常使用请联系管理员`);
        }
        const loginDate = new Date();
        await this.userRepo.update({
            userId: data.userId,
        }, {
            loginDate: loginDate,
            loginIp: clientInfo.ipaddr,
        });
        const uuid = (0, index_1.GenerateUUID)();
        const token = this.createToken({ uuid: uuid, userId: userData.userId });
        const permissions = await this.getUserPermissions(userData.userId);
        const deptData = await this.sysDeptEntityRep.findOne({
            where: {
                deptId: userData.deptId,
            },
            select: ['deptName'],
        });
        userData['deptName'] = deptData.deptName || '';
        const roles = userData.roles.map((item) => item.roleKey);
        const userInfo = {
            browser: clientInfo.browser,
            ipaddr: clientInfo.ipaddr,
            loginLocation: clientInfo.loginLocation,
            loginTime: loginDate,
            os: clientInfo.os,
            permissions: permissions,
            roles: roles,
            token: uuid,
            user: userData,
            userId: userData.userId,
            userName: userData.userName,
            deptId: userData.deptId,
        };
        await this.updateRedisToken(uuid, userInfo);
        return result_1.ResultData.ok({
            token,
        }, '登录成功');
    }
    async updateRedisUserRolesAndPermissions(uuid, userId) {
        const userData = await this.getUserinfo(userId);
        const permissions = await this.getUserPermissions(userId);
        const roles = userData.roles.map((item) => item.roleKey);
        await this.updateRedisToken(uuid, {
            permissions: permissions,
            roles: roles,
        });
    }
    async updateRedisToken(token, metaData) {
        const oldMetaData = await this.redisService.get(`${index_2.CacheEnum.LOGIN_TOKEN_KEY}${token}`);
        let newMetaData = metaData;
        if (oldMetaData) {
            newMetaData = Object.assign(oldMetaData, metaData);
        }
        await this.redisService.set(`${index_2.CacheEnum.LOGIN_TOKEN_KEY}${token}`, newMetaData, index_3.LOGIN_TOKEN_EXPIRESIN);
    }
    async getRoleIds(userIds) {
        const roleList = await this.sysUserWithRoleEntityRep.find({
            where: {
                userId: (0, typeorm_1.In)(userIds),
            },
            select: ['roleId'],
        });
        const roleIds = roleList.map((item) => item.roleId);
        return (0, index_1.Uniq)(roleIds);
    }
    async getUserPermissions(userId) {
        const roleIds = await this.getRoleIds([userId]);
        const list = await this.roleService.getPermissionsByRoleIds(roleIds);
        const permissions = (0, index_1.Uniq)(list.map((item) => item.perms)).filter((item) => {
            return item;
        });
        return permissions;
    }
    async getUserinfo(userId) {
        const entity = this.userRepo.createQueryBuilder('user');
        entity.where({
            userId: userId,
            delFlag: index_2.DelFlagEnum.NORMAL,
        });
        entity.leftJoinAndMapOne('user.dept', dept_entity_1.SysDeptEntity, 'dept', 'dept.deptId = user.deptId');
        const roleIds = await this.getRoleIds([userId]);
        const roles = await this.roleService.findRoles({
            where: {
                delFlag: '0',
                roleId: (0, typeorm_1.In)(roleIds),
            },
        });
        const postIds = (await this.sysUserWithPostEntityRep.find({
            where: {
                userId: userId,
            },
            select: ['postId'],
        })).map((item) => item.postId);
        const posts = await this.sysPostEntityRep.find({
            where: {
                delFlag: '0',
                postId: (0, typeorm_1.In)(postIds),
            },
        });
        const data = await entity.getOne();
        const result = {
            ...data,
            roles,
            posts,
            dept: data.dept,
        };
        return result;
    }
    async register(user) {
        const loginDate = (0, index_1.GetNowDate)();
        const salt = bcrypt.genSaltSync(10);
        if (user.password) {
            user.password = await bcrypt.hashSync(user.password, salt);
        }
        const checkUserNameUnique = await this.userRepo.findOne({
            where: {
                userName: user.userName,
            },
            select: ['userName'],
        });
        if (checkUserNameUnique) {
            return result_1.ResultData.fail(500, `保存用户'${user.userName}'失败，注册账号已存在`);
        }
        user['userName'] = user.userName;
        user['nickName'] = user.userName;
        await this.userRepo.save({ ...user, loginDate, userType: "10" });
        return result_1.ResultData.ok();
    }
    createToken(payload) {
        const accessToken = this.jwtService.sign(payload);
        return accessToken;
    }
    parseToken(token) {
        try {
            if (!token)
                return null;
            const payload = this.jwtService.verify(token.replace('Bearer ', ''));
            return payload;
        }
        catch (error) {
            return null;
        }
    }
    async resetPwd(body) {
        if (body.userId === 1) {
            return result_1.ResultData.fail(500, '系统用户不能重置密码');
        }
        if (body.password) {
            body.password = await bcrypt.hashSync(body.password, bcrypt.genSaltSync(10));
        }
        await this.userRepo.update({
            userId: body.userId,
        }, {
            password: body.password,
        });
        return result_1.ResultData.ok();
    }
    async remove(ids) {
        const data = await this.userRepo.update({ userId: (0, typeorm_1.In)(ids), userType: (0, typeorm_1.Not)("00") }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async authRole(userId) {
        const allRoles = await this.roleService.findRoles({
            where: {
                delFlag: '0',
            },
        });
        const user = await this.userRepo.findOne({
            where: {
                delFlag: '0',
                userId: userId,
            },
        });
        const dept = await this.sysDeptEntityRep.findOne({
            where: {
                delFlag: '0',
                deptId: user.deptId,
            },
        });
        user['dept'] = dept;
        const roleIds = await this.getRoleIds([userId]);
        user['roles'] = allRoles.filter((item) => {
            if (roleIds.includes(item.roleId)) {
                item['flag'] = true;
                return true;
            }
            else {
                return true;
            }
        });
        return result_1.ResultData.ok({
            roles: allRoles,
            user,
        });
    }
    async updateAuthRole(query) {
        let roleIds = query.roleIds.split(',');
        roleIds = roleIds.filter((v) => v != 1);
        if (roleIds?.length > 0) {
            const hasRoletId = await this.sysUserWithRoleEntityRep.findOne({
                where: {
                    userId: query.userId,
                },
                select: ['roleId'],
            });
            if (hasRoletId) {
                await this.sysUserWithRoleEntityRep.delete({
                    userId: query.userId,
                });
            }
            const roleEntity = this.sysUserWithRoleEntityRep.createQueryBuilder('roleEntity');
            const roleValues = roleIds.map((id) => {
                return {
                    userId: query.userId,
                    roleId: id,
                };
            });
            roleEntity.insert().values(roleValues).execute();
        }
        return result_1.ResultData.ok();
    }
    async changeStatus(changeStatusDto) {
        const userData = await this.userRepo.findOne({
            where: {
                userId: changeStatusDto.userId,
            },
            select: ['userType'],
        });
        if (userData.userType === "00") {
            return result_1.ResultData.fail(500, '系统角色不可停用');
        }
        const res = await this.userRepo.update({ userId: changeStatusDto.userId }, {
            status: changeStatusDto.status,
        });
        return result_1.ResultData.ok(res);
    }
    async deptTree() {
        const tree = await this.deptService.deptTree();
        return result_1.ResultData.ok(tree);
    }
    async allocatedList(query) {
        const roleWidthRoleList = await this.sysUserWithRoleEntityRep.find({
            where: {
                roleId: +query.roleId,
            },
            select: ['userId'],
        });
        if (roleWidthRoleList.length === 0) {
            return result_1.ResultData.ok({
                list: [],
                total: 0,
            });
        }
        const userIds = roleWidthRoleList.map((item) => item.userId);
        const entity = this.userRepo.createQueryBuilder('user');
        entity.where('user.delFlag = :delFlag', { delFlag: '0' });
        entity.andWhere('user.status = :status', { status: '0' });
        entity.andWhere('user.userId IN (:...userIds)', { userIds: userIds });
        if (query.userName) {
            entity.andWhere(`user.userName LIKE "%${query.userName}%"`);
        }
        if (query.phonenumber) {
            entity.andWhere(`user.phonenumber LIKE "%${query.phonenumber}%"`);
        }
        entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        entity.leftJoinAndMapOne('user.dept', dept_entity_1.SysDeptEntity, 'dept', 'dept.deptId = user.deptId');
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async unallocatedList(query) {
        const roleWidthRoleList = await this.sysUserWithRoleEntityRep.find({
            where: {
                roleId: +query.roleId,
            },
            select: ['userId'],
        });
        const userIds = roleWidthRoleList.map((item) => item.userId);
        const entity = this.userRepo.createQueryBuilder('user');
        entity.where('user.delFlag = :delFlag', { delFlag: '0' });
        entity.andWhere('user.status = :status', { status: '0' });
        entity.andWhere({
            userId: (0, typeorm_1.Not)((0, typeorm_1.In)(userIds)),
        });
        if (query.userName) {
            entity.andWhere(`user.userName LIKE "%${query.userName}%"`);
        }
        if (query.phonenumber) {
            entity.andWhere(`user.phonenumber LIKE "%${query.phonenumber}%"`);
        }
        entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        entity.leftJoinAndMapOne('user.dept', dept_entity_1.SysDeptEntity, 'dept', 'dept.deptId = user.deptId');
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async authUserCancel(data) {
        await this.sysUserWithRoleEntityRep.delete({
            userId: data.userId,
            roleId: data.roleId,
        });
        return result_1.ResultData.ok();
    }
    async authUserCancelAll(data) {
        const userIds = data.userIds.split(',').map((id) => +id);
        await this.sysUserWithRoleEntityRep.delete({
            userId: (0, typeorm_1.In)(userIds),
            roleId: +data.roleId,
        });
        return result_1.ResultData.ok();
    }
    async authUserSelectAll(data) {
        const userIds = data.userIds.split(',');
        const entitys = userIds.map((userId) => {
            const sysDeptEntityEntity = new user_width_role_entity_1.SysUserWithRoleEntity();
            return Object.assign(sysDeptEntityEntity, {
                userId: userId,
                roleId: +data.roleId,
            });
        });
        await this.sysUserWithRoleEntityRep.save(entitys);
        return result_1.ResultData.ok();
    }
    async profile(user) {
        return result_1.ResultData.ok(user);
    }
    async updateProfile(user, updateProfileDto) {
        await this.userRepo.update({ userId: user.user.userId }, updateProfileDto);
        const userData = await this.redisService.get(`${index_2.CacheEnum.LOGIN_TOKEN_KEY}${user.token}`);
        userData.user = Object.assign(userData.user, updateProfileDto);
        await this.redisService.set(`${index_2.CacheEnum.LOGIN_TOKEN_KEY}${user.token}`, userData);
        return result_1.ResultData.ok();
    }
    async updatePwd(user, updatePwdDto) {
        if (updatePwdDto.oldPassword === updatePwdDto.newPassword) {
            return result_1.ResultData.fail(500, '新密码不能与旧密码相同');
        }
        if (bcrypt.compareSync(user.user.password, updatePwdDto.oldPassword)) {
            return result_1.ResultData.fail(500, '修改密码失败，旧密码错误');
        }
        const password = await bcrypt.hashSync(updatePwdDto.newPassword, bcrypt.genSaltSync(10));
        await this.userRepo.update({ userId: user.user.userId }, { password: password });
        return result_1.ResultData.ok();
    }
    async export(res, body, user) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAll(body, user);
        const options = {
            sheetName: '用户数据',
            data: list.data.list,
            header: [
                { title: '用户序号', dataIndex: 'userId' },
                { title: '登录名称', dataIndex: 'userName' },
                { title: '用户昵称', dataIndex: 'nickName' },
                { title: '用户邮箱', dataIndex: 'email' },
                { title: '手机号码', dataIndex: 'phonenumber' },
                { title: '用户性别', dataIndex: 'sex' },
                { title: '账号状态', dataIndex: 'status' },
                { title: '最后登录IP', dataIndex: 'loginIp' },
                { title: '最后登录时间', dataIndex: 'loginDate', width: 20 },
                { title: '部门', dataIndex: 'dept.deptName' },
                { title: '部门负责人', dataIndex: 'dept.leader' },
            ],
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.UserService = UserService;
__decorate([
    (0, redis_decorator_1.Cacheable)(index_2.CacheEnum.SYS_USER_KEY, '{userId}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserService.prototype, "findOne", null);
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_2.CacheEnum.SYS_USER_KEY, '{updateUserDto.userId}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_4.UpdateUserDto, Number]),
    __metadata("design:returntype", Promise)
], UserService.prototype, "update", null);
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_2.CacheEnum.SYS_USER_KEY, '{userId}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], UserService.prototype, "clearCacheByUserId", null);
__decorate([
    (0, captcha_decorator_1.Captcha)('user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_5.LoginDto, Object]),
    __metadata("design:returntype", Promise)
], UserService.prototype, "login", null);
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(sys_user_entity_1.UserEntity)),
    __param(1, (0, typeorm_2.InjectRepository)(dept_entity_1.SysDeptEntity)),
    __param(2, (0, typeorm_2.InjectRepository)(post_entity_1.SysPostEntity)),
    __param(3, (0, typeorm_2.InjectRepository)(user_width_post_entity_1.SysUserWithPostEntity)),
    __param(4, (0, typeorm_2.InjectRepository)(user_width_role_entity_1.SysUserWithRoleEntity)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        role_service_1.RoleService,
        dept_service_1.DeptService,
        jwt_1.JwtService,
        redis_service_1.RedisService,
        config_service_1.ConfigService])
], UserService);
//# sourceMappingURL=user.service.js.map