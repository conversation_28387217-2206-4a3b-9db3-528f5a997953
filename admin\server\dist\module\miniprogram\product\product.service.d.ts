import { Repository } from 'typeorm';
import { ProductEntity } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { CategoryEntity } from '../category/entities/category.entity';
import { SysUploadEntity } from '../../upload/entities/upload.entity';
import { ResultData } from '../../../common/utils/result';
import { ProductSpecEntity } from './entities/product-spec.entity';
import { CreateProductSpecDto, UpdateProductSpecDto } from './dto/product-spec.dto';
import { FootprintService } from '../footprint/footprint.service';
export declare class ProductService {
    private readonly productRepository;
    private readonly categoryRepository;
    private readonly uploadRepository;
    private readonly productSpecRepository;
    private readonly footprintService;
    private readonly logger;
    constructor(productRepository: Repository<ProductEntity>, categoryRepository: Repository<CategoryEntity>, uploadRepository: Repository<SysUploadEntity>, productSpecRepository: Repository<ProductSpecEntity>, footprintService: FootprintService);
    private processProductImages;
    create(createProductDto: CreateProductDto): Promise<ResultData>;
    findAll(queryDto: ProductQueryDto): Promise<ResultData>;
    findEnabled(queryDto: ProductQueryDto): Promise<ResultData>;
    findOne(id: number, userId?: number): Promise<ResultData>;
    private recordUserFootprint;
    update(id: number, updateProductDto: UpdateProductDto): Promise<ResultData>;
    remove(id: number): Promise<ResultData>;
    updateStatus(ids: number[], status: number): Promise<ResultData>;
    increaseSales(id: number, count: number): Promise<ResultData>;
    addSpec(productId: number, createSpecDto: CreateProductSpecDto): Promise<ResultData>;
    findSpecs(productId: number): Promise<ResultData>;
    updateSpec(specId: number, updateSpecDto: UpdateProductSpecDto): Promise<ResultData>;
    removeSpec(specId: number): Promise<ResultData>;
    setDefaultSpec(specId: number): Promise<ResultData>;
    private clearDefaultSpec;
}
