"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MenuService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const menu_entity_1 = require("./entities/menu.entity");
const role_width_menu_entity_1 = require("../role/entities/role-width-menu.entity");
const index_1 = require("../../../common/utils/index");
const user_service_1 = require("../user/user.service");
const utils_1 = require("./utils");
let MenuService = class MenuService {
    constructor(userService, sysMenuEntityRep, sysRoleWithMenuEntityRep) {
        this.userService = userService;
        this.sysMenuEntityRep = sysMenuEntityRep;
        this.sysRoleWithMenuEntityRep = sysRoleWithMenuEntityRep;
    }
    async create(createMenuDto) {
        const res = await this.sysMenuEntityRep.save(createMenuDto);
        return result_1.ResultData.ok(res);
    }
    async findAll(query) {
        const entity = this.sysMenuEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.menuName) {
            entity.andWhere(`entity.menuName LIKE "%${query.menuName}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        entity.orderBy('entity.orderNum', 'ASC');
        const res = await entity.getMany();
        return result_1.ResultData.ok(res);
    }
    async treeSelect() {
        const res = await this.sysMenuEntityRep.find({
            where: {
                delFlag: '0',
            },
            order: {
                orderNum: 'ASC',
            },
        });
        const tree = (0, index_1.ListToTree)(res, (m) => m.menuId, (m) => m.menuName);
        return result_1.ResultData.ok(tree);
    }
    async roleMenuTreeselect(roleId) {
        const res = await this.sysMenuEntityRep.find({
            where: {
                delFlag: '0',
            },
            order: {
                orderNum: 'ASC',
                parentId: 'ASC',
            },
        });
        const tree = (0, index_1.ListToTree)(res, (m) => m.menuId, (m) => m.menuName);
        const menuIds = await this.sysRoleWithMenuEntityRep.find({
            where: { roleId: roleId },
            select: ['menuId'],
        });
        const checkedKeys = menuIds.map((item) => {
            return item.menuId;
        });
        return result_1.ResultData.ok({
            menus: tree,
            checkedKeys: checkedKeys,
        });
    }
    async findOne(menuId) {
        const res = await this.sysMenuEntityRep.findOne({
            where: {
                delFlag: '0',
                menuId: menuId,
            },
        });
        return result_1.ResultData.ok(res);
    }
    async update(updateMenuDto) {
        const res = await this.sysMenuEntityRep.update({ menuId: updateMenuDto.menuId }, updateMenuDto);
        return result_1.ResultData.ok(res);
    }
    async remove(menuId) {
        const data = await this.sysMenuEntityRep.update({ menuId: menuId }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async findMany(where) {
        return await this.sysMenuEntityRep.find(where);
    }
    async getMenuListByUserId(userId) {
        let menuWidthRoleList = [];
        const roleIds = await this.userService.getRoleIds([userId]);
        if (roleIds.includes(1)) {
            menuWidthRoleList = await this.sysMenuEntityRep.find({
                where: {
                    delFlag: '0',
                    status: '0',
                },
                select: ['menuId'],
            });
        }
        else {
            menuWidthRoleList = await this.sysRoleWithMenuEntityRep.find({
                where: { roleId: (0, typeorm_2.In)(roleIds) },
                select: ['menuId'],
            });
        }
        const menuIds = (0, index_1.Uniq)(menuWidthRoleList.map((item) => item.menuId));
        const menuList = await this.sysMenuEntityRep.find({
            where: {
                delFlag: '0',
                status: '0',
                menuId: (0, typeorm_2.In)(menuIds),
            },
            order: {
                orderNum: 'ASC',
            },
        });
        const menuTree = (0, utils_1.buildMenus)(menuList);
        return menuTree;
    }
};
exports.MenuService = MenuService;
exports.MenuService = MenuService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => user_service_1.UserService))),
    __param(1, (0, typeorm_1.InjectRepository)(menu_entity_1.SysMenuEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(role_width_menu_entity_1.SysRoleWithMenuEntity)),
    __metadata("design:paramtypes", [user_service_1.UserService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], MenuService);
//# sourceMappingURL=menu.service.js.map