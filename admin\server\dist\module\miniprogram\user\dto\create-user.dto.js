"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatPhoneAuthDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class WechatPhoneAuthDto {
}
exports.WechatPhoneAuthDto = WechatPhoneAuthDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信获取手机号的动态令牌code', required: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WechatPhoneAuthDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '微信登录code（用于获取openid）', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WechatPhoneAuthDto.prototype, "loginCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WechatPhoneAuthDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WechatPhoneAuthDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户性别：0-未知，1-男，2-女', enum: ['0', '1', '2'], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['0', '1', '2']),
    __metadata("design:type", String)
], WechatPhoneAuthDto.prototype, "gender", void 0);
//# sourceMappingURL=create-user.dto.js.map