"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AxiosService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const iconv_lite_1 = __importDefault(require("iconv-lite"));
let AxiosService = class AxiosService {
    constructor(httpService) {
        this.httpService = httpService;
    }
    async getIpAddress(ip) {
        try {
            const IP_URL = 'https://whois.pconline.com.cn/ipJson.jsp';
            const response = await this.httpService.axiosRef(`${IP_URL}?ip=${ip}&json=true`, {
                responseType: 'arraybuffer',
                transformResponse: [
                    function (data) {
                        const str = iconv_lite_1.default.decode(data, 'gbk');
                        return JSON.parse(str);
                    },
                ],
            });
            return response.data.addr;
        }
        catch (error) {
            return '未知';
        }
    }
    async get(url, config) {
        try {
            const response = await this.httpService.axiosRef.get(url, config);
            return response.data;
        }
        catch (error) {
            throw error;
        }
    }
    async post(url, data, config) {
        try {
            const response = await this.httpService.axiosRef.post(url, data, config);
            return response.data;
        }
        catch (error) {
            throw error;
        }
    }
};
exports.AxiosService = AxiosService;
exports.AxiosService = AxiosService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], AxiosService);
//# sourceMappingURL=axios.service.js.map