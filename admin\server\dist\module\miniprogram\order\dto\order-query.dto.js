"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class OrderQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.OrderQueryDto = OrderQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    __metadata("design:type", Number)
], OrderQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    __metadata("design:type", Number)
], OrderQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单状态',
        example: '1',
        enum: ['1', '2', '3', '4', '5', '6', '7'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单状态必须是字符串' }),
    (0, class_validator_1.IsEnum)(['-1', '1', '2', '3', '4', '5', '6', '7'], {
        message: '订单状态只能是：1待支付 2待发货 3配送中 4已完成 5已取消 6团购失败已退款 7退款中',
    }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单类型',
        example: '1',
        enum: ['1', '2'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单类型必须是字符串' }),
    (0, class_validator_1.IsEnum)(['1', '2'], { message: '订单类型只能是：1普通订单 2团购订单' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '用户ID必须大于0' }),
    __metadata("design:type", Number)
], OrderQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID关键词', example: 'ORD', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单ID关键词必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人姓名', example: '张三', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '收货人姓名必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "receiverName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人电话', example: '13800138000', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '收货人电话必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "receiverPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号尾号(后4位)', example: '8000', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '手机号尾号必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "phoneLastFour", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', example: '2024-06-01', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '开始时间必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', example: '2024-06-30', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '结束时间必须是字符串' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配送方式',
        example: '1',
        enum: ['1', '2'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '配送方式必须是字符串' }),
    (0, class_validator_1.IsEnum)(['1', '2'], { message: '配送方式只能是：1配送 2自提' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "deliveryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '按预约配送时间排序（ASC/DESC）', example: 'DESC', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['ASC', 'DESC'], { message: '排序方式只能是ASC或DESC' }),
    __metadata("design:type", String)
], OrderQueryDto.prototype, "orderByDeliveryTime", void 0);
//# sourceMappingURL=order-query.dto.js.map