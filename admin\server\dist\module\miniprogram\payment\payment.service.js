"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var PaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const axios_1 = __importDefault(require("axios"));
const payment_entity_1 = require("./entities/payment.entity");
const order_entity_1 = require("../order/entities/order.entity");
const user_entity_1 = require("../user/entities/user.entity");
const order_service_1 = require("../order/order.service");
const result_1 = require("../../../common/utils/result");
let PaymentService = PaymentService_1 = class PaymentService {
    constructor(paymentRepository, orderRepository, userRepository, configService, orderService) {
        this.paymentRepository = paymentRepository;
        this.orderRepository = orderRepository;
        this.userRepository = userRepository;
        this.configService = configService;
        this.orderService = orderService;
        this.logger = new common_1.Logger(PaymentService_1.name);
        this.wechatConfig = this.configService.get('wechat');
        this.logger.log(`微信支付配置加载成功: appid=${this.wechatConfig.appid}`);
        this.certSerialNo = this.wechatConfig.pay.certSerialNo || '1234567890';
        this.logger.log(`微信支付证书序列号: ${this.certSerialNo}`);
    }
    async createPayment(paymentRequest, userId) {
        try {
            this.logger.log(`用户 ${userId} 创建支付订单，订单ID: ${paymentRequest.orderId}`);
            const order = await this.orderRepository.findOne({
                where: { orderId: paymentRequest.orderId, userId: userId },
            });
            if (!order) {
                this.logger.error(`订单不存在或不属于当前用户: ${paymentRequest.orderId}, 用户ID: ${userId}`);
                throw new common_1.HttpException('订单不存在或无权限操作', common_1.HttpStatus.BAD_REQUEST);
            }
            if (order.status !== '1') {
                this.logger.error(`订单状态不允许支付: ${order.orderId}, 状态: ${order.status}`);
                throw new common_1.HttpException('订单状态不允许支付', common_1.HttpStatus.BAD_REQUEST);
            }
            const paymentAmount = Number(order.finalAmount);
            this.logger.log(`从订单获取支付金额: ${paymentAmount}`);
            const user = await this.userRepository.findOne({
                where: { userId: userId },
            });
            if (!user || !user.openid) {
                this.logger.error(`用户不存在或未绑定微信openid: ${userId}`);
                throw new common_1.HttpException('用户未绑定微信，无法使用微信支付', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`获取用户openid成功: ${user.openid.substring(0, 5)}***`);
            const existingPayment = await this.paymentRepository.findOne({
                where: { orderId: paymentRequest.orderId, paymentStatus: '1' },
            });
            if (existingPayment) {
                this.logger.warn(`订单已存在待支付记录: ${paymentRequest.orderId}`);
                let paymentParams = null;
                if (paymentRequest.paymentMethod === '1') {
                    paymentParams = await this.generateWechatPayParams(existingPayment, user.openid);
                }
                else if (paymentRequest.paymentMethod === '2') {
                    await this.handleBalancePayment(existingPayment, user);
                    existingPayment.paymentStatus = '2';
                    existingPayment.paymentTime = new Date();
                }
                const result = {
                    paymentId: existingPayment.paymentId,
                    orderId: existingPayment.orderId,
                    paymentStatus: existingPayment.paymentStatus,
                    paymentAmount: Number(existingPayment.paymentAmount),
                    transactionId: existingPayment.transactionId,
                    paymentTime: existingPayment.paymentTime,
                    paymentParams: paymentParams,
                };
                return result_1.ResultData.ok(result, '支付订单已存在');
            }
            const payment = new payment_entity_1.PaymentEntity();
            payment.orderId = paymentRequest.orderId;
            payment.userId = userId;
            payment.paymentMethod = paymentRequest.paymentMethod;
            payment.paymentAmount = paymentAmount;
            payment.paymentStatus = '1';
            payment.createBy = userId.toString();
            const savedPayment = await this.paymentRepository.save(payment);
            this.logger.log(`支付记录创建成功: ID=${savedPayment.paymentId}`);
            let paymentParams = null;
            if (paymentRequest.paymentMethod === '1') {
                paymentParams = await this.generateWechatPayParams(savedPayment, user.openid);
            }
            else if (paymentRequest.paymentMethod === '2') {
                await this.handleBalancePayment(savedPayment, user);
                savedPayment.paymentStatus = '2';
                savedPayment.paymentTime = new Date();
            }
            const result = {
                paymentId: savedPayment.paymentId,
                orderId: savedPayment.orderId,
                paymentStatus: savedPayment.paymentStatus,
                paymentAmount: Number(savedPayment.paymentAmount),
                paymentParams: paymentParams,
            };
            return result_1.ResultData.ok(result, '支付订单创建成功');
        }
        catch (error) {
            this.logger.error(`创建支付订单失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('创建支付订单失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createBalanceRecharge(rechargeDto, userId) {
        try {
            this.logger.log(`用户 ${userId} 创建余额充值订单，金额: ${rechargeDto.amount}`);
            const user = await this.userRepository.findOne({
                where: { userId: userId },
            });
            if (!user || !user.openid) {
                this.logger.error(`用户不存在或未绑定微信openid: ${userId}`);
                throw new common_1.HttpException('用户未绑定微信，无法使用微信支付', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`获取用户openid成功: ${user.openid.substring(0, 5)}***`);
            const rechargeId = `RECHARGE_${this.generateTimestamp()}_${this.generateRandomString(6)}`;
            this.logger.log(`生成充值单号: ${rechargeId}`);
            const payment = new payment_entity_1.PaymentEntity();
            payment.orderId = rechargeId;
            payment.userId = userId;
            payment.paymentMethod = '1';
            payment.paymentAmount = rechargeDto.amount;
            payment.paymentStatus = '1';
            payment.createBy = userId.toString();
            const savedPayment = await this.paymentRepository.save(payment);
            this.logger.log(`充值支付记录创建成功: ID=${savedPayment.paymentId}`);
            const paymentParams = await this.generateWechatPayParams(savedPayment, user.openid, '余额充值');
            const result = {
                paymentId: savedPayment.paymentId,
                rechargeId: rechargeId,
                paymentStatus: savedPayment.paymentStatus,
                amount: Number(savedPayment.paymentAmount),
                paymentParams: paymentParams,
            };
            return result_1.ResultData.ok(result, '充值订单创建成功');
        }
        catch (error) {
            this.logger.error(`创建充值订单失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('创建充值订单失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async rechargeUserBalance(rechargeDto, operatorId) {
        try {
            this.logger.log(`为用户 ${rechargeDto.userId} 充值余额，金额: ${rechargeDto.amount}`);
            const user = await this.userRepository.findOne({
                where: { userId: rechargeDto.userId },
            });
            if (!user) {
                this.logger.error(`用户不存在: ${rechargeDto.userId}`);
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const beforeBalance = Number(user.balance) || 0;
            this.logger.log(`用户当前余额: ${beforeBalance}`);
            user.balance = beforeBalance + rechargeDto.amount;
            user.updateBy = operatorId.toString();
            user.updateTime = new Date();
            await this.userRepository.save(user);
            this.logger.log(`用户余额更新成功: ${user.userId}, 当前余额: ${user.balance}`);
            const result = {
                userId: user.userId,
                beforeBalance: beforeBalance,
                amount: rechargeDto.amount,
                afterBalance: Number(user.balance),
                rechargeTime: new Date(),
                operator: operatorId.toString(),
                remark: rechargeDto.remark,
            };
            return result_1.ResultData.ok(result, '用户余额充值成功');
        }
        catch (error) {
            this.logger.error(`用户余额充值失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('用户余额充值失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateWechatPayParams(payment, openid, description) {
        try {
            this.logger.log(`生成微信支付参数: ${payment.paymentId}, 使用商户号: ${this.wechatConfig.pay.mchId}`);
            this.logger.log(`用户openid: ${openid.substring(0, 5)}***`);
            const timestamp = Math.floor(Date.now() / 1000).toString();
            const nonceStr = this.generateNonceStr();
            const notifyUrl = this.wechatConfig.pay.notifyUrl.replace('{orderId}', payment.orderId);
            const prepayParams = {
                appid: this.wechatConfig.appid,
                mchid: this.wechatConfig.pay.mchId,
                description: description || `初鲜果味订单-${payment.orderId}`,
                out_trade_no: payment.orderId,
                notify_url: notifyUrl,
                amount: {
                    total: Math.floor(Number(payment.paymentAmount) * 100),
                    currency: 'CNY',
                },
                payer: {
                    openid: openid,
                },
            };
            this.logger.log(`预支付订单参数: ${JSON.stringify(prepayParams)}`);
            const rootDir = process.cwd();
            const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
            const privateKeyPath = path.join(rootDir, relativePath);
            this.logger.log(`私钥路径: ${privateKeyPath}`);
            if (!fs.existsSync(privateKeyPath)) {
                this.logger.error(`商户私钥文件不存在: ${privateKeyPath}`);
                throw new Error('商户私钥文件不存在，请配置正确的私钥路径');
            }
            const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
            this.logger.log(`私钥读取成功，长度: ${privateKey.length}`);
            const method = 'POST';
            const url = '/v3/pay/transactions/jsapi';
            const requestTimestamp = Math.floor(Date.now() / 1000).toString();
            const requestNonce = this.generateNonceStr();
            const requestBody = JSON.stringify(prepayParams);
            const message = `${method}\n${url}\n${requestTimestamp}\n${requestNonce}\n${requestBody}\n`;
            this.logger.log(`签名原文: ${message}`);
            try {
                const sign = crypto.createSign('RSA-SHA256');
                sign.update(message);
                const signature = sign.sign(privateKey, 'base64');
                this.logger.log(`签名生成成功，签名值: ${signature.substring(0, 10)}***`);
                const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.wechatConfig.pay.mchId}",nonce_str="${requestNonce}",timestamp="${requestTimestamp}",serial_no="${this.certSerialNo}",signature="${signature}"`;
                this.logger.log(`认证头: ${authHeader}`);
                this.logger.log(`开始调用微信支付API...`);
                const response = await axios_1.default.post('https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi', prepayParams, {
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: 'application/json',
                        Authorization: authHeader,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
                    },
                });
                this.logger.log(`微信支付API响应: ${JSON.stringify(response.data)}`);
                const prepayId = response.data.prepay_id;
                if (!prepayId) {
                    throw new Error('获取预支付订单ID失败');
                }
                const signContent = `${this.wechatConfig.appid}\n${timestamp}\n${nonceStr}\nprepay_id=${prepayId}\n`;
                const paySign = this.generatePaySign(signContent);
                const payParams = {
                    appId: this.wechatConfig.appid,
                    timeStamp: timestamp,
                    nonceStr: nonceStr,
                    package: `prepay_id=${prepayId}`,
                    signType: 'RSA',
                    paySign: paySign,
                };
                this.logger.log(`生成支付参数成功: ${JSON.stringify(payParams)}`);
                return payParams;
            }
            catch (signError) {
                this.logger.error(`签名生成或API调用失败: ${signError.message}`);
                if (signError.response) {
                    this.logger.error(`API错误详情: ${JSON.stringify(signError.response.data || {})}`);
                }
                throw signError;
            }
        }
        catch (error) {
            this.logger.error(`生成微信支付参数失败: ${error.message}`);
            if (error.response) {
                this.logger.error(`微信支付API错误详情: ${JSON.stringify(error.response.data || {})}`);
                throw new common_1.HttpException(`微信支付API错误: ${error.response.data?.message || error.message}`, common_1.HttpStatus.BAD_REQUEST);
            }
            throw new common_1.HttpException(`生成支付参数失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    generateNonceStr(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    generatePaySign(signContent) {
        try {
            const rootDir = process.cwd();
            const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
            const privateKeyPath = path.join(rootDir, relativePath);
            this.logger.log(`读取商户私钥: ${privateKeyPath}`);
            if (!fs.existsSync(privateKeyPath)) {
                this.logger.error(`商户私钥文件不存在: ${privateKeyPath}`);
                return '私钥文件不存在，请配置正确的私钥路径';
            }
            const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
            const sign = crypto.createSign('RSA-SHA256');
            sign.update(signContent);
            const signature = sign.sign(privateKey, 'base64');
            this.logger.log(`生成签名成功，签名内容长度: ${signContent.length}`);
            return signature;
        }
        catch (error) {
            this.logger.error(`生成签名失败: ${error.message}`);
            return '签名生成失败';
        }
    }
    async handlePaymentNotify(orderId, notifyData) {
        try {
            this.logger.log(`接收到支付回调，订单ID: ${orderId}, 通知类型: ${notifyData.event_type}`);
            this.logger.log(`回调数据: ${JSON.stringify(notifyData)}`);
            if (notifyData.event_type !== 'TRANSACTION.SUCCESS') {
                this.logger.warn(`非支付成功通知，忽略处理: ${notifyData.event_type}`);
                return { code: 'SUCCESS', message: '非支付成功通知' };
            }
            if (!notifyData.resource || !notifyData.resource.ciphertext) {
                this.logger.error('回调数据缺少加密信息');
                throw new common_1.HttpException('回调数据格式错误', common_1.HttpStatus.BAD_REQUEST);
            }
            const decryptedData = await this.decryptNotifyData(notifyData.resource);
            this.logger.log(`解密后的回调数据: ${JSON.stringify(decryptedData)}`);
            const payment = await this.paymentRepository.findOne({
                where: { orderId: orderId },
            });
            if (!payment) {
                this.logger.error(`支付记录不存在: ${orderId}`);
                throw new common_1.HttpException('支付记录不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (payment.paymentStatus === '2') {
                this.logger.warn(`支付记录已处理，重复通知，订单ID: ${orderId}`);
                return { code: 'SUCCESS', message: '支付已成功，重复通知' };
            }
            const notifyAmount = decryptedData.amount.total;
            const paymentAmount = Math.floor(Number(payment.paymentAmount) * 100);
            if (notifyAmount !== paymentAmount) {
                this.logger.error(`支付金额不匹配: 通知金额=${notifyAmount}, 订单金额=${paymentAmount}`);
                throw new common_1.HttpException('支付金额不匹配', common_1.HttpStatus.BAD_REQUEST);
            }
            payment.paymentStatus = '2';
            payment.transactionId = decryptedData.transaction_id;
            payment.paymentTime = new Date(decryptedData.success_time);
            payment.callbackData = JSON.stringify(decryptedData);
            payment.updateBy = 'system';
            payment.updateTime = new Date();
            await this.paymentRepository.save(payment);
            this.logger.log(`支付记录更新成功: ${payment.paymentId}, 状态: ${payment.paymentStatus}`);
            if (orderId.startsWith('RECHARGE_')) {
                await this.updateUserBalance(payment.userId, Number(payment.paymentAmount));
            }
            else {
                await this.updateOrderStatus(orderId, '2');
            }
            return { code: 'SUCCESS', message: '成功' };
        }
        catch (error) {
            this.logger.error(`处理支付回调失败: ${error.message}`);
            return { code: 'FAIL', message: error.message || '处理失败' };
        }
    }
    async decryptNotifyData(resource) {
        try {
            const { algorithm, ciphertext, associated_data, nonce } = resource;
            if (!algorithm || !ciphertext || !nonce) {
                throw new Error('缺少必要的解密参数');
            }
            this.logger.log(`开始解密回调数据: ${algorithm}`);
            this.logger.log(`解密参数: nonce=${nonce}, associated_data=${associated_data || 'undefined'}`);
            const apiKey = this.wechatConfig.pay.apiKey;
            if (!apiKey) {
                throw new Error('未配置 apiKey');
            }
            const key = Buffer.from(apiKey, 'utf8');
            this.logger.log(`加载的 apiKey 长度: ${key.length} 字节`);
            if (key.length !== 32) {
                throw new Error('无效的ApiKey，长度必须为32个字节');
            }
            const decipher = crypto.createDecipheriv('aes-256-gcm', key, Buffer.from(nonce, 'utf8'));
            this.logger.log(`解密器创建成功`);
            if (associated_data) {
                decipher.setAAD(Buffer.from(associated_data));
                this.logger.log(`已设置 AAD`);
            }
            const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
            this.logger.log(`密文长度: ${ciphertextBuffer.length} 字节`);
            const authTag = ciphertextBuffer.slice(-16);
            const encryptedData = ciphertextBuffer.slice(0, -16);
            this.logger.log(`已提取 authTag，长度: ${authTag.length} 字节`);
            decipher.setAuthTag(authTag);
            this.logger.log(`已设置 authTag`);
            let decrypted = decipher.update(encryptedData);
            decrypted = Buffer.concat([decrypted, decipher.final()]);
            this.logger.log(`解密成功，解密后数据长度: ${decrypted.length} 字节`);
            const result = JSON.parse(decrypted.toString('utf8'));
            this.logger.log(`解析 JSON 成功: ${JSON.stringify(result)}`);
            return result;
        }
        catch (error) {
            this.logger.error(`解密回调数据失败: ${error.message}`);
            if (error.message.includes('Unsupported state or unable to authenticate data')) {
                this.logger.error('可能是密钥不正确或数据被篡改');
            }
            throw new common_1.HttpException('解密回调数据失败', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateUserBalance(userId, amount) {
        try {
            this.logger.log(`更新用户余额: 用户ID=${userId}, 金额=${amount}`);
            const user = await this.userRepository.findOne({
                where: { userId: userId },
            });
            if (!user) {
                this.logger.error(`用户不存在: ${userId}`);
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const currentBalance = Number(user.balance) || 0;
            user.balance = currentBalance + amount;
            user.updateBy = 'system';
            user.updateTime = new Date();
            await this.userRepository.save(user);
            this.logger.log(`用户余额更新成功: ${userId}, 当前余额: ${user.balance}`);
        }
        catch (error) {
            this.logger.error(`更新用户余额失败: ${error.message}`);
            throw error;
        }
    }
    async updateOrderStatus(orderId, status) {
        try {
            this.logger.log(`更新订单状态: 订单ID=${orderId}, 状态=${status}`);
            const order = await this.orderRepository.findOne({
                where: { orderId: orderId },
            });
            if (!order) {
                this.logger.error(`订单不存在: ${orderId}`);
                throw new common_1.HttpException('订单不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const updateOrderDto = {
                userId: order.userId,
                orderId: orderId,
                status: status,
            };
            await this.orderService.updateOrderStatus(orderId, updateOrderDto, 0);
            this.logger.log(`订单状态更新成功: ${orderId}, 状态: ${status}`);
        }
        catch (error) {
            this.logger.error(`更新订单状态失败: ${error.message}`);
            throw error;
        }
    }
    async handleBalancePayment(payment, user) {
        try {
            this.logger.log(`处理余额支付: 支付ID=${payment.paymentId}, 用户ID=${user.userId}`);
            const paymentAmount = Number(payment.paymentAmount);
            const currentBalance = Number(user.balance) || 0;
            if (currentBalance < paymentAmount) {
                this.logger.error(`用户余额不足: 用户ID=${user.userId}, 当前余额=${currentBalance}, 支付金额=${paymentAmount}`);
                throw new common_1.HttpException('用户余额不足', common_1.HttpStatus.BAD_REQUEST);
            }
            user.balance = currentBalance - paymentAmount;
            user.updateBy = user.userId.toString();
            user.updateTime = new Date();
            await this.userRepository.save(user);
            this.logger.log(`用户余额扣除成功: 用户ID=${user.userId}, 剩余余额=${user.balance}`);
            payment.paymentStatus = '2';
            payment.transactionId = `BALANCE_${this.generateTimestamp()}_${this.generateRandomString(10)}`;
            payment.paymentTime = new Date();
            payment.updateBy = user.userId.toString();
            payment.updateTime = new Date();
            await this.paymentRepository.save(payment);
            this.logger.log(`支付记录更新成功: ${payment.paymentId}, 状态: ${payment.paymentStatus}`);
            await this.updateOrderStatus(payment.orderId, '2');
            this.logger.log(`订单状态更新成功: ${payment.orderId}, 状态: 已支付`);
        }
        catch (error) {
            this.logger.error(`处理余额支付失败: ${error.message}`);
            throw error;
        }
    }
    async getPaymentStatus(orderId, userId) {
        try {
            this.logger.log(`查询支付状态: 订单=${orderId}, 用户=${userId}`);
            const payment = await this.paymentRepository.findOne({
                where: { orderId: orderId, userId: userId },
                order: { createTime: 'DESC' },
            });
            if (!payment) {
                throw new common_1.HttpException('支付记录不存在', common_1.HttpStatus.NOT_FOUND);
            }
            const result = {
                paymentId: payment.paymentId,
                orderId: payment.orderId,
                paymentStatus: payment.paymentStatus,
                paymentAmount: Number(payment.paymentAmount),
                transactionId: payment.transactionId,
                paymentTime: payment.paymentTime,
            };
            return result_1.ResultData.ok(result, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询支付状态失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('查询失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserPaymentList(userId, pageNum = 1, pageSize = 10) {
        try {
            this.logger.log(`获取用户支付记录: 用户=${userId}, 页码=${pageNum}`);
            const [payments, total] = await this.paymentRepository.findAndCount({
                where: { userId: userId },
                order: { createTime: 'DESC' },
                skip: (pageNum - 1) * pageSize,
                take: pageSize,
            });
            const paymentList = payments.map((payment) => ({
                paymentId: payment.paymentId,
                orderId: payment.orderId,
                userId: payment.userId,
                paymentMethod: payment.paymentMethod,
                paymentAmount: Number(payment.paymentAmount),
                paymentStatus: payment.paymentStatus,
                transactionId: payment.transactionId,
                paymentTime: payment.paymentTime,
                refundAmount: Number(payment.refundAmount),
                createTime: payment.createTime,
            }));
            return result_1.ResultData.ok({
                list: paymentList,
                total: total,
                pageNum: pageNum,
                pageSize: pageSize,
            }, '查询成功');
        }
        catch (error) {
            this.logger.error(`获取用户支付记录失败: ${error.message}`);
            throw new common_1.HttpException('查询失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async requestRefund(refundRequest, userId) {
        try {
            this.logger.log(`申请退款: 支付ID=${refundRequest.paymentId}, 用户=${userId}`);
            const payment = await this.paymentRepository.findOne({
                where: { paymentId: refundRequest.paymentId, userId: userId },
            });
            if (!payment) {
                throw new common_1.HttpException('支付记录不存在', common_1.HttpStatus.NOT_FOUND);
            }
            if (payment.paymentStatus !== '2') {
                throw new common_1.HttpException('只有支付成功的订单才能退款', common_1.HttpStatus.BAD_REQUEST);
            }
            const maxRefundAmount = Number(payment.paymentAmount) - Number(payment.refundAmount);
            if (refundRequest.refundAmount > maxRefundAmount) {
                throw new common_1.HttpException(`退款金额不能超过${maxRefundAmount}元`, common_1.HttpStatus.BAD_REQUEST);
            }
            const isBalanceTransaction = payment.transactionId && payment.transactionId.startsWith('BALANCE_');
            if (payment.paymentMethod === '1' && !isBalanceTransaction) {
                await this.processWechatRefund(payment, refundRequest.refundAmount, refundRequest.refundReason);
            }
            else if (payment.paymentMethod === '2' || isBalanceTransaction) {
                if (isBalanceTransaction && payment.paymentMethod === '1') {
                    this.logger.warn(`检测到数据不一致: PaymentMethod=1但TransactionId=${payment.transactionId}，按余额退款处理`);
                }
                await this.processBalanceRefund(payment, refundRequest.refundAmount);
            }
            else if (payment.paymentMethod === '3') {
                this.logger.log(`模拟支付退款: PaymentId=${payment.paymentId}, Amount=${refundRequest.refundAmount}`);
            }
            else {
                throw new common_1.HttpException('不支持的支付方式退款', common_1.HttpStatus.BAD_REQUEST);
            }
            const newRefundAmount = Number(payment.refundAmount) + refundRequest.refundAmount;
            await this.paymentRepository.update(refundRequest.paymentId, {
                refundAmount: newRefundAmount,
                refundTime: new Date(),
                paymentStatus: newRefundAmount >= Number(payment.paymentAmount) ? '4' : '2',
                updateBy: userId.toString(),
            });
            if (newRefundAmount >= Number(payment.paymentAmount)) {
                await this.orderRepository.update(payment.orderId, {
                    status: '5',
                    cancelReason: refundRequest.refundReason,
                    updateBy: userId.toString(),
                });
                this.logger.log(`全额退款，订单状态更新为已取消: ${payment.orderId}`);
            }
            const result = {
                paymentId: refundRequest.paymentId,
                refundAmount: refundRequest.refundAmount,
                refundTime: new Date(),
                refundStatus: 'success',
            };
            return result_1.ResultData.ok(result, '退款申请成功');
        }
        catch (error) {
            this.logger.error(`申请退款失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('退款申请失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserBalance(userId) {
        try {
            this.logger.log(`查询用户余额: ${userId}`);
            const user = await this.userRepository.findOne({
                where: { userId: userId },
            });
            if (!user) {
                this.logger.error(`用户不存在: ${userId}`);
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return result_1.ResultData.ok({ balance: Number(user.balance) || 0 }, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询用户余额失败: ${error.message}`);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('查询用户余额失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserBalanceChangeList(userId, pageNum = 1, pageSize = 10) {
        try {
            this.logger.log(`获取用户余额变动记录: 用户=${userId}, 页码=${pageNum}, 页大小=${pageSize}`);
            const queryBuilder = this.paymentRepository
                .createQueryBuilder('payment')
                .where('payment.userId = :userId', { userId })
                .andWhere('payment.paymentStatus = :paymentStatus', { paymentStatus: '2' })
                .andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('payment.orderId LIKE :rechargePrefix', { rechargePrefix: 'RECHARGE_%' })
                    .orWhere('payment.paymentMethod = :paymentMethod', { paymentMethod: '2' });
            }))
                .orderBy('payment.createTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [payments, total] = await queryBuilder.getManyAndCount();
            const balanceChangeList = await Promise.all(payments.map(async (payment) => {
                const isRecharge = payment.orderId.startsWith('RECHARGE_');
                let orderInfo;
                if (!isRecharge && payment.paymentMethod === '2') {
                    const order = await this.orderRepository.findOne({
                        where: { orderId: payment.orderId },
                        relations: ['orderItems'],
                    });
                    if (order) {
                        orderInfo = {
                            orderId: order.orderId,
                            items: order.orderItems
                                ? order.orderItems.map((item) => ({
                                    productId: item.productId,
                                    productName: item.productName,
                                    productImage: item.productImage,
                                    quantity: item.quantity,
                                    price: Number(item.price),
                                    totalPrice: Number(item.totalPrice),
                                }))
                                : [],
                        };
                    }
                }
                return {
                    id: payment.paymentId.toString(),
                    userId: payment.userId,
                    type: isRecharge ? 'recharge' : 'consume',
                    amount: isRecharge ? Number(payment.paymentAmount) : -Number(payment.paymentAmount),
                    description: isRecharge ? '余额充值' : `订单消费: ${payment.orderId}`,
                    time: payment.createTime,
                    status: payment.paymentStatus,
                    orderInfo: orderInfo,
                };
            }));
            return result_1.ResultData.ok({
                list: balanceChangeList,
                total: total,
                pageNum: pageNum,
                pageSize: pageSize,
                totalPages: Math.ceil(total / pageSize),
            }, '查询成功');
        }
        catch (error) {
            this.logger.error(`获取用户余额变动记录失败: ${error.message}`);
            throw new common_1.HttpException('查询失败', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    generateTimestamp() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    async processWechatRefund(payment, refundAmount, refundReason) {
        try {
            this.logger.log(`开始处理微信支付退款: OrderId=${payment.orderId}, RefundAmount=${refundAmount}`);
            const refundId = `REFUND_${this.generateTimestamp()}_${this.generateRandomString(6)}`;
            const refundParams = {
                out_trade_no: payment.orderId,
                out_refund_no: refundId,
                reason: refundReason || '订单退款',
                amount: {
                    refund: Math.floor(refundAmount * 100),
                    total: Math.floor(Number(payment.paymentAmount) * 100),
                    currency: 'CNY',
                },
            };
            const rootDir = process.cwd();
            const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
            const privateKeyPath = path.join(rootDir, relativePath);
            if (!fs.existsSync(privateKeyPath)) {
                throw new Error('商户私钥文件不存在，请配置正确的私钥路径');
            }
            const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
            const method = 'POST';
            const url = '/v3/refund/domestic/refunds';
            const requestTimestamp = Math.floor(Date.now() / 1000).toString();
            const requestNonce = this.generateNonceStr();
            const requestBody = JSON.stringify(refundParams);
            const message = `${method}\n${url}\n${requestTimestamp}\n${requestNonce}\n${requestBody}\n`;
            const sign = crypto.createSign('RSA-SHA256');
            sign.update(message);
            const signature = sign.sign(privateKey, 'base64');
            const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.wechatConfig.pay.mchId}",nonce_str="${requestNonce}",timestamp="${requestTimestamp}",serial_no="${this.certSerialNo}",signature="${signature}"`;
            this.logger.log(`调用微信退款API: ${JSON.stringify(refundParams)}`);
            const response = await axios_1.default.post('https://api.mch.weixin.qq.com/v3/refund/domestic/refunds', refundParams, {
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                    Authorization: authHeader,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                },
            });
            this.logger.log(`微信退款API响应: ${JSON.stringify(response.data)}`);
            if (response.data.status === 'SUCCESS' || response.data.status === 'PROCESSING') {
                this.logger.log(`微信退款成功: RefundId=${refundId}, Status=${response.data.status}`);
            }
            else {
                throw new Error(`微信退款失败: ${response.data.status}`);
            }
        }
        catch (error) {
            this.logger.error(`微信退款处理失败: ${error.message}`, error.stack);
            if (error.response) {
                this.logger.error(`微信退款API错误: ${JSON.stringify(error.response.data)}`);
            }
            throw new common_1.HttpException(`微信退款失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async processBalanceRefund(payment, refundAmount) {
        try {
            this.logger.log(`开始处理余额退款: UserId=${payment.userId}, RefundAmount=${refundAmount}`);
            const user = await this.userRepository.findOne({
                where: { userId: payment.userId },
            });
            if (!user) {
                throw new Error('用户不存在');
            }
            const currentBalance = Number(user.balance) || 0;
            user.balance = currentBalance + refundAmount;
            user.updateBy = 'system';
            user.updateTime = new Date();
            await this.userRepository.save(user);
            this.logger.log(`余额退款成功: UserId=${payment.userId}, RefundAmount=${refundAmount}, NewBalance=${user.balance}`);
        }
        catch (error) {
            this.logger.error(`余额退款处理失败: ${error.message}`, error.stack);
            throw new common_1.HttpException(`余额退款失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = PaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_entity_1.PaymentEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(order_entity_1.OrderEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.MiniprogramUser)),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => order_service_1.OrderService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        config_1.ConfigService,
        order_service_1.OrderService])
], PaymentService);
//# sourceMappingURL=payment.service.js.map