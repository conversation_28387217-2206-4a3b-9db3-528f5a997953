import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryQueryDto } from './dto/category-query.dto';
export declare class CategoryController {
    private readonly categoryService;
    private readonly logger;
    constructor(categoryService: CategoryService);
    create(createCategoryDto: CreateCategoryDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(queryDto: CategoryQueryDto): Promise<import("../../../common/utils/result").ResultData>;
    findEnabled(): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: number): Promise<import("../../../common/utils/result").ResultData>;
    update(id: number, updateCategoryDto: UpdateCategoryDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(id: number): Promise<import("../../../common/utils/result").ResultData>;
    updateStatus(body: {
        ids: number[];
        status: number;
    }): Promise<import("../../../common/utils/result").ResultData>;
}
