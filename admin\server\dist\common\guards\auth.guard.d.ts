import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ExecutionContext } from '@nestjs/common';
import { UserService } from 'src/module/system/user/user.service';
declare const JwtAuthGuard_base: import("@nestjs/passport").Type<import("@nestjs/passport").IAuthGuard>;
export declare class JwtAuthGuard extends JwtAuthGuard_base {
    private readonly reflector;
    private readonly userService;
    private readonly config;
    private globalWhiteList;
    constructor(reflector: Reflector, userService: UserService, config: ConfigService);
    canActivate(ctx: ExecutionContext): Promise<boolean>;
    activate(ctx: ExecutionContext): Promise<boolean>;
    jumpActivate(ctx: ExecutionContext): Promise<boolean>;
    checkWhiteList(ctx: ExecutionContext): boolean;
}
export {};
