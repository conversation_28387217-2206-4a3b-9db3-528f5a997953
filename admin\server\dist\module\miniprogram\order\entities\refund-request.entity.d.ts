import { BaseEntity } from '../../../../common/entities/base';
import { OrderEntity } from './order.entity';
import { MiniprogramUser } from '../../user/entities/user.entity';
export declare class RefundRequestEntity extends BaseEntity {
    id: number;
    orderId: string;
    userId: number;
    refundReason: string;
    refundAmount: number;
    status: string;
    adminComment: string | null;
    requestTime: Date;
    processTime: Date | null;
    order?: OrderEntity;
    user?: MiniprogramUser;
}
