"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CategoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const category_entity_1 = require("./entities/category.entity");
const result_1 = require("../../../common/utils/result");
let CategoryService = CategoryService_1 = class CategoryService {
    constructor(categoryRepository) {
        this.categoryRepository = categoryRepository;
        this.logger = new common_1.Logger(CategoryService_1.name);
    }
    async create(createCategoryDto) {
        try {
            this.logger.log(`创建分类: ${JSON.stringify(createCategoryDto)}`);
            const category = this.categoryRepository.create(createCategoryDto);
            const savedCategory = await this.categoryRepository.save(category);
            this.logger.log(`分类创建成功: ID=${savedCategory.categoryId}`);
            return result_1.ResultData.ok(savedCategory, '分类创建成功');
        }
        catch (error) {
            this.logger.error(`创建分类失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '创建分类失败');
        }
    }
    async findAll(queryDto) {
        try {
            this.logger.log(`查询分类列表: ${JSON.stringify(queryDto)}`);
            const { name, status, page = 1, pageSize = 10 } = queryDto;
            const skip = (page - 1) * pageSize;
            const queryBuilder = this.categoryRepository.createQueryBuilder('category');
            if (name) {
                queryBuilder.andWhere('category.name LIKE :name', { name: `%${name}%` });
            }
            if (status !== undefined) {
                queryBuilder.andWhere('category.status = :status', { status });
            }
            queryBuilder.orderBy('category.sortOrder', 'ASC').addOrderBy('category.createTime', 'DESC');
            queryBuilder.skip(skip).take(pageSize);
            const [list, total] = await queryBuilder.getManyAndCount();
            this.logger.log(`分类列表查询成功: 共${total}条记录`);
            return result_1.ResultData.ok({
                list,
                total,
                page,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
            }, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询分类列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询分类列表失败');
        }
    }
    async findOne(id) {
        try {
            this.logger.log(`查询分类详情: ID=${id}`);
            const category = await this.categoryRepository.findOne({ where: { categoryId: id } });
            if (!category) {
                this.logger.warn(`分类不存在: ID=${id}`);
                return result_1.ResultData.fail(404, '分类不存在');
            }
            this.logger.log(`分类详情查询成功: ID=${id}`);
            return result_1.ResultData.ok(category, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询分类详情失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询分类详情失败');
        }
    }
    async findEnabled() {
        try {
            this.logger.log('查询启用的分类列表');
            const categories = await this.categoryRepository.find({
                where: { status: 1 },
                order: { sortOrder: 'ASC', createTime: 'DESC' },
            });
            this.logger.log(`启用分类查询成功: 共${categories.length}条记录`);
            return result_1.ResultData.ok(categories, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询启用分类失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询启用分类失败');
        }
    }
    async update(id, updateCategoryDto) {
        try {
            this.logger.log(`更新分类: ID=${id}, Data=${JSON.stringify(updateCategoryDto)}`);
            const category = await this.categoryRepository.findOne({ where: { categoryId: id } });
            if (!category) {
                return result_1.ResultData.fail(404, '分类不存在');
            }
            await this.categoryRepository.update(id, updateCategoryDto);
            const updatedCategory = await this.categoryRepository.findOne({ where: { categoryId: id } });
            this.logger.log(`分类更新成功: ID=${id}`);
            return result_1.ResultData.ok(updatedCategory, '更新成功');
        }
        catch (error) {
            this.logger.error(`更新分类失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '更新分类失败');
        }
    }
    async remove(id) {
        try {
            this.logger.log(`删除分类: ID=${id}`);
            const category = await this.categoryRepository.findOne({
                where: { categoryId: id },
                relations: ['products'],
            });
            if (!category) {
                return result_1.ResultData.fail(404, '分类不存在');
            }
            if (category.products && category.products.length > 0) {
                return result_1.ResultData.fail(400, '该分类下还有商品，无法删除');
            }
            await this.categoryRepository.delete(id);
            this.logger.log(`分类删除成功: ID=${id}`);
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除分类失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '删除分类失败');
        }
    }
    async updateStatus(ids, status) {
        try {
            this.logger.log(`批量更新分类状态: IDs=${JSON.stringify(ids)}, Status=${status}`);
            await this.categoryRepository.update(ids, { status });
            this.logger.log(`分类状态更新成功: 共更新${ids.length}条记录`);
            return result_1.ResultData.ok(null, '状态更新成功');
        }
        catch (error) {
            this.logger.error(`批量更新分类状态失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '批量更新分类状态失败');
        }
    }
};
exports.CategoryService = CategoryService;
exports.CategoryService = CategoryService = CategoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(category_entity_1.CategoryEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CategoryService);
//# sourceMappingURL=category.service.js.map