"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var UploadService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const result_1 = require("../../common/utils/result");
const upload_entity_1 = require("./entities/upload.entity");
const index_1 = require("../../common/utils/index");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const iconv_lite_1 = __importDefault(require("iconv-lite"));
const cos_nodejs_sdk_v5_1 = __importDefault(require("cos-nodejs-sdk-v5"));
const mime_types_1 = __importDefault(require("mime-types"));
const common_2 = require("@nestjs/common");
let UploadService = UploadService_1 = class UploadService {
    constructor(sysUploadEntityRep, config) {
        this.sysUploadEntityRep = sysUploadEntityRep;
        this.config = config;
        this.cos = new cos_nodejs_sdk_v5_1.default({
            SecretId: this.config.get('cos.secretId'),
            SecretKey: this.config.get('cos.secretKey'),
            FileParallelLimit: 3,
            ChunkParallelLimit: 8,
            ChunkSize: 1024 * 1024 * 8,
        });
        this.logger = new common_2.Logger(UploadService_1.name);
        this.thunkDir = 'thunk';
        this.isLocal = this.config.get('app.file.isLocal');
    }
    async singleFileUpload(file) {
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        if (fileSize > this.config.get('app.file.maxSize')) {
            return result_1.ResultData.fail(500, `文件大小不能超过${this.config.get('app.file.maxSize')}MB`);
        }
        let res;
        if (this.isLocal) {
            res = await this.saveFileLocal(file);
        }
        else {
            const targetDir = this.config.get('cos.location');
            res = await this.saveFileCos(targetDir, file);
        }
        const uploadId = (0, index_1.GenerateUUID)();
        await this.sysUploadEntityRep.save({ uploadId, ...res, ext: path_1.default.extname(res.newFileName), size: file.size });
        return res;
    }
    async getChunkUploadId() {
        const uploadId = (0, index_1.GenerateUUID)();
        return result_1.ResultData.ok({
            uploadId: uploadId,
        });
    }
    async chunkFileUpload(file, body) {
        const rootPath = process.cwd();
        const baseDirPath = path_1.default.join(rootPath, this.config.get('app.file.location'));
        const chunckDirPath = path_1.default.join(baseDirPath, this.thunkDir, body.uploadId);
        if (!fs_1.default.existsSync(chunckDirPath)) {
            this.mkdirsSync(chunckDirPath);
        }
        const chunckFilePath = path_1.default.join(chunckDirPath, `${body.uploadId}${body.fileName}@${body.index}`);
        if (fs_1.default.existsSync(chunckFilePath)) {
            return result_1.ResultData.ok();
        }
        else {
            fs_1.default.writeFileSync(chunckFilePath, file.buffer);
            return result_1.ResultData.ok();
        }
    }
    async checkChunkFile(body) {
        const rootPath = process.cwd();
        const baseDirPath = path_1.default.join(rootPath, this.config.get('app.file.location'));
        const chunckDirPath = path_1.default.join(baseDirPath, this.thunkDir, body.uploadId);
        const chunckFilePath = path_1.default.join(chunckDirPath, `${body.uploadId}${body.fileName}@${body.index}`);
        if (!fs_1.default.existsSync(chunckFilePath)) {
            return result_1.ResultData.fail(500, '文件不存在');
        }
        else {
            return result_1.ResultData.ok();
        }
    }
    mkdirsSync(dirname) {
        if (fs_1.default.existsSync(dirname)) {
            return true;
        }
        else {
            if (this.mkdirsSync(path_1.default.dirname(dirname))) {
                fs_1.default.mkdirSync(dirname);
                return true;
            }
        }
    }
    async chunkMergeFile(body) {
        const { uploadId, fileName } = body;
        const rootPath = process.cwd();
        const baseDirPath = path_1.default.join(rootPath, this.config.get('app.file.location'));
        const sourceFilesDir = path_1.default.join(baseDirPath, this.thunkDir, uploadId);
        if (!fs_1.default.existsSync(sourceFilesDir)) {
            return result_1.ResultData.fail(500, '文件不存在');
        }
        const newFileName = this.getNewFileName(fileName);
        const targetFile = path_1.default.join(baseDirPath, newFileName);
        await this.thunkStreamMerge(sourceFilesDir, targetFile);
        const relativeFilePath = targetFile.replace(baseDirPath, '');
        const domain = this.config.get('app.file.domain').replace(/\/$/, '');
        const formattedFileName = fileName.startsWith('/') ? fileName : '/' + fileName;
        const url = domain + formattedFileName;
        const key = path_1.default.join('test', relativeFilePath);
        const data = {
            fileName: key,
            newFileName: newFileName,
            url: url,
        };
        const stats = fs_1.default.statSync(targetFile);
        if (!this.isLocal) {
            this.uploadLargeFileCos(targetFile, key);
            const cosDomain = this.config.get('cos.domain').replace(/\/$/, '');
            const formattedKey = key.startsWith('/') ? key : '/' + key;
            data.url = cosDomain + formattedKey;
            this.logger.log(`COS大文件上传成功: domain=${cosDomain}, key=${key}, url=${data.url}`);
            await this.sysUploadEntityRep.save({ uploadId, ...data, ext: path_1.default.extname(data.newFileName), size: stats.size, status: '0' });
            return result_1.ResultData.ok(data);
        }
        await this.sysUploadEntityRep.save({ uploadId, ...data, ext: path_1.default.extname(data.newFileName), size: stats.size });
        return result_1.ResultData.ok(data);
    }
    async thunkStreamMerge(sourceFilesDir, targetFile) {
        const fileList = fs_1.default
            .readdirSync(sourceFilesDir)
            .filter((file) => fs_1.default.lstatSync(path_1.default.join(sourceFilesDir, file)).isFile())
            .sort((a, b) => parseInt(a.split('@')[1]) - parseInt(b.split('@')[1]))
            .map((name) => ({
            name,
            filePath: path_1.default.join(sourceFilesDir, name),
        }));
        const fileWriteStream = fs_1.default.createWriteStream(targetFile);
        let onResolve;
        const callbackPromise = new Promise((resolve) => {
            onResolve = resolve;
        });
        this.thunkStreamMergeProgress(fileList, fileWriteStream, sourceFilesDir, onResolve);
        return callbackPromise;
    }
    thunkStreamMergeProgress(fileList, fileWriteStream, sourceFilesDir, onResolve) {
        if (!fileList.length) {
            fs_1.default.rmdirSync(sourceFilesDir, { recursive: true });
            onResolve();
            return;
        }
        const { filePath: chunkFilePath } = fileList.shift();
        const currentReadStream = fs_1.default.createReadStream(chunkFilePath);
        currentReadStream.pipe(fileWriteStream, { end: false });
        currentReadStream.on('end', () => {
            this.thunkStreamMergeProgress(fileList, fileWriteStream, sourceFilesDir, onResolve);
        });
    }
    async saveFileLocal(file) {
        const rootPath = process.cwd();
        const baseDirPath = path_1.default.join(rootPath, this.config.get('app.file.location'));
        const originalname = iconv_lite_1.default.decode(Buffer.from(file.originalname, 'binary'), 'utf8');
        const ext = mime_types_1.default.extension(file.mimetype);
        const newFileName = this.getNewFileName(originalname) + '.' + ext;
        const targetFile = path_1.default.join(baseDirPath, newFileName);
        const sourceFilesDir = path_1.default.dirname(targetFile);
        const relativeFilePath = targetFile.replace(baseDirPath, '');
        if (!fs_1.default.existsSync(sourceFilesDir)) {
            this.mkdirsSync(sourceFilesDir);
        }
        fs_1.default.writeFileSync(targetFile, file.buffer);
        const fileName = path_1.default.join(this.config.get('app.file.serveRoot'), relativeFilePath).replace(/\\/g, '/');
        const domain = this.config.get('app.file.domain').replace(/\/$/, '');
        const formattedFileName = fileName.startsWith('/') ? fileName : '/' + fileName;
        const url = domain + formattedFileName;
        this.logger.log(`文件上传成功: domain=${domain}, fileName=${fileName}, url=${url}`);
        return {
            fileName: fileName,
            newFileName: newFileName,
            url: url,
        };
    }
    getNewFileName(originalname) {
        if (!originalname) {
            return originalname;
        }
        const newFileNameArr = originalname.split('.');
        newFileNameArr[newFileNameArr.length - 1] = `${newFileNameArr[newFileNameArr.length - 1]}_${new Date().getTime()}`;
        return newFileNameArr.join('.');
    }
    async saveFileCos(targetDir, file) {
        const originalname = iconv_lite_1.default.decode(Buffer.from(file.originalname, 'binary'), 'utf8');
        const newFileName = this.getNewFileName(originalname);
        const targetFile = path_1.default.join(targetDir, newFileName);
        await this.uploadCos(targetFile, file.buffer);
        const domain = this.config.get('cos.domain').replace(/\/$/, '');
        const formattedTargetFile = targetFile.startsWith('/') ? targetFile : '/' + targetFile;
        const url = domain + formattedTargetFile;
        this.logger.log(`COS文件上传成功: domain=${domain}, targetFile=${targetFile}, url=${url}`);
        return {
            fileName: targetFile,
            newFileName: newFileName,
            url: url,
        };
    }
    async uploadCos(targetFile, buffer) {
        const { statusCode } = await this.cosHeadObject(targetFile);
        if (statusCode !== 200) {
            const data = await this.cos.putObject({
                Bucket: this.config.get('cos.bucket'),
                Region: this.config.get('cos.region'),
                Key: targetFile,
                Body: buffer,
            });
            return path_1.default.dirname(data.Location);
        }
        return targetFile;
    }
    async getChunkUploadResult(uploadId) {
        const data = await this.sysUploadEntityRep.findOne({
            where: { uploadId },
            select: ['status', 'fileName', 'newFileName', 'url'],
        });
        if (data) {
            return result_1.ResultData.ok({
                data: data,
                msg: data.status === '0' ? '上传成功' : '上传中',
            });
        }
        else {
            return result_1.ResultData.fail(500, '文件不存在');
        }
    }
    async uploadLargeFileCos(sourceFile, targetFile) {
        const { statusCode } = await this.cosHeadObject(targetFile);
        if (statusCode !== 200) {
            await this.cos.uploadFile({
                Bucket: this.config.get('cos.bucket'),
                Region: this.config.get('cos.region'),
                Key: targetFile,
                FilePath: sourceFile,
                SliceSize: 1024 * 1024 * 5,
                onProgress: function (progressData) {
                    if (progressData.percent === 1) {
                        this.sysUploadEntityRep.update({ filName: targetFile }, { status: 0 });
                    }
                },
            });
        }
        fs_1.default.unlinkSync(sourceFile);
        return targetFile;
    }
    async cosHeadObject(targetFile) {
        try {
            return await this.cos.headObject({
                Bucket: this.config.get('cos.bucket'),
                Region: this.config.get('cos.region'),
                Key: targetFile,
            });
        }
        catch (error) {
            return error;
        }
    }
    async getAuthorization(Key) {
        const url = this.cos.getObjectUrl({
            Bucket: this.config.get('cos.bucket'),
            Region: this.config.get('cos.region'),
            Key: Key,
            Sign: true,
            Expires: 60,
        });
        return result_1.ResultData.ok({ url });
    }
    async deleteFile(fileUrl) {
        try {
            this.logger.log(`尝试删除文件: ${fileUrl}`);
            if (!fileUrl) {
                return false;
            }
            if (this.isLocal) {
                const rootPath = process.cwd();
                const baseDirPath = path_1.default.join(rootPath, this.config.get('app.file.location'));
                const domainUrl = this.config.get('app.file.domain').replace(/\/$/, '');
                const serveRoot = this.config.get('app.file.serveRoot');
                if (fileUrl.startsWith(domainUrl)) {
                    let filePath = '';
                    const formattedServeRoot = serveRoot.startsWith('/') ? serveRoot : '/' + serveRoot;
                    if (serveRoot && serveRoot !== '') {
                        filePath = fileUrl.replace(domainUrl + formattedServeRoot, '');
                    }
                    else {
                        filePath = fileUrl.replace(domainUrl, '');
                    }
                    filePath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
                    const fullPath = path_1.default.join(baseDirPath, filePath);
                    this.logger.log(`删除本地文件: domainUrl=${domainUrl}, serveRoot=${formattedServeRoot}, filePath=${filePath}, fullPath=${fullPath}`);
                    if (fs_1.default.existsSync(fullPath)) {
                        fs_1.default.unlinkSync(fullPath);
                        this.logger.log(`本地文件删除成功: ${fullPath}`);
                        return true;
                    }
                }
            }
            else {
                const domainUrl = this.config.get('cos.domain').replace(/\/$/, '');
                if (fileUrl.startsWith(domainUrl)) {
                    let Key = fileUrl.replace(domainUrl, '');
                    Key = Key.startsWith('/') ? Key.substring(1) : Key;
                    this.logger.log(`删除COS文件: domain=${domainUrl}, Key=${Key}`);
                    return new Promise((resolve) => {
                        this.cos.deleteObject({
                            Bucket: this.config.get('cos.bucket'),
                            Region: this.config.get('cos.region'),
                            Key: Key,
                        }, (err, data) => {
                            if (err) {
                                this.logger.error(`COS文件删除失败: ${err.message}`);
                                resolve(false);
                            }
                            else {
                                this.logger.log(`COS文件删除成功: ${Key}`);
                                resolve(true);
                            }
                        });
                    });
                }
            }
            return false;
        }
        catch (error) {
            this.logger.error(`删除文件失败: ${error.message}`);
            return false;
        }
    }
};
exports.UploadService = UploadService;
exports.UploadService = UploadService = UploadService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(upload_entity_1.SysUploadEntity)),
    __param(1, (0, common_1.Inject)(config_1.ConfigService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        config_1.ConfigService])
], UploadService);
//# sourceMappingURL=upload.service.js.map