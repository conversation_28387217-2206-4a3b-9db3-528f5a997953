"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnlineService = void 0;
const common_1 = require("@nestjs/common");
const result_1 = require("../../../common/utils/result");
const redis_service_1 = require("../../common/redis/redis.service");
const index_1 = require("../../../common/enum/index");
const index_2 = require("../../../common/utils/index");
let OnlineService = class OnlineService {
    constructor(redisService) {
        this.redisService = redisService;
    }
    async findAll(query) {
        const kes = await this.redisService.keys(`${index_1.CacheEnum.LOGIN_TOKEN_KEY}*`);
        const data = await this.redisService.mget(kes);
        const list = (0, index_2.Paginate)({
            list: data,
            pageSize: query.pageSize,
            pageNum: query.pageNum,
        }, query).map((item) => {
            return {
                tokenId: item.token,
                deptName: item.user.deptName,
                userName: item.userName,
                ipaddr: item.ipaddr,
                loginLocation: item.loginLocation,
                browser: item.browser,
                os: item.os,
                loginTime: item.loginTime,
            };
        });
        return result_1.ResultData.ok({
            list,
            total: data.length,
        });
    }
    async delete(token) {
        await this.redisService.del(`${index_1.CacheEnum.LOGIN_TOKEN_KEY}${token}`);
        return result_1.ResultData.ok();
    }
};
exports.OnlineService = OnlineService;
exports.OnlineService = OnlineService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], OnlineService);
//# sourceMappingURL=online.service.js.map