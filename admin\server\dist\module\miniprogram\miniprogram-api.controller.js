"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MiniprogramApiController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MiniprogramApiController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const delivery_settings_service_1 = require("./delivery-settings/delivery-settings.service");
const user_decorator_1 = require("../system/user/user.decorator");
let MiniprogramApiController = MiniprogramApiController_1 = class MiniprogramApiController {
    constructor(deliverySettingsService) {
        this.deliverySettingsService = deliverySettingsService;
        this.logger = new common_1.Logger(MiniprogramApiController_1.name);
    }
    async getDeliveryTimeSlots() {
        this.logger.log('小程序获取配送时间段接口开始');
        return await this.deliverySettingsService.getActiveTimeSlots();
    }
};
exports.MiniprogramApiController = MiniprogramApiController;
__decorate([
    (0, common_1.Get)('delivery-time-slots'),
    (0, swagger_1.ApiOperation)({
        summary: '获取可用的配送时间段',
        description: '获取小程序可用的配送时间段列表',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: [
                    {
                        label: '上午9:00-12:00',
                        value: '10:00:00',
                        disabled: false,
                    },
                    {
                        label: '下午13:00-18:00',
                        value: '15:00:00',
                        disabled: false,
                    },
                    {
                        label: '晚上18:00-21:00',
                        value: '19:30:00',
                        disabled: false,
                    },
                ],
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MiniprogramApiController.prototype, "getDeliveryTimeSlots", null);
exports.MiniprogramApiController = MiniprogramApiController = MiniprogramApiController_1 = __decorate([
    (0, swagger_1.ApiTags)('小程序公开接口'),
    (0, common_1.Controller)('api/miniprogram'),
    (0, user_decorator_1.NotRequireAuth)(),
    __metadata("design:paramtypes", [delivery_settings_service_1.DeliverySettingsService])
], MiniprogramApiController);
//# sourceMappingURL=miniprogram-api.controller.js.map