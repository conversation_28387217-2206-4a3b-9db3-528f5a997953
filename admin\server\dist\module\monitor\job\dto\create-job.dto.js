"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListJobLogDto = exports.ListJobDto = exports.CreateJobDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const index_1 = require("../../../../common/dto/index");
class CreateJobDto {
}
exports.CreateJobDto = CreateJobDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '任务名称不能为空' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 64),
    __metadata("design:type", String)
], CreateJobDto.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务组名' }),
    (0, class_validator_1.IsNotEmpty)({ message: '任务组名不能为空' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 64),
    __metadata("design:type", String)
], CreateJobDto.prototype, "jobGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '调用目标字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '调用目标字符串不能为空' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 500),
    __metadata("design:type", String)
], CreateJobDto.prototype, "invokeTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'cron执行表达式' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'cron表达式不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDto.prototype, "cronExpression", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '计划执行错误策略（1立即执行 2执行一次 3放弃执行）' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateJobDto.prototype, "misfirePolicy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否并发执行（0允许 1禁止）' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateJobDto.prototype, "concurrent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态（0正常 1暂停）' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateJobDto.prototype, "remark", void 0);
class ListJobDto {
}
exports.ListJobDto = ListJobDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListJobDto.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务组名' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 64),
    __metadata("design:type", String)
], ListJobDto.prototype, "jobGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态（0正常 1暂停）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListJobDto.prototype, "status", void 0);
class ListJobLogDto extends index_1.PagingDto {
}
exports.ListJobLogDto = ListJobLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListJobLogDto.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务组名' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 64),
    __metadata("design:type", String)
], ListJobLogDto.prototype, "jobGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态（0正常 1暂停）' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListJobLogDto.prototype, "status", void 0);
//# sourceMappingURL=create-job.dto.js.map