"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.commonExportMap = void 0;
exports.ExportTable = ExportTable;
const Lodash = __importStar(require("lodash"));
const ExcelJS = __importStar(require("exceljs"));
const index_1 = require("../enum/index");
exports.commonExportMap = {
    status: {
        [index_1.StatusEnum.NORMAL]: '正常',
        [index_1.StatusEnum.STOP]: '停用',
    },
    sex: {
        [index_1.SexEnum.MAN]: '男',
        [index_1.SexEnum.WOMAN]: '女',
    },
    delFlag: {
        [index_1.DelFlagEnum.NORMAL]: '正常',
        [index_1.DelFlagEnum.DELETE]: '已删除',
    },
};
async function ExportTable(options, res) {
    let data = options.data;
    const workbook = new ExcelJS.Workbook();
    const sheetName = options.sheetName || 'Sheet1';
    const worksheet = workbook.addWorksheet(sheetName);
    worksheet.columns = options.header.map((column) => {
        const width = column.width;
        return {
            header: column.title,
            key: column.dataIndex,
            width: isNaN(width) ? 16 : width,
        };
    });
    const dictMap = { ...exports.commonExportMap, ...options.dictMap };
    data = data.map((item) => {
        const newItem = {};
        options.header.forEach((field) => {
            const dataIndex = field.dataIndex;
            const dataValue = Lodash.get(item, dataIndex);
            if (dictMap && dictMap[dataIndex]) {
                newItem[dataIndex] = dictMap[dataIndex][dataValue] !== undefined ? dictMap[dataIndex][dataValue] : dataValue;
            }
            else {
                newItem[dataIndex] = dataValue;
            }
        });
        return newItem;
    });
    const headerStyle = {
        font: {
            size: 10,
            bold: true,
            color: { argb: 'ffffff' },
        },
        alignment: { vertical: 'middle', horizontal: 'center' },
        fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '808080' },
        },
        border: {
            top: { style: 'thin', color: { argb: '9e9e9e' } },
            left: { style: 'thin', color: { argb: '9e9e9e' } },
            bottom: { style: 'thin', color: { argb: '9e9e9e' } },
            right: { style: 'thin', color: { argb: '9e9e9e' } },
        },
    };
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
        cell.style = headerStyle;
    });
    data.forEach((item) => {
        worksheet.addRow(item);
    });
    worksheet.columns.forEach((column) => {
        column.alignment = { vertical: 'middle', horizontal: 'center' };
    });
    const buffer = await workbook.xlsx.writeBuffer();
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment;filename=sheet.xlsx');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.end(buffer, 'binary');
}
//# sourceMappingURL=export.js.map