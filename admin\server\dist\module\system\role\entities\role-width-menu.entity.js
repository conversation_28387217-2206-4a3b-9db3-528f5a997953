"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysRoleWithMenuEntity = void 0;
const typeorm_1 = require("typeorm");
let SysRoleWithMenuEntity = class SysRoleWithMenuEntity {
};
exports.SysRoleWithMenuEntity = SysRoleWithMenuEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'int', name: 'role_id', default: 0, comment: '角色ID' }),
    __metadata("design:type", Number)
], SysRoleWithMenuEntity.prototype, "roleId", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'int', name: 'menu_id', default: 0, comment: '菜单ID' }),
    __metadata("design:type", Number)
], SysRoleWithMenuEntity.prototype, "menuId", void 0);
exports.SysRoleWithMenuEntity = SysRoleWithMenuEntity = __decorate([
    (0, typeorm_1.Entity)('sys_role_menu', {
        comment: '角色和菜单关联表',
    })
], SysRoleWithMenuEntity);
//# sourceMappingURL=role-width-menu.entity.js.map