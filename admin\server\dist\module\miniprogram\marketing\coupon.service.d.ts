import { DataSource, Repository } from 'typeorm';
import { Coupon } from './entities/coupon.entity';
import { UserCoupon } from './entities/user-coupon.entity';
import { CreateCouponDto } from './dto/coupon/create-coupon.dto';
import { UpdateCouponDto } from './dto/coupon/update-coupon.dto';
import { QueryCouponDto } from './dto/coupon/query-coupon.dto';
import { ResultData } from 'src/common/utils/result';
import { DistributeCouponDto, QueryUserListDto } from './dto/coupon/distribute-coupon.dto';
import { MiniprogramUser } from '../user/entities/user.entity';
export declare class CouponService {
    private readonly couponRepository;
    private readonly userCouponRepository;
    private readonly userRepository;
    private readonly dataSource;
    private readonly logger;
    constructor(couponRepository: Repository<Coupon>, userCouponRepository: Repository<UserCoupon>, userRepository: Repository<MiniprogramUser>, dataSource: DataSource);
    create(createCouponDto: CreateCouponDto, adminUserId: number): Promise<Coupon>;
    update(updateCouponDto: UpdateCouponDto, adminUserId: number): Promise<Coupon>;
    findAll(queryParams: QueryCouponDto): Promise<ResultData>;
    findOne(id: number): Promise<Coupon>;
    remove(id: number, adminUserId: number): Promise<boolean>;
    findUserAvailableCoupons(userId: number, orderAmount: number): Promise<UserCoupon[]>;
    userReceiveCoupon(userId: number, couponId: number): Promise<ResultData>;
    useCoupon(userCouponId: number, orderId: string, userId: number): Promise<boolean>;
    getUserCoupons(userId: number, status?: string): Promise<UserCoupon[]>;
    processExpiredCoupons(): Promise<{
        totalProcessed: number;
        successCount: number;
    }>;
    updateCouponStatus(): Promise<{
        totalProcessed: number;
        updatedCount: number;
    }>;
    findAvailableToReceive(userId: number): Promise<any>;
    distributeCouponToUsers(distributeCouponDto: DistributeCouponDto, adminUserId: number): Promise<ResultData>;
    getUserList(queryParams: QueryUserListDto): Promise<ResultData>;
    getDistributeRecords(couponId: number, queryParams: QueryUserListDto): Promise<ResultData>;
    private getStatusText;
}
