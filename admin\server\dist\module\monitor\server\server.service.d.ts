import { ResultData } from 'src/common/utils/result';
export declare class ServerService {
    getInfo(): Promise<ResultData>;
    getDiskStatus(): Promise<{
        dirName: any;
        typeName: any;
        total: string;
        used: string;
        free: string;
        usage: string;
    }[]>;
    getServerIP(): string;
    getCpuInfo(): {
        cpuNum: any;
        total: any;
        sys: string;
        used: string;
        wait: number;
        free: string;
    };
    getMemInfo(): {
        total: string;
        used: string;
        free: string;
        usage: string;
    };
    bytesToGB(bytes: any): string;
}
