-- 修复支付方式不一致的数据
-- 将transaction_id为BALANCE_开头但payment_method为1的记录修正为payment_method=2

-- 先查看需要修复的数据
SELECT 
    payment_id,
    order_id,
    payment_method,
    transaction_id,
    payment_status,
    create_time
FROM payments 
WHERE payment_method = '1' 
  AND transaction_id LIKE 'BALANCE_%'
  AND create_time >= '2025-08-01 00:00:00'
ORDER BY create_time DESC;

-- 执行修复（请先备份数据！）
-- UPDATE payments 
-- SET payment_method = '2',
--     update_time = NOW(),
--     update_by = 'data_fix'
-- WHERE payment_method = '1' 
--   AND transaction_id LIKE 'BALANCE_%'
--   AND create_time >= '2025-08-01 00:00:00';

-- 验证修复结果
-- SELECT 
--     payment_id,
--     order_id,
--     payment_method,
--     transaction_id,
--     payment_status,
--     update_time
-- FROM payments 
-- WHERE payment_method = '2'
--   AND transaction_id LIKE 'BALANCE_%'
--   AND create_time >= '2025-08-01 00:00:00'
-- ORDER BY create_time DESC;