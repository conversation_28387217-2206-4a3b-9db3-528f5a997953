"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderDto = exports.CreateOrderItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateOrderItemDto {
}
exports.CreateOrderItemDto = CreateOrderItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '商品ID必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '商品ID必须大于0' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量', example: 2 }),
    (0, class_validator_1.IsNumber)({}, { message: '商品数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '商品数量必须大于0' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID', example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '规格ID必须是数字' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '购物车项ID', example: 0, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '购物车项ID必须是数字' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "cartId", void 0);
class CreateOrderDto {
}
exports.CreateOrderDto = CreateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货地址ID', example: 1 }),
    (0, class_validator_1.IsNumber)({}, { message: '收货地址ID必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '收货地址ID必须大于0' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "addressId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单商品列表',
        type: [CreateOrderItemDto],
        example: [{ productId: 1, quantity: 2, specId: 0 }],
    }),
    (0, class_validator_1.IsArray)({ message: '订单商品列表必须是数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateOrderItemDto),
    __metadata("design:type", Array)
], CreateOrderDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配送方式',
        example: '1',
        enum: ['1', '2'],
        enumName: 'DeliveryType',
    }),
    (0, class_validator_1.IsString)({ message: '配送方式必须是字符串' }),
    (0, class_validator_1.IsEnum)(['1', '2'], { message: '配送方式只能是：1配送 2自提' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "deliveryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '预约配送时间',
        example: '2024-06-20 14:00:00',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '预约配送时间必须是字符串' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "deliveryTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '使用的优惠券ID',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '优惠券ID必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '优惠券ID必须大于0' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "couponId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '订单备注',
        example: '请尽快发货',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单备注必须是字符串' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否是用户发起的团购',
        example: false,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '是否是用户发起的团购必须是布尔值' }),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "isUserInitiatedGroupBuy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '参与的拼团ID（参团时必填）',
        example: 'a1b2c3d4e5f6',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '拼团ID必须是字符串' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "userGroupBuyId", void 0);
//# sourceMappingURL=create-order.dto.js.map