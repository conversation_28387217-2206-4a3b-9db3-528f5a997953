"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Captcha = Captcha;
const common_1 = require("@nestjs/common");
const enum_1 = require("../enum");
const decorator_1 = require("../utils/decorator");
const result_1 = require("../utils/result");
const config_service_1 = require("../../module/system/config/config.service");
const redis_service_1 = require("../../module/common/redis/redis.service");
function Captcha(CACHE_KEY) {
    const injectRedis = (0, common_1.Inject)(redis_service_1.RedisService);
    const injectConfig = (0, common_1.Inject)(config_service_1.ConfigService);
    return function (target, propertyKey, descriptor) {
        injectRedis(target, 'redisService');
        injectConfig(target, 'configService');
        const originMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const enable = await this.configService.getConfigValue('sys.account.captchaEnabled');
            const captchaEnabled = enable === 'true';
            if (captchaEnabled) {
                const user = (0, decorator_1.paramsKeyGetObj)(originMethod, CACHE_KEY, args);
                const code = await this.redisService.get(enum_1.CacheEnum.CAPTCHA_CODE_KEY + user.uuid);
                if (!user.code)
                    return result_1.ResultData.fail(500, `请输入验证码`);
                if (!code)
                    return result_1.ResultData.fail(500, `验证码已过期`);
                if (code !== user.code)
                    return result_1.ResultData.fail(500, `验证码错误`);
            }
            const result = await originMethod.apply(this, args);
            return result;
        };
    };
}
//# sourceMappingURL=captcha.decorator.js.map