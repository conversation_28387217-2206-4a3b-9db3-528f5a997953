import { DictService } from './dict.service';
import { CreateDictTypeDto, UpdateDictTypeDto, ListDictType, CreateDictDataDto, UpdateDictDataDto, ListDictData } from './dto/index';
import { Response } from 'express';
export declare class DictController {
    private readonly dictService;
    constructor(dictService: DictService);
    createType(createDictTypeDto: CreateDictTypeDto, req: any): Promise<import("../../../common/utils/result").ResultData>;
    refreshCache(): Promise<import("../../../common/utils/result").ResultData>;
    deleteType(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    updateType(updateDictTypeDto: UpdateDictTypeDto): Promise<import("../../../common/utils/result").ResultData>;
    findAllType(query: ListDictType): Promise<import("../../../common/utils/result").ResultData>;
    findOptionselect(): Promise<import("../../../common/utils/result").ResultData>;
    findOneType(id: string): Promise<import("../../../common/utils/result").ResultData>;
    createDictData(createDictDataDto: CreateDictDataDto, req: any): Promise<import("../../../common/utils/result").ResultData>;
    deleteDictData(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    updateDictData(updateDictDataDto: UpdateDictDataDto): Promise<import("../../../common/utils/result").ResultData>;
    findAllData(query: ListDictData): Promise<import("../../../common/utils/result").ResultData>;
    findOneDictData(dictCode: string): Promise<import("../../../common/utils/result").ResultData>;
    findOneDataType(dictType: string): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListDictType): Promise<void>;
    exportData(res: Response, body: ListDictType): Promise<void>;
}
