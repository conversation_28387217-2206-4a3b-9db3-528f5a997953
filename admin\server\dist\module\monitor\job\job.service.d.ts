import { SchedulerRegistry } from '@nestjs/schedule';
import { Repository } from 'typeorm';
import { Job } from './entities/job.entity';
import { CreateJobDto, ListJobDto } from './dto/create-job.dto';
import { ResultData } from 'src/common/utils/result';
import { TaskService } from './task.service';
import { Response } from 'express';
export declare class JobService {
    private schedulerRegistry;
    private jobRepository;
    private taskService;
    private readonly logger;
    constructor(schedulerRegistry: SchedulerRegistry, jobRepository: Repository<Job>, taskService: TaskService);
    private initializeJobs;
    list(query: {
        pageNum?: number;
        pageSize?: number;
        jobName?: string;
        jobGroup?: string;
        status?: string;
    }): Promise<ResultData>;
    getJob(jobId: number): Promise<ResultData>;
    create(createJobDto: CreateJobDto, userName: string): Promise<ResultData>;
    update(jobId: number, updateJobDto: Partial<Job>, userName: string): Promise<ResultData>;
    remove(jobIds: number | number[]): Promise<ResultData>;
    changeStatus(jobId: number, status: string, userName: string): Promise<ResultData>;
    run(jobId: number): Promise<ResultData>;
    private addCronJob;
    private deleteCronJob;
    private getCronJob;
    export(res: Response, body: ListJobDto): Promise<void>;
}
