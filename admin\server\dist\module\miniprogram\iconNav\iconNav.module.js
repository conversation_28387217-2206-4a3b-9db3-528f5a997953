"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IconNavModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const iconNav_controller_1 = require("./iconNav.controller");
const iconNav_service_1 = require("./iconNav.service");
const iconNav_entity_1 = require("./entities/iconNav.entity");
const upload_module_1 = require("../../upload/upload.module");
let IconNavModule = class IconNavModule {
};
exports.IconNavModule = IconNavModule;
exports.IconNavModule = IconNavModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([iconNav_entity_1.IconNavEntity]), upload_module_1.UploadModule],
        controllers: [iconNav_controller_1.IconNavController],
        providers: [iconNav_service_1.IconNavService],
        exports: [iconNav_service_1.IconNavService],
    })
], IconNavModule);
//# sourceMappingURL=iconNav.module.js.map