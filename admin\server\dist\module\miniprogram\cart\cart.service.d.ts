import { Repository, DataSource } from 'typeorm';
import { CartEntity } from './entities/cart.entity';
import { AddToCartDto } from './dto/add-to-cart.dto';
import { UpdateCartDto } from './dto/update-cart.dto';
import { CartQueryDto } from './dto/cart-query.dto';
import { ResultData } from '../../../common/utils/result';
import { SysUploadEntity } from '../../upload/entities/upload.entity';
export declare class CartService {
    private readonly cartRepository;
    private readonly uploadRepository;
    private readonly dataSource;
    private readonly logger;
    constructor(cartRepository: Repository<CartEntity>, uploadRepository: Repository<SysUploadEntity>, dataSource: DataSource);
    private processProductImages;
    addToCart(userId: number, addToCartDto: AddToCartDto): Promise<ResultData>;
    getCartItems(userId: number): Promise<ResultData>;
    updateCartItem(userId: number, cartId: number, updateCartDto: UpdateCartDto): Promise<ResultData>;
    removeFromCart(userId: number, cartId: number): Promise<ResultData>;
    clearCart(userId: number): Promise<ResultData>;
    getCartCount(userId: number): Promise<ResultData>;
    findAll(queryDto: CartQueryDto): Promise<ResultData>;
    batchRemove(cartIds: number[]): Promise<ResultData>;
}
