"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiDataResponse = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const result_1 = require("../utils/result");
const baseTypeNames = ['String', 'Number', 'Boolean'];
const ApiDataResponse = (model, isArray, isPager) => {
    let items = null;
    const modelIsBaseType = model && baseTypeNames.includes(model.name);
    if (modelIsBaseType) {
        items = { type: model.name.toLocaleLowerCase() };
    }
    else {
        items = { $ref: (0, swagger_1.getSchemaPath)(model) };
    }
    let prop = null;
    if (isArray && isPager) {
        prop = {
            type: 'object',
            properties: {
                list: {
                    type: 'array',
                    items,
                },
                total: {
                    type: 'number',
                    default: 0,
                },
            },
        };
    }
    else if (isArray) {
        prop = {
            type: 'array',
            items,
        };
    }
    else if (model) {
        prop = items;
    }
    else {
        prop = prop = {
            type: 'object',
            properties: {
                value: {
                    type: 'boolean',
                    default: true,
                },
            },
        };
    }
    return (0, common_1.applyDecorators)((0, swagger_1.ApiExtraModels)(...(model && !modelIsBaseType ? [result_1.ResultData, model] : [result_1.ResultData])), (0, swagger_1.ApiOkResponse)({
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(result_1.ResultData) },
                {
                    properties: {
                        data: prop,
                    },
                },
            ],
        },
    }));
};
exports.ApiDataResponse = ApiDataResponse;
//# sourceMappingURL=apiDataResponse.decorator.js.map