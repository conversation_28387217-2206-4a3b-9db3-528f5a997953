"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let PaymentEntity = class PaymentEntity extends base_1.BaseEntity {
};
exports.PaymentEntity = PaymentEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'payment_id', comment: '支付记录ID主键' }),
    __metadata("design:type", Number)
], PaymentEntity.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'order_id', length: 32, comment: '关联订单ID' }),
    (0, typeorm_1.Index)('idx_order_id'),
    __metadata("design:type", String)
], PaymentEntity.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '支付用户ID' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], PaymentEntity.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'payment_method',
        length: 1,
        default: '1',
        comment: '支付方式：1微信支付2余额支付3模拟支付',
    }),
    __metadata("design:type", String)
], PaymentEntity.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'payment_amount',
        precision: 10,
        scale: 2,
        comment: '支付金额',
    }),
    __metadata("design:type", Number)
], PaymentEntity.prototype, "paymentAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: '1' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'payment_status',
        length: 1,
        default: '1',
        comment: '支付状态：1待支付2支付成功3支付失败4已退款',
    }),
    (0, typeorm_1.Index)('idx_payment_status'),
    __metadata("design:type", String)
], PaymentEntity.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方交易号', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'transaction_id',
        length: 64,
        nullable: true,
        comment: '第三方交易号（微信支付交易号）',
    }),
    (0, typeorm_1.Index)('idx_transaction_id'),
    __metadata("design:type", String)
], PaymentEntity.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'payment_time',
        nullable: true,
        comment: '支付完成时间',
    }),
    __metadata("design:type", Date)
], PaymentEntity.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额', example: 0 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'refund_amount',
        precision: 10,
        scale: 2,
        default: 0,
        comment: '退款金额',
    }),
    __metadata("design:type", Number)
], PaymentEntity.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款时间', required: false }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'refund_time',
        nullable: true,
        comment: '退款时间',
    }),
    __metadata("design:type", Date)
], PaymentEntity.prototype, "refundTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付回调数据', required: false }),
    (0, typeorm_1.Column)({
        type: 'text',
        name: 'callback_data',
        nullable: true,
        comment: '支付回调数据（JSON格式）',
    }),
    __metadata("design:type", String)
], PaymentEntity.prototype, "callbackData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '失败原因', required: false }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'failure_reason',
        length: 200,
        nullable: true,
        comment: '支付失败原因',
    }),
    __metadata("design:type", String)
], PaymentEntity.prototype, "failureReason", void 0);
exports.PaymentEntity = PaymentEntity = __decorate([
    (0, typeorm_1.Entity)('payments', { comment: '支付记录表' })
], PaymentEntity);
//# sourceMappingURL=payment.entity.js.map