import { FavoriteService } from './favorite.service';
import { CreateFavoriteDto, FavoriteQueryDto } from './dto';
import { ResultData } from '../../../common/utils/result';
export declare class FavoriteController {
    private readonly favoriteService;
    constructor(favoriteService: FavoriteService);
    addFavorite(createFavoriteDto: CreateFavoriteDto): Promise<ResultData>;
    cancelFavorite(userId: string, favoriteId: number): Promise<ResultData>;
    getFavoriteList(query: FavoriteQueryDto): Promise<ResultData>;
    checkFavorite(userId: string, productId: number): Promise<ResultData>;
}
