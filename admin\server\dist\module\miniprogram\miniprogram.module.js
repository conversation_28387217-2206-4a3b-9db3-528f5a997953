"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MiniprogramModule = void 0;
const common_1 = require("@nestjs/common");
const user_module_1 = require("./user/user.module");
const category_module_1 = require("./category/category.module");
const product_module_1 = require("./product/product.module");
const cart_module_1 = require("./cart/cart.module");
const address_module_1 = require("./address/address.module");
const order_module_1 = require("./order/order.module");
const payment_module_1 = require("./payment/payment.module");
const marketing_module_1 = require("./marketing/marketing.module");
const review_module_1 = require("./review/review.module");
const footprint_module_1 = require("./footprint/footprint.module");
const favorite_module_1 = require("./favorite/favorite.module");
const banner_module_1 = require("./banner/banner.module");
const iconNav_module_1 = require("./iconNav/iconNav.module");
const delivery_settings_module_1 = require("./delivery-settings/delivery-settings.module");
const miniprogram_api_controller_1 = require("./miniprogram-api.controller");
let MiniprogramModule = class MiniprogramModule {
};
exports.MiniprogramModule = MiniprogramModule;
exports.MiniprogramModule = MiniprogramModule = __decorate([
    (0, common_1.Module)({
        imports: [
            user_module_1.UserModule,
            category_module_1.CategoryModule,
            product_module_1.ProductModule,
            cart_module_1.CartModule,
            address_module_1.AddressModule,
            order_module_1.OrderModule,
            payment_module_1.PaymentModule,
            marketing_module_1.MarketingModule,
            review_module_1.ReviewModule,
            footprint_module_1.FootprintModule,
            favorite_module_1.FavoriteModule,
            banner_module_1.BannerModule,
            iconNav_module_1.IconNavModule,
            delivery_settings_module_1.DeliverySettingsModule,
        ],
        controllers: [miniprogram_api_controller_1.MiniprogramApiController],
        exports: [
            user_module_1.UserModule,
            category_module_1.CategoryModule,
            product_module_1.ProductModule,
            cart_module_1.CartModule,
            address_module_1.AddressModule,
            order_module_1.OrderModule,
            payment_module_1.PaymentModule,
            marketing_module_1.MarketingModule,
            review_module_1.ReviewModule,
            footprint_module_1.FootprintModule,
            favorite_module_1.FavoriteModule,
            banner_module_1.BannerModule,
            iconNav_module_1.IconNavModule,
            delivery_settings_module_1.DeliverySettingsModule,
        ],
    })
], MiniprogramModule);
//# sourceMappingURL=miniprogram.module.js.map