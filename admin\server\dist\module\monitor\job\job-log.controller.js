"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobLogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const job_log_service_1 = require("./job-log.service");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
const create_job_dto_1 = require("./dto/create-job.dto");
let JobLogController = class JobLogController {
    constructor(jobLogService) {
        this.jobLogService = jobLogService;
    }
    list(query) {
        return this.jobLogService.list(query);
    }
    clean() {
        return this.jobLogService.clean();
    }
    async export(res, body) {
        return this.jobLogService.export(res, body);
    }
};
exports.JobLogController = JobLogController;
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取定时任务日志列表' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_job_dto_1.ListJobLogDto]),
    __metadata("design:returntype", void 0)
], JobLogController.prototype, "list", null);
__decorate([
    (0, common_1.Delete)('clean'),
    (0, swagger_1.ApiOperation)({ summary: '清空定时任务日志' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:remove'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], JobLogController.prototype, "clean", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出调度日志为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:job:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_job_dto_1.ListJobLogDto]),
    __metadata("design:returntype", Promise)
], JobLogController.prototype, "export", null);
exports.JobLogController = JobLogController = __decorate([
    (0, swagger_1.ApiTags)('定时任务日志管理'),
    (0, common_1.Controller)('monitor/jobLog'),
    __metadata("design:paramtypes", [job_log_service_1.JobLogService])
], JobLogController);
//# sourceMappingURL=job-log.controller.js.map