"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let BannerEntity = class BannerEntity extends base_1.BaseEntity {
};
exports.BannerEntity = BannerEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '轮播图ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'banner_id', comment: '轮播图ID' }),
    __metadata("design:type", Number)
], BannerEntity.prototype, "bannerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, name: 'image_url', comment: '图片URL' }),
    __metadata("design:type", String)
], BannerEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'sort_order', default: 0, comment: '排序顺序' }),
    __metadata("design:type", Number)
], BannerEntity.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '链接URL' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, name: 'link_url', nullable: true, comment: '链接URL' }),
    __metadata("design:type", String)
], BannerEntity.prototype, "linkUrl", void 0);
exports.BannerEntity = BannerEntity = __decorate([
    (0, typeorm_1.Entity)('mini_banner', { comment: '小程序轮播图表' })
], BannerEntity);
//# sourceMappingURL=banner.entity.js.map