import { ResultData } from 'src/common/utils/result';
import { UserService } from '../system/user/user.service';
import { LoginlogService } from '../monitor/loginlog/loginlog.service';
import { AxiosService } from 'src/module/common/axios/axios.service';
import { RegisterDto, LoginDto } from './dto/index';
import { MenuService } from '../system/menu/menu.service';
import { ClientInfoDto } from 'src/common/decorators/common.decorator';
export declare class MainService {
    private readonly userService;
    private readonly loginlogService;
    private readonly axiosService;
    private readonly menuService;
    constructor(userService: UserService, loginlogService: LoginlogService, axiosService: AxiosService, menuService: MenuService);
    login(user: LoginDto, clientInfo: ClientInfoDto): Promise<ResultData>;
    logout(clientInfo: ClientInfoDto): Promise<ResultData>;
    register(user: RegisterDto): Promise<ResultData>;
    loginRecord(): void;
    getRouters(userId: number): Promise<ResultData>;
}
