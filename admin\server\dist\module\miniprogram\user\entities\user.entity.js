"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MiniprogramUser = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let MiniprogramUser = class MiniprogramUser extends base_1.BaseEntity {
};
exports.MiniprogramUser = MiniprogramUser;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '用户ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'user_id', comment: '用户ID' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '微信OpenID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'openid', length: 100, nullable: true, comment: '微信OpenID' }),
    (0, typeorm_1.Index)('idx_openid'),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "openid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '微信UnionID', required: false }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'unionid', length: 100, nullable: true, comment: '微信UnionID' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "unionid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户昵称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'nickname', length: 50, default: '', comment: '用户昵称' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户头像' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'avatar', length: 500, default: '', comment: '用户头像' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户性别', enum: ['0', '1', '2'] }),
    (0, typeorm_1.Column)({ type: 'char', name: 'gender', length: 1, default: '0', comment: '用户性别：0-未知，1-男，2-女' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '手机号码' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'phone', length: 20, default: '', comment: '手机号码' }),
    (0, typeorm_1.Index)('idx_phone'),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户邮箱' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'email', length: 100, default: '', comment: '用户邮箱' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户生日' }),
    (0, typeorm_1.Column)({ type: 'date', name: 'birthday', nullable: true, comment: '用户生日' }),
    __metadata("design:type", Date)
], MiniprogramUser.prototype, "birthday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户地区' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'region', length: 100, default: '', comment: '用户地区' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '用户等级' }),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'level', default: 1, comment: '用户等级' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '用户积分' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'points', default: 0, comment: '用户积分' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "points", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '用户余额' }),
    (0, typeorm_1.Column)({ type: 'decimal', name: 'balance', precision: 10, scale: 2, default: 0, comment: '用户余额' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "balance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '累计消费金额' }),
    (0, typeorm_1.Column)({ type: 'decimal', name: 'total_amount', precision: 10, scale: 2, default: 0, comment: '累计消费金额' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '订单数量' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'order_count', default: 0, comment: '订单数量' }),
    __metadata("design:type", Number)
], MiniprogramUser.prototype, "orderCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date, description: '最后登录时间' }),
    (0, typeorm_1.Column)({ type: 'datetime', name: 'last_login_time', nullable: true, comment: '最后登录时间' }),
    __metadata("design:type", Date)
], MiniprogramUser.prototype, "lastLoginTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '最后登录IP' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'last_login_ip', length: 50, default: '', comment: '最后登录IP' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "lastLoginIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户标签' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'tags', length: 500, default: '', comment: '用户标签，多个用逗号分隔' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户类型', enum: ['0', '1', '2'] }),
    (0, typeorm_1.Column)({ type: 'char', name: 'user_type', length: 1, default: '0', comment: '用户类型：0-普通用户，1-VIP用户，2-管理员' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "userType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date, description: 'VIP到期时间' }),
    (0, typeorm_1.Column)({ type: 'datetime', name: 'vip_expire_time', nullable: true, comment: 'VIP到期时间' }),
    __metadata("design:type", Date)
], MiniprogramUser.prototype, "vipExpireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否接收推送' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'push_enabled', length: 1, default: '1', comment: '是否接收推送：0-否，1-是' }),
    __metadata("design:type", String)
], MiniprogramUser.prototype, "pushEnabled", void 0);
exports.MiniprogramUser = MiniprogramUser = __decorate([
    (0, typeorm_1.Entity)('miniprogram_user', { comment: '小程序用户表' })
], MiniprogramUser);
//# sourceMappingURL=user.entity.js.map