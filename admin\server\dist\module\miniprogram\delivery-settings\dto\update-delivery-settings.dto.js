"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliverySettingsQueryDto = exports.UpdateDeliverySettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const create_delivery_settings_dto_1 = require("./create-delivery-settings.dto");
class UpdateDeliverySettingsDto extends (0, swagger_1.PartialType)(create_delivery_settings_dto_1.CreateDeliverySettingsDto) {
}
exports.UpdateDeliverySettingsDto = UpdateDeliverySettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '超市名称', example: '初鲜果味超市', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '超市名称必须是字符串' }),
    (0, class_validator_1.Length)(2, 100, { message: '超市名称长度为2-100个字符' }),
    __metadata("design:type", String)
], UpdateDeliverySettingsDto.prototype, "storeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度', example: 22.5329, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '纬度必须是数字' }),
    (0, class_validator_1.Min)(-90, { message: '纬度值范围为-90到90' }),
    (0, class_validator_1.Max)(90, { message: '纬度值范围为-90到90' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateDeliverySettingsDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度', example: 114.0544, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '经度必须是数字' }),
    (0, class_validator_1.Min)(-180, { message: '经度值范围为-180到180' }),
    (0, class_validator_1.Max)(180, { message: '经度值范围为-180到180' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateDeliverySettingsDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送半径(km)', example: 5, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '配送半径必须是数字' }),
    (0, class_validator_1.Min)(0.1, { message: '配送半径必须大于0.1km' }),
    (0, class_validator_1.Max)(100, { message: '配送半径不能超过100km' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateDeliverySettingsDto.prototype, "deliveryRadius", void 0);
class DeliverySettingsQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.DeliverySettingsQueryDto = DeliverySettingsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '超市名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '超市名称必须是字符串' }),
    __metadata("design:type", String)
], DeliverySettingsQueryDto.prototype, "storeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DeliverySettingsQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页条数', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '每页条数必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页条数必须大于0' }),
    (0, class_validator_1.Max)(100, { message: '每页条数不能超过100' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], DeliverySettingsQueryDto.prototype, "pageSize", void 0);
//# sourceMappingURL=update-delivery-settings.dto.js.map