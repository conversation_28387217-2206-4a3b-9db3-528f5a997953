import { Repository } from 'typeorm';
import { FavoriteEntity } from './entities/favorite.entity';
import { CreateFavoriteDto, FavoriteQueryDto } from './dto';
import { ProductEntity } from '../product/entities/product.entity';
import { ResultData } from '../../../common/utils/result';
import { SysUploadEntity } from '../../upload/entities/upload.entity';
export declare class FavoriteService {
    private readonly favoriteRepository;
    private readonly productRepository;
    private readonly uploadRepository;
    private readonly logger;
    constructor(favoriteRepository: Repository<FavoriteEntity>, productRepository: Repository<ProductEntity>, uploadRepository: Repository<SysUploadEntity>);
    private processProductImages;
    addFavorite(createFavoriteDto: CreateFavoriteDto): Promise<ResultData>;
    cancelFavorite(userId: string, favoriteId: number): Promise<ResultData>;
    getFavoriteList(userId: string, query: FavoriteQueryDto): Promise<ResultData>;
    checkFavorite(userId: string, productId: number): Promise<ResultData>;
}
