"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeptController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const dept_service_1 = require("./dept.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let DeptController = class DeptController {
    constructor(deptService) {
        this.deptService = deptService;
    }
    create(createDeptDto) {
        return this.deptService.create(createDeptDto);
    }
    findAll(query) {
        return this.deptService.findAll(query);
    }
    findOne(id) {
        return this.deptService.findOne(+id);
    }
    findListExclude(id) {
        return this.deptService.findListExclude(+id);
    }
    update(updateDeptDto) {
        return this.deptService.update(updateDeptDto);
    }
    remove(id) {
        return this.deptService.remove(+id);
    }
};
exports.DeptController = DeptController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateDeptDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:add'),
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(200),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateDeptDto]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-列表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListDeptDto]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:query'),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-黑名单',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:query'),
    (0, common_1.Get)('/list/exclude/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "findListExclude", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-更新',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdateDeptDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateDeptDto]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '部门管理-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dept:remove'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DeptController.prototype, "remove", null);
exports.DeptController = DeptController = __decorate([
    (0, swagger_1.ApiTags)('部门管理'),
    (0, common_1.Controller)('system/dept'),
    __metadata("design:paramtypes", [dept_service_1.DeptService])
], DeptController);
//# sourceMappingURL=dept.controller.js.map