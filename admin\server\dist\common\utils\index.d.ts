import 'dayjs/locale/zh-cn';
import { DataScopeEnum } from '../enum/index';
export declare function ListToTree(arr: any, getId: any, getLabel: any): any[];
export declare function GetNowDate(): string;
export declare function FormatDate(date: Date, format?: string): string;
export declare function DeepClone<T>(obj: T): T;
export declare function GenerateUUID(): string;
export declare function Uniq<T extends number | string>(list: Array<T>): Array<T>;
export declare function Paginate(data: {
    list: Array<any>;
    pageSize: number;
    pageNum: number;
}, filterParam: any): any[];
export declare function DataScopeFilter<T>(entity: any, dataScope: DataScopeEnum): Promise<T>;
export declare function isObject(item: any): boolean;
export declare function mergeDeep(target: any, ...sources: any[]): any;
