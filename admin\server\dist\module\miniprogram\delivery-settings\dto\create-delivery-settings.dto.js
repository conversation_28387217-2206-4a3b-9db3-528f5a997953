"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDeliverySettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateDeliverySettingsDto {
}
exports.CreateDeliverySettingsDto = CreateDeliverySettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '超市名称', example: '初鲜果味超市' }),
    (0, class_validator_1.IsNotEmpty)({ message: '超市名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '超市名称必须是字符串' }),
    (0, class_validator_1.Length)(2, 100, { message: '超市名称长度为2-100个字符' }),
    __metadata("design:type", String)
], CreateDeliverySettingsDto.prototype, "storeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度', example: 22.5329 }),
    (0, class_validator_1.IsNotEmpty)({ message: '纬度不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '纬度必须是数字' }),
    (0, class_validator_1.Min)(-90, { message: '纬度值范围为-90到90' }),
    (0, class_validator_1.Max)(90, { message: '纬度值范围为-90到90' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateDeliverySettingsDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度', example: 114.0544 }),
    (0, class_validator_1.IsNotEmpty)({ message: '经度不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '经度必须是数字' }),
    (0, class_validator_1.Min)(-180, { message: '经度值范围为-180到180' }),
    (0, class_validator_1.Max)(180, { message: '经度值范围为-180到180' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateDeliverySettingsDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送半径(km)', example: 5 }),
    (0, class_validator_1.IsNotEmpty)({ message: '配送半径不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '配送半径必须是数字' }),
    (0, class_validator_1.Min)(0.1, { message: '配送半径必须大于0.1km' }),
    (0, class_validator_1.Max)(100, { message: '配送半径不能超过100km' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateDeliverySettingsDto.prototype, "deliveryRadius", void 0);
//# sourceMappingURL=create-delivery-settings.dto.js.map