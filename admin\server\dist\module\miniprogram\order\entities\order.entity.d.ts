import { BaseEntity } from '../../../../common/entities/base';
import { OrderItemEntity } from './order-item.entity';
import { UserAddress } from '../../address/entities/address.entity';
import { MiniprogramUser } from '../../user/entities/user.entity';
export declare class OrderEntity extends BaseEntity {
    orderId: string;
    userId: number;
    addressId: number;
    userGroupBuyId: string | null;
    isGroupInitiator: number;
    orderType: string;
    totalAmount: number;
    discountAmount: number;
    finalAmount: number;
    deliveryType: string;
    deliveryTime: Date | null;
    status: string;
    reviewStatus: string;
    paymentTime: Date | null;
    shipmentTime: Date | null;
    completionTime: Date | null;
    cancelReason: string | null;
    receiverName: string;
    receiverPhone: string;
    receiverAddress: string;
    refundReason: string | null;
    refundTime: Date | null;
    refundType: string | null;
    user?: MiniprogramUser;
    address?: UserAddress;
    orderItems?: OrderItemEntity[];
}
