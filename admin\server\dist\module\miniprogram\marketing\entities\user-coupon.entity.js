"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCoupon = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
const coupon_interface_1 = require("../interfaces/coupon.interface");
const coupon_entity_1 = require("./coupon.entity");
let UserCoupon = class UserCoupon extends base_1.BaseEntity {
};
exports.UserCoupon = UserCoupon;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'id', comment: '用户优惠券ID' }),
    __metadata("design:type", Number)
], UserCoupon.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID' }),
    (0, typeorm_1.Index)('idx_user_coupon_user_id'),
    __metadata("design:type", Number)
], UserCoupon.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'coupon_id', comment: '优惠券ID' }),
    (0, typeorm_1.Index)('idx_user_coupon_coupon_id'),
    __metadata("design:type", Number)
], UserCoupon.prototype, "couponId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态 0未使用1已使用2已过期' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'status',
        length: 1,
        comment: '状态 0未使用1已使用2已过期',
        default: coupon_interface_1.UserCouponStatus.UNUSED,
    }),
    (0, typeorm_1.Index)('idx_user_coupon_status'),
    __metadata("design:type", String)
], UserCoupon.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '领取时间' }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'receive_time',
        comment: '领取时间',
    }),
    __metadata("design:type", Date)
], UserCoupon.prototype, "receiveTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用时间' }),
    (0, typeorm_1.Column)({
        type: 'datetime',
        name: 'use_time',
        comment: '使用时间',
        nullable: true,
    }),
    __metadata("design:type", Date)
], UserCoupon.prototype, "useTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'order_id',
        length: 32,
        comment: '订单ID',
        nullable: true,
    }),
    (0, typeorm_1.Index)('idx_user_coupon_order_id'),
    __metadata("design:type", String)
], UserCoupon.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '优惠券信息',
        type: () => coupon_entity_1.Coupon,
    }),
    (0, typeorm_1.ManyToOne)(() => coupon_entity_1.Coupon, (coupon) => coupon.userCoupons, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'coupon_id' }),
    __metadata("design:type", coupon_entity_1.Coupon)
], UserCoupon.prototype, "coupon", void 0);
exports.UserCoupon = UserCoupon = __decorate([
    (0, typeorm_1.Entity)('t_user_coupon', { comment: '用户优惠券表' })
], UserCoupon);
//# sourceMappingURL=user-coupon.entity.js.map