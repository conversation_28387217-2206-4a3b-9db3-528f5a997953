"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_service_1 = require("./config.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let ConfigController = class ConfigController {
    constructor(configService) {
        this.configService = configService;
    }
    create(createConfigDto, req) {
        createConfigDto['createBy'] = req.user.userName;
        return this.configService.create(createConfigDto);
    }
    findAll(query) {
        return this.configService.findAll(query);
    }
    findOne(id) {
        return this.configService.findOne(+id);
    }
    findOneByconfigKey(configKey) {
        return this.configService.findOneByConfigKey(configKey);
    }
    update(updateConfigDto) {
        return this.configService.update(updateConfigDto);
    }
    refreshCache() {
        return this.configService.resetConfigCache();
    }
    remove(ids) {
        const configIds = ids.split(',').map((id) => +id);
        return this.configService.remove(configIds);
    }
    async export(res, body) {
        return this.configService.export(res, body);
    }
};
exports.ConfigController = ConfigController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateConfigDto,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:add'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateConfigDto, Object]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ListConfigDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListConfigDto]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-详情(id)',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:query'),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-详情(configKey)【走缓存】',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:query'),
    (0, common_1.Get)('/configKey/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "findOneByconfigKey", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-更新',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateConfigDto]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-刷新缓存',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:remove'),
    (0, common_1.Delete)('/refreshCache'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "refreshCache", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '参数设置-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:remove'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ConfigController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出参数管理为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListConfigDto]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "export", null);
exports.ConfigController = ConfigController = __decorate([
    (0, swagger_1.ApiTags)('参数设置'),
    (0, common_1.Controller)('system/config'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], ConfigController);
//# sourceMappingURL=config.controller.js.map