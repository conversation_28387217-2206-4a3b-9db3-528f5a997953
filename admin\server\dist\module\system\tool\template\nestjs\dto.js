"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.dtoTem = void 0;
const Lodash = __importStar(require("lodash"));
const dtoTem = (options) => {
    const { BusinessName } = options;
    const insertExclude = getExcludeClounmByType(options, 'isInsert');
    const editExclude = getExcludeClounmByType(options, 'isEdit');
    const queryExclude = getExcludeClounmByType(options, 'isQuery');
    const listExclude = getExcludeClounmByType(options, 'isList');
    const All = getAllBaseDto(options);
    return `
import { IsString, IsNumber, IsBoolean, IsDate, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty, OmitType, IntersectionType } from '@nestjs/swagger';
import { PagingDto } from 'src/common/dto/index';
import { CharEnum } from 'src/common/enum/index';


export class Base${Lodash.upperFirst(BusinessName)}Dto{
${All}
}

export class Create${Lodash.upperFirst(BusinessName)}Dto extends OmitType(Base${Lodash.upperFirst(BusinessName)}Dto, [${insertExclude}]){}

export class Update${Lodash.upperFirst(BusinessName)}Dto extends OmitType(Base${Lodash.upperFirst(BusinessName)}Dto, [${editExclude}]){}

export class Query${Lodash.upperFirst(BusinessName)}Dto extends OmitType(IntersectionType( Base${Lodash.upperFirst(BusinessName)}Dto, PagingDto), [${queryExclude}]){}

export class List${Lodash.upperFirst(BusinessName)}Dto  extends OmitType(Base${Lodash.upperFirst(BusinessName)}Dto, [${listExclude}]) {}
`;
};
exports.dtoTem = dtoTem;
const getExcludeClounmByType = (options, type) => {
    const { columns } = options;
    return columns
        .filter((column) => {
        return column[type] === '0';
    })
        .map((column) => {
        const { javaField } = column;
        return ` '${javaField}'`;
    })
        .join(',');
};
const getAllBaseDto = (options) => {
    const { columns } = options;
    return columns
        .map((column) => {
        const { javaType, javaField, isRequired, columnComment, columnType } = column;
        const type = lowercaseFirstLetter(javaType);
        const decorators = [
            `@ApiProperty({${columnType === 'char' ? 'enum: CharEnum, ' : ''}required: ${isRequired == 1} , description: '${columnComment}'})`,
            isRequired != 1 && `\t@IsOptional()`,
            '\t' + getValidatorDecorator(javaType),
        ]
            .filter(Boolean)
            .join('\n');
        return `\t${decorators}\n\t${javaField}${isRequired == 1 ? '' : '?'}: ${type == 'Date' ? javaType : type};\n`;
    })
        .join('\n');
};
function getValidatorDecorator(javaType) {
    switch (javaType) {
        case 'String':
            return `@IsString()`;
        case 'Number':
            return `@IsNumber()`;
        case 'Boolean':
            return `@IsBoolean()`;
        case 'Date':
            return `@IsString()`;
        default:
            return ``;
    }
}
function lowercaseFirstLetter(str) {
    if (str === 'Date') {
        return 'string';
    }
    return str.charAt(0).toLowerCase() + str.slice(1);
}
//# sourceMappingURL=dto.js.map