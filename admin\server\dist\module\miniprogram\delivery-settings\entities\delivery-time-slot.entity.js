"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryTimeSlot = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let DeliveryTimeSlot = class DeliveryTimeSlot {
};
exports.DeliveryTimeSlot = DeliveryTimeSlot;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '时间段ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '时间段ID' }),
    __metadata("design:type", Number)
], DeliveryTimeSlot.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送时间段', example: '上午9:00-12:00' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        comment: '配送时间段显示文本',
        name: 'label'
    }),
    __metadata("design:type", String)
], DeliveryTimeSlot.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送时间', example: '10:00:00' }),
    (0, typeorm_1.Column)({
        type: 'time',
        comment: '具体配送时间',
        name: 'value'
    }),
    __metadata("design:type", String)
], DeliveryTimeSlot.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    (0, typeorm_1.Column)({
        type: 'int',
        default: 0,
        comment: '排序，数字越小越靠前',
        name: 'sort_order'
    }),
    __metadata("design:type", Number)
], DeliveryTimeSlot.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: 1 }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '是否启用：1启用，0禁用',
        name: 'is_active'
    }),
    __metadata("design:type", Number)
], DeliveryTimeSlot.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    (0, typeorm_1.CreateDateColumn)({
        comment: '创建时间',
        name: 'create_time'
    }),
    __metadata("design:type", Date)
], DeliveryTimeSlot.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    (0, typeorm_1.UpdateDateColumn)({
        comment: '更新时间',
        name: 'update_time'
    }),
    __metadata("design:type", Date)
], DeliveryTimeSlot.prototype, "updateTime", void 0);
exports.DeliveryTimeSlot = DeliveryTimeSlot = __decorate([
    (0, typeorm_1.Entity)('delivery_time_slots', {
        comment: '配送时间段表',
    })
], DeliveryTimeSlot);
//# sourceMappingURL=delivery-time-slot.entity.js.map