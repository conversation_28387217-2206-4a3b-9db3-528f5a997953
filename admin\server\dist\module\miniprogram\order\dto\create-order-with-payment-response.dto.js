"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderWithPaymentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class OrderWithPaymentResponseDto {
}
exports.OrderWithPaymentResponseDto = OrderWithPaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORDER_20241215_001' }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单总金额', example: 99.99 }),
    __metadata("design:type", Number)
], OrderWithPaymentResponseDto.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠金额', example: 10.0 }),
    __metadata("design:type", Number)
], OrderWithPaymentResponseDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '实付金额', example: 89.99 }),
    __metadata("design:type", Number)
], OrderWithPaymentResponseDto.prototype, "finalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态', example: '1', enum: ['1', '2', '3', '4', '5', '6'] }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "orderStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态（与orderStatus相同，兼容性字段）', example: '1', enum: ['1', '2', '3', '4', '5', '6'] }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    __metadata("design:type", Number)
], OrderWithPaymentResponseDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: '1', enum: ['1', '2', '3', '4'] }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 89.99 }),
    __metadata("design:type", Number)
], OrderWithPaymentResponseDto.prototype, "paymentAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式', example: '1', enum: ['1', '2', '3'] }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '微信支付参数（微信支付时返回）',
        required: false,
        example: {
            appId: 'wx1234567890',
            timeStamp: '1640422800',
            nonceStr: 'abc123',
            package: 'prepay_id=wx1234567890',
            signType: 'RSA',
            paySign: 'signature',
        },
    }),
    __metadata("design:type", Object)
], OrderWithPaymentResponseDto.prototype, "paymentParams", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付信息', required: false }),
    __metadata("design:type", Object)
], OrderWithPaymentResponseDto.prototype, "paymentInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方交易号', required: false, example: 'SIMULATE_1640422800' }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false, example: '2024-12-15 18:00:00' }),
    __metadata("design:type", Date)
], OrderWithPaymentResponseDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '拼团ID', required: false, example: 'a1b2c3d4e5f6' }),
    __metadata("design:type", String)
], OrderWithPaymentResponseDto.prototype, "groupBuyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为拼团发起人', required: false, example: true }),
    __metadata("design:type", Boolean)
], OrderWithPaymentResponseDto.prototype, "isInitiator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-12-15 17:30:00' }),
    __metadata("design:type", Date)
], OrderWithPaymentResponseDto.prototype, "createTime", void 0);
//# sourceMappingURL=create-order-with-payment-response.dto.js.map