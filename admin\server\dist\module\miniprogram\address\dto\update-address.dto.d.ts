import { CreateAddressDto } from './create-address.dto';
declare const UpdateAddressDto_base: import("@nestjs/common").Type<Partial<CreateAddressDto>>;
export declare class UpdateAddressDto extends UpdateAddressDto_base {
    receiverName?: string;
    receiverPhone?: string;
    addressName?: string;
    detailAddress?: string;
}
export declare class SetDefaultAddressDto {
    isDefault: number;
}
export declare class AddressQueryDto {
    label?: string;
    isDefault?: number;
}
export declare class DeliveryRangeDto {
    addressName: string;
    detailAddress: string;
    latitude?: string;
    longitude?: string;
}
export {};
