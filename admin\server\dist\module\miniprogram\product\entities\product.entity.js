"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductEntity = void 0;
const typeorm_1 = require("typeorm");
const category_entity_1 = require("../../category/entities/category.entity");
const product_spec_entity_1 = require("./product-spec.entity");
let ProductEntity = class ProductEntity {
};
exports.ProductEntity = ProductEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'product_id', comment: '商品ID' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, comment: '商品名称' }),
    __metadata("design:type", String)
], ProductEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, comment: '商品价格' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'origin_place', length: 100, nullable: true, comment: '产地' }),
    __metadata("design:type", String)
], ProductEntity.prototype, "originPlace", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true, comment: '商品图片URL列表，逗号分隔' }),
    __metadata("design:type", String)
], ProductEntity.prototype, "images", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '商品描述' }),
    __metadata("design:type", String)
], ProductEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '商品详情' }),
    __metadata("design:type", String)
], ProductEntity.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'category_id', comment: '分类ID' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "categoryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'sales_count', default: 0, comment: '销售数量' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "salesCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'tinyint', default: 1, comment: '状态：1上架，0下架' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'tinyint', default: 1, comment: '是否多规格商品：1是，0否' }),
    __metadata("design:type", Number)
], ProductEntity.prototype, "hasMultiSpecs", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time', comment: '创建时间' }),
    __metadata("design:type", Date)
], ProductEntity.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time', comment: '更新时间' }),
    __metadata("design:type", Date)
], ProductEntity.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => category_entity_1.CategoryEntity, (category) => category.products, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'category_id' }),
    __metadata("design:type", category_entity_1.CategoryEntity)
], ProductEntity.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => product_spec_entity_1.ProductSpecEntity, (spec) => spec.product, { createForeignKeyConstraints: false }),
    __metadata("design:type", Array)
], ProductEntity.prototype, "specs", void 0);
exports.ProductEntity = ProductEntity = __decorate([
    (0, typeorm_1.Entity)('products', { comment: '商品信息表' })
], ProductEntity);
//# sourceMappingURL=product.entity.js.map