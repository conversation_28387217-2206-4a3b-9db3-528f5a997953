import { RedisService } from 'src/module/common/redis/redis.service';
import { ResultData } from 'src/common/utils/result';
export declare class CacheService {
    private readonly redisService;
    constructor(redisService: RedisService);
    private readonly caches;
    getNames(): Promise<ResultData>;
    getKeys(id: string): Promise<ResultData>;
    clearCacheKey(id: string): Promise<ResultData>;
    clearCacheName(id: string): Promise<ResultData>;
    clearCacheAll(): Promise<ResultData>;
    getValue(params: any): Promise<ResultData>;
    getInfo(): Promise<ResultData>;
}
