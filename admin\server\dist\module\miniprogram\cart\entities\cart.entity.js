"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let CartEntity = class CartEntity extends base_1.BaseEntity {
};
exports.CartEntity = CartEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '购物车ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'cart_id', comment: '购物车ID主键' }),
    __metadata("design:type", Number)
], CartEntity.prototype, "cartId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID外键' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], CartEntity.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'product_id', comment: '商品ID外键' }),
    (0, typeorm_1.Index)('idx_product_id'),
    __metadata("design:type", Number)
], CartEntity.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID', example: 0 }),
    (0, typeorm_1.Column)({ type: 'int', name: 'spec_id', default: 0, comment: '商品规格ID，0表示默认规格' }),
    (0, typeorm_1.Index)('idx_spec_id'),
    __metadata("design:type", Number)
], CartEntity.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量', example: 1 }),
    (0, typeorm_1.Column)({ type: 'int', name: 'quantity', default: 1, comment: '商品数量' }),
    __metadata("design:type", Number)
], CartEntity.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('UserEntity', { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id', referencedColumnName: 'userId' }),
    __metadata("design:type", Object)
], CartEntity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('ProductEntity', { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id', referencedColumnName: 'productId' }),
    __metadata("design:type", Object)
], CartEntity.prototype, "product", void 0);
exports.CartEntity = CartEntity = __decorate([
    (0, typeorm_1.Entity)('shopping_cart', { comment: '购物车表' }),
    (0, typeorm_1.Index)('uk_user_product_active', ['userId', 'productId', 'specId', 'delFlag'], { unique: true }),
    (0, typeorm_1.Index)('idx_user_id', ['userId']),
    (0, typeorm_1.Index)('idx_product_id', ['productId'])
], CartEntity);
//# sourceMappingURL=cart.entity.js.map