"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Task = exports.TaskRegistry = exports.TASK_METADATA = void 0;
const common_1 = require("@nestjs/common");
exports.TASK_METADATA = 'task_metadata';
class TaskRegistry {
    constructor() {
        this.tasks = new Map();
    }
    static getInstance() {
        if (!TaskRegistry.instance) {
            TaskRegistry.instance = new TaskRegistry();
        }
        return TaskRegistry.instance;
    }
    register(target, methodName, metadata) {
        const classOrigin = target.constructor;
        this.tasks.set(metadata.name, { classOrigin, methodName, metadata });
    }
    getTasks() {
        return Array.from(this.tasks.values());
    }
    getTask(name) {
        return this.tasks.get(name);
    }
}
exports.TaskRegistry = TaskRegistry;
const Task = (metadata) => {
    return (target, propertyKey, descriptor) => {
        (0, common_1.SetMetadata)(exports.TASK_METADATA, metadata)(target, propertyKey, descriptor);
        TaskRegistry.getInstance().register(target, propertyKey.toString(), metadata);
        return descriptor;
    };
};
exports.Task = Task;
//# sourceMappingURL=task.decorator.js.map