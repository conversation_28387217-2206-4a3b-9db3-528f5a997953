"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProductService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("./entities/product.entity");
const category_entity_1 = require("../category/entities/category.entity");
const upload_entity_1 = require("../../upload/entities/upload.entity");
const result_1 = require("../../../common/utils/result");
const product_spec_entity_1 = require("./entities/product-spec.entity");
const footprint_service_1 = require("../footprint/footprint.service");
let ProductService = ProductService_1 = class ProductService {
    constructor(productRepository, categoryRepository, uploadRepository, productSpecRepository, footprintService) {
        this.productRepository = productRepository;
        this.categoryRepository = categoryRepository;
        this.uploadRepository = uploadRepository;
        this.productSpecRepository = productSpecRepository;
        this.footprintService = footprintService;
        this.logger = new common_1.Logger(ProductService_1.name);
    }
    async processProductImages(products) {
        try {
            const productArray = Array.isArray(products) ? products : [products];
            const processedProducts = productArray.map((product) => {
                const imageList = [];
                if (product.images) {
                    const imageUrls = product.images.split(',').filter((url) => url.trim());
                    imageUrls.forEach((url) => {
                        if (url && url.trim()) {
                            imageList.push({
                                url: url.trim(),
                                fileName: url.split('/').pop() || 'image',
                            });
                        }
                    });
                }
                return {
                    ...product,
                    imageList,
                    originalImages: product.images,
                };
            });
            return Array.isArray(products) ? processedProducts : processedProducts[0];
        }
        catch (error) {
            this.logger.error(`处理商品图片失败: ${error.message}`, error.stack);
            return Array.isArray(products) ? products.map((p) => ({ ...p, imageList: [] })) : { ...products, imageList: [] };
        }
    }
    async create(createProductDto) {
        try {
            this.logger.log(`创建商品: ${JSON.stringify(createProductDto)}`);
            const category = await this.categoryRepository.findOne({
                where: { categoryId: createProductDto.categoryId },
            });
            if (!category) {
                return result_1.ResultData.fail(400, '商品分类不存在');
            }
            if (category.status !== 1) {
                return result_1.ResultData.fail(400, '商品分类已禁用');
            }
            const hasMultiSpecs = createProductDto.hasMultiSpecs || 0;
            const specList = createProductDto.specs || createProductDto.specList || [];
            delete createProductDto.specs;
            delete createProductDto.specList;
            const product = this.productRepository.create({
                ...createProductDto,
                hasMultiSpecs,
            });
            const savedProduct = await this.productRepository.save(product);
            if (hasMultiSpecs === 1 && specList.length > 0) {
                for (let i = 0; i < specList.length; i++) {
                    const spec = specList[i];
                    const isDefault = i === 0 ? 1 : 0;
                    await this.addSpec(savedProduct.productId, {
                        ...spec,
                        isDefault,
                    });
                }
            }
            else if (specList.length === 0) {
                await this.addSpec(savedProduct.productId, {
                    name: '默认规格',
                    price: savedProduct.price,
                    value: '默认',
                    stock: 0,
                    status: 1,
                    isDefault: 1,
                    groupBuyPrice: null,
                });
            }
            this.logger.log(`商品创建成功: ID=${savedProduct.productId}`);
            return result_1.ResultData.ok(savedProduct, '商品创建成功');
        }
        catch (error) {
            this.logger.error(`创建商品失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '创建商品失败');
        }
    }
    async findAll(queryDto) {
        try {
            this.logger.log(`查询商品列表: ${JSON.stringify(queryDto)}`);
            const { name, categoryId, status, minPrice, maxPrice, originPlace, page = 1, pageSize = 10 } = queryDto;
            const skip = (page - 1) * pageSize;
            const queryBuilder = this.productRepository.createQueryBuilder('product').leftJoinAndSelect('product.category', 'category');
            if (name) {
                queryBuilder.andWhere('product.name LIKE :name', { name: `%${name}%` });
            }
            if (categoryId) {
                queryBuilder.andWhere('product.categoryId = :categoryId', { categoryId });
            }
            if (status !== undefined) {
                queryBuilder.andWhere('product.status = :status', { status });
            }
            if (minPrice !== undefined) {
                queryBuilder.andWhere('product.price >= :minPrice', { minPrice });
            }
            if (maxPrice !== undefined) {
                queryBuilder.andWhere('product.price <= :maxPrice', { maxPrice });
            }
            if (originPlace) {
                queryBuilder.andWhere('product.originPlace LIKE :originPlace', {
                    originPlace: `%${originPlace}%`,
                });
            }
            queryBuilder.orderBy('product.createTime', 'DESC');
            queryBuilder.skip(skip).take(pageSize);
            const [list, total] = await queryBuilder.getManyAndCount();
            const processedList = await this.processProductImages(list);
            this.logger.log(`商品列表查询成功: 共${total}条记录`);
            return result_1.ResultData.ok({
                list: processedList,
                total,
                page,
                pageSize,
                totalPages: Math.ceil(total / pageSize),
            }, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询商品列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询商品列表失败');
        }
    }
    async findEnabled(queryDto) {
        try {
            const { categoryId, name } = queryDto;
            this.logger.log(`查询上架商品列表: categoryId=${categoryId}, name=${name || ''}`);
            const queryBuilder = this.productRepository.createQueryBuilder('product').leftJoinAndSelect('product.category', 'category').where('product.status = :status', { status: 1 });
            if (categoryId) {
                queryBuilder.andWhere('product.categoryId = :categoryId', { categoryId });
            }
            if (name) {
                queryBuilder.andWhere('product.name LIKE :name', { name: `%${name}%` });
            }
            queryBuilder.orderBy('product.salesCount', 'DESC').addOrderBy('product.createTime', 'DESC');
            const products = await queryBuilder.getMany();
            const processedProducts = await this.processProductImages(products);
            const productIds = products.map((product) => product.productId);
            if (productIds.length === 0) {
                return result_1.ResultData.ok(processedProducts, '查询成功');
            }
            const allSpecs = await this.productSpecRepository.find({
                where: {
                    productId: (0, typeorm_2.In)(productIds),
                    status: 1,
                },
                order: {
                    isDefault: 'DESC',
                    createTime: 'ASC',
                },
            });
            const specsByProductId = allSpecs.reduce((acc, spec) => {
                if (!acc[spec.productId]) {
                    acc[spec.productId] = [];
                }
                acc[spec.productId].push(spec);
                return acc;
            }, {});
            const finalProducts = processedProducts.map((product) => ({
                ...product,
                specs: specsByProductId[product.productId] || [],
            }));
            this.logger.log(`上架商品查询成功: 共${products.length}条记录`);
            return result_1.ResultData.ok(finalProducts, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询上架商品失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询上架商品失败');
        }
    }
    async findOne(id, userId) {
        try {
            this.logger.log(`查询商品详情: ID=${id}`);
            const product = await this.productRepository.findOne({
                where: { productId: id },
                relations: ['category'],
            });
            if (!product) {
                throw new common_1.NotFoundException(`商品不存在`);
            }
            if (userId) {
                try {
                    this.logger.log(`记录用户足迹: UserId=${userId}, ProductId=${id}`);
                    await this.recordUserFootprint(userId, id);
                }
                catch (error) {
                    this.logger.error(`记录用户足迹失败: ${error.message}`, error.stack);
                }
            }
            const processedProduct = await this.processProductImages(product);
            const specs = await this.productSpecRepository.find({
                where: { productId: id },
                order: { isDefault: 'DESC', createTime: 'ASC' },
            });
            const result = {
                ...processedProduct,
                specs,
            };
            this.logger.log(`商品详情查询成功: ID=${id}`);
            return result_1.ResultData.ok(result, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询商品详情失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询商品详情失败');
        }
    }
    async recordUserFootprint(userId, productId) {
        await this.footprintService.recordFootprint(userId, productId);
    }
    async update(id, updateProductDto) {
        try {
            this.logger.log(`更新商品: ID=${id}, Data=${JSON.stringify(updateProductDto)}`);
            const product = await this.productRepository.findOne({ where: { productId: id } });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            if (updateProductDto.categoryId) {
                const category = await this.categoryRepository.findOne({
                    where: { categoryId: updateProductDto.categoryId },
                });
                if (!category) {
                    return result_1.ResultData.fail(400, '商品分类不存在');
                }
                if (category.status !== 1) {
                    return result_1.ResultData.fail(400, '商品分类已禁用');
                }
            }
            const hasMultiSpecs = updateProductDto.hasMultiSpecs;
            const specList = updateProductDto.specList || updateProductDto.specs || [];
            delete updateProductDto.specList;
            delete updateProductDto.specs;
            if (specList.length > 0) {
                this.logger.log(`规格数据详情:`);
                specList.forEach((spec, index) => {
                    this.logger.log(`规格${index + 1}: specId=${spec.specId || '未设置'}, name=${spec.name}, price=${spec.price}`);
                });
            }
            const productToUpdate = { ...updateProductDto };
            await this.productRepository.update(id, productToUpdate);
            if (hasMultiSpecs !== undefined) {
                if (hasMultiSpecs === 1) {
                    const existingSpecs = await this.productSpecRepository.find({ where: { productId: id } });
                    if (specList.length > 0) {
                        const existingSpecMap = existingSpecs.reduce((map, spec) => {
                            map[spec.specId] = spec;
                            return map;
                        }, {});
                        const processedSpecIds = new Set();
                        let hasDefaultSpec = false;
                        this.logger.log(`更新商品ID=${id}的规格，现有规格数量=${existingSpecs.length}，新规格数量=${specList.length}`);
                        for (let i = 0; i < specList.length; i++) {
                            const spec = specList[i];
                            if (spec.isDefault === 1) {
                                hasDefaultSpec = true;
                            }
                            if (spec.specId) {
                                const specId = spec.specId;
                                processedSpecIds.add(specId);
                                if (existingSpecMap[specId]) {
                                    this.logger.log(`更新现有规格: SpecID=${specId}`);
                                    await this.productSpecRepository.update(specId, {
                                        name: spec.name || '规格' + (i + 1),
                                        price: spec.price || 0,
                                        value: spec.value || '默认',
                                        stock: spec.stock || 0,
                                        image: spec.image,
                                        status: spec.status || 1,
                                        isDefault: spec.isDefault || 0,
                                    });
                                }
                                else {
                                    this.logger.log(`规格ID ${specId} 不存在，创建新规格`);
                                    await this.addSpec(id, {
                                        name: spec.name || '规格' + (i + 1),
                                        price: spec.price || 0,
                                        value: spec.value || '默认',
                                        stock: spec.stock || 0,
                                        image: spec.image,
                                        status: spec.status || 1,
                                        isDefault: spec.isDefault || 0,
                                    });
                                }
                            }
                            else {
                                this.logger.log(`添加新规格，索引=${i}`);
                                await this.addSpec(id, {
                                    name: spec.name || '规格' + (i + 1),
                                    price: spec.price || 0,
                                    value: spec.value || '默认',
                                    stock: spec.stock || 0,
                                    image: spec.image,
                                    status: spec.status || 1,
                                    isDefault: spec.isDefault || 0,
                                });
                            }
                        }
                        for (const existingSpec of existingSpecs) {
                            if (!processedSpecIds.has(existingSpec.specId)) {
                                this.logger.log(`删除未包含的旧规格: SpecID=${existingSpec.specId}`);
                                await this.productSpecRepository.delete(existingSpec.specId);
                            }
                        }
                        if (!hasDefaultSpec && specList.length > 0) {
                            const newSpecs = await this.productSpecRepository.find({
                                where: { productId: id },
                                order: { createTime: 'ASC' },
                            });
                            if (newSpecs.length > 0) {
                                await this.setDefaultSpec(newSpecs[0].specId);
                            }
                        }
                    }
                }
                else if (hasMultiSpecs === 0) {
                    const specs = await this.productSpecRepository.find({ where: { productId: id } });
                    for (const spec of specs) {
                        await this.productSpecRepository.delete(spec.specId);
                    }
                }
            }
            const updatedProduct = await this.productRepository.findOne({
                where: { productId: id },
                relations: ['category'],
            });
            const specs = await this.productSpecRepository.find({
                where: { productId: id },
                order: { isDefault: 'DESC', createTime: 'ASC' },
            });
            const result = {
                ...updatedProduct,
                specs,
            };
            this.logger.log(`商品更新成功: ID=${id}`);
            return result_1.ResultData.ok(result, '更新成功');
        }
        catch (error) {
            this.logger.error(`更新商品失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '更新商品失败');
        }
    }
    async remove(id) {
        try {
            this.logger.log(`删除商品: ID=${id}`);
            const product = await this.productRepository.findOne({ where: { productId: id } });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            await this.productRepository.delete(id);
            this.logger.log(`商品删除成功: ID=${id}`);
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除商品失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '删除商品失败');
        }
    }
    async updateStatus(ids, status) {
        try {
            this.logger.log(`批量更新商品状态: IDs=${JSON.stringify(ids)}, Status=${status}`);
            await this.productRepository.update(ids, { status });
            this.logger.log(`商品状态更新成功: 共更新${ids.length}条记录`);
            return result_1.ResultData.ok(null, '状态更新成功');
        }
        catch (error) {
            this.logger.error(`批量更新商品状态失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '批量更新商品状态失败');
        }
    }
    async increaseSales(id, count) {
        try {
            this.logger.log(`增加商品销量: ID=${id}, Count=${count}`);
            const product = await this.productRepository.findOne({ where: { productId: id } });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            const newSalesCount = product.salesCount + count;
            await this.productRepository.update(id, { salesCount: newSalesCount });
            this.logger.log(`商品销量更新成功: ID=${id}, 新销量=${newSalesCount}`);
            return result_1.ResultData.ok({ newSalesCount }, '销量更新成功');
        }
        catch (error) {
            this.logger.error(`增加商品销量失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '增加商品销量失败');
        }
    }
    async addSpec(productId, createSpecDto) {
        try {
            this.logger.log(`添加商品规格: ProductID=${productId}, Data=${JSON.stringify(createSpecDto)}`);
            const product = await this.productRepository.findOne({
                where: { productId },
            });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            const spec = new product_spec_entity_1.ProductSpecEntity();
            spec.productId = productId;
            spec.name = createSpecDto.name;
            spec.price = createSpecDto.price;
            spec.value = createSpecDto.value;
            spec.stock = createSpecDto.stock;
            spec.image = createSpecDto.image || null;
            spec.status = createSpecDto.status || 1;
            spec.isDefault = createSpecDto.isDefault || 0;
            spec.groupBuyPrice = createSpecDto.groupBuyPrice || null;
            if (spec.isDefault === 1) {
                await this.clearDefaultSpec(productId);
            }
            await this.productRepository.update(productId, { hasMultiSpecs: 1 });
            const savedSpec = await this.productSpecRepository.save(spec);
            this.logger.log(`商品规格添加成功: ID=${savedSpec.specId}`);
            return result_1.ResultData.ok(savedSpec, '添加规格成功');
        }
        catch (error) {
            this.logger.error(`添加商品规格失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '添加商品规格失败');
        }
    }
    async findSpecs(productId) {
        try {
            this.logger.log(`查询商品规格列表: ProductID=${productId}`);
            const product = await this.productRepository.findOne({
                where: { productId },
            });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            const specs = await this.productSpecRepository.find({
                where: { productId },
                order: {
                    isDefault: 'DESC',
                    createTime: 'ASC',
                },
            });
            this.logger.log(`商品规格查询成功: ProductID=${productId}, Count=${specs.length}`);
            return result_1.ResultData.ok(specs, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询商品规格列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询商品规格列表失败');
        }
    }
    async updateSpec(specId, updateSpecDto) {
        try {
            this.logger.log(`更新商品规格: SpecID=${specId}, Data=${JSON.stringify(updateSpecDto)}`);
            const spec = await this.productSpecRepository.findOne({
                where: { specId },
            });
            if (!spec) {
                return result_1.ResultData.fail(404, '商品规格不存在');
            }
            if (updateSpecDto.isDefault === 1) {
                await this.clearDefaultSpec(spec.productId);
            }
            if (updateSpecDto.groupBuyPrice === undefined) {
                delete updateSpecDto.groupBuyPrice;
            }
            else if (updateSpecDto.groupBuyPrice === null) {
                updateSpecDto.groupBuyPrice = null;
            }
            await this.productSpecRepository.update(specId, updateSpecDto);
            const updatedSpec = await this.productSpecRepository.findOne({
                where: { specId },
            });
            this.logger.log(`商品规格更新成功: SpecID=${specId}`);
            return result_1.ResultData.ok(updatedSpec, '更新成功');
        }
        catch (error) {
            this.logger.error(`更新商品规格失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '更新商品规格失败');
        }
    }
    async removeSpec(specId) {
        try {
            this.logger.log(`删除商品规格: SpecID=${specId}`);
            const spec = await this.productSpecRepository.findOne({
                where: { specId },
            });
            if (!spec) {
                return result_1.ResultData.fail(404, '商品规格不存在');
            }
            const specCount = await this.productSpecRepository.count({
                where: { productId: spec.productId },
            });
            if (specCount <= 1) {
                return result_1.ResultData.fail(400, '商品至少需要保留一个规格');
            }
            if (spec.isDefault === 1) {
                const otherSpec = await this.productSpecRepository.findOne({
                    where: { productId: spec.productId, specId: (0, typeorm_2.Not)(specId) },
                });
                if (otherSpec) {
                    await this.productSpecRepository.update(otherSpec.specId, { isDefault: 1 });
                }
            }
            await this.productSpecRepository.delete(specId);
            const remainingSpecCount = await this.productSpecRepository.count({
                where: { productId: spec.productId },
            });
            if (remainingSpecCount === 0) {
                await this.productRepository.update(spec.productId, { hasMultiSpecs: 0 });
            }
            this.logger.log(`商品规格删除成功: SpecID=${specId}`);
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除商品规格失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '删除商品规格失败');
        }
    }
    async setDefaultSpec(specId) {
        try {
            this.logger.log(`设置默认规格: SpecID=${specId}`);
            const spec = await this.productSpecRepository.findOne({
                where: { specId },
            });
            if (!spec) {
                return result_1.ResultData.fail(404, '商品规格不存在');
            }
            await this.clearDefaultSpec(spec.productId);
            await this.productSpecRepository.update(specId, { isDefault: 1 });
            this.logger.log(`设置默认规格成功: SpecID=${specId}`);
            return result_1.ResultData.ok(null, '设置成功');
        }
        catch (error) {
            this.logger.error(`设置默认规格失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '设置默认规格失败');
        }
    }
    async clearDefaultSpec(productId) {
        await this.productSpecRepository.update({ productId, isDefault: 1 }, { isDefault: 0 });
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = ProductService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.ProductEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(category_entity_1.CategoryEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(upload_entity_1.SysUploadEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(product_spec_entity_1.ProductSpecEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        footprint_service_1.FootprintService])
], ProductService);
//# sourceMappingURL=product.service.js.map