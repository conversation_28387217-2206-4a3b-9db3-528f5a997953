"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.entityTem = void 0;
const Lodash = __importStar(require("lodash"));
const entityTem = (options) => {
    const { BusinessName, tableName, tableComment } = options;
    const contentTem = content(options);
    return `
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('${tableName}', {
    comment: '${tableComment}',
})
export class ${Lodash.upperFirst(BusinessName)}Entity {
${contentTem}
}
`;
};
exports.entityTem = entityTem;
const content = (options) => {
    const { columns } = options;
    let html = ``;
    columns.sort((a, b) => b.isPk - a.isPk);
    columns.forEach((column) => {
        const { javaType, javaField, isPk, columnType, columnComment, columnDefault } = column;
        const filed = convertToSnakeCase(javaField);
        const type = lowercaseFirstLetter(javaType);
        if (isPk == '1') {
            html += `\t@PrimaryGeneratedColumn({ type: '${columnType}', name: '${filed}', comment: '${columnComment}' })\n\tpublic ${javaField}: ${type};\n`;
        }
        else {
            html += `\n\t@Column({ type: '${columnType}', name: '${filed}', default: ${getColumnDefault(column)}, comment: '${columnComment}' })\n\tpublic ${javaField}: ${type};\n`;
        }
    });
    return html;
};
function convertToSnakeCase(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
}
function lowercaseFirstLetter(str) {
    if (str.length === 0 || str === 'Date') {
        return str;
    }
    return str.charAt(0).toLowerCase() + str.slice(1);
}
function getColumnDefault(column) {
    if (column.columnType === 'char') {
        return column.columnDefault == '1';
    }
    else if (column.columnType === 'varchar') {
        return "''";
    }
    else {
        return column.columnDefault;
    }
}
//# sourceMappingURL=entity.js.map