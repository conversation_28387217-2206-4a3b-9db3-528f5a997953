"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperlogController = void 0;
const common_1 = require("@nestjs/common");
const operlog_service_1 = require("./operlog.service");
const create_operlog_dto_1 = require("./dto/create-operlog.dto");
const update_operlog_dto_1 = require("./dto/update-operlog.dto");
const swagger_1 = require("@nestjs/swagger");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
const operlog_decorator_1 = require("../../../common/decorators/operlog.decorator");
const business_constant_1 = require("../../../common/constant/business.constant");
let OperlogController = class OperlogController {
    constructor(operlogService) {
        this.operlogService = operlogService;
    }
    create(createOperlogDto) {
        return this.operlogService.create(createOperlogDto);
    }
    removeAll() {
        return this.operlogService.removeAll();
    }
    findAll(query) {
        return this.operlogService.findAll(query);
    }
    findOne(id) {
        return this.operlogService.findOne(+id);
    }
    update(id, updateOperlogDto) {
        return this.operlogService.update(+id, updateOperlogDto);
    }
    remove(id) {
        return this.operlogService.remove(+id);
    }
};
exports.OperlogController = OperlogController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_operlog_dto_1.CreateOperlogDto]),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '登录日志-清除全部日志',
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:logininfor:remove'),
    (0, common_1.Delete)('/clean'),
    (0, operlog_decorator_1.Operlog)({ businessType: business_constant_1.BusinessType.CLEAN }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "removeAll", null);
__decorate([
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_operlog_dto_1.UpdateOperlogDto]),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OperlogController.prototype, "remove", null);
exports.OperlogController = OperlogController = __decorate([
    (0, common_1.Controller)('monitor/operlog'),
    __metadata("design:paramtypes", [operlog_service_1.OperlogService])
], OperlogController);
//# sourceMappingURL=operlog.controller.js.map