"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MainController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const main_service_1 = require("./main.service");
const index_1 = require("./dto/index");
const captcha_1 = require("../../common/utils/captcha");
const result_1 = require("../../common/utils/result");
const index_2 = require("../../common/utils/index");
const redis_service_1 = require("../common/redis/redis.service");
const index_3 = require("../../common/enum/index");
const config_service_1 = require("../system/config/config.service");
const common_decorator_1 = require("../../common/decorators/common.decorator");
const user_decorator_1 = require("../system/user/user.decorator");
let MainController = class MainController {
    constructor(mainService, redisService, configService) {
        this.mainService = mainService;
        this.redisService = redisService;
        this.configService = configService;
    }
    login(user, clientInfo) {
        return this.mainService.login(user, clientInfo);
    }
    async logout(user, clientInfo) {
        if (user?.token) {
            await this.redisService.del(`${index_3.CacheEnum.LOGIN_TOKEN_KEY}${user.token}`);
        }
        return this.mainService.logout(clientInfo);
    }
    register(user) {
        return this.mainService.register(user);
    }
    async registerUser() {
        const res = await this.configService.getConfigValue('sys.account.registerUser');
        const enable = res === 'true';
        return result_1.ResultData.ok(enable, '操作成功');
    }
    async captchaImage() {
        const enable = await this.configService.getConfigValue('sys.account.captchaEnabled');
        const captchaEnabled = enable === 'true';
        const data = {
            captchaEnabled,
            img: '',
            uuid: '',
        };
        try {
            if (captchaEnabled) {
                const captchaInfo = (0, captcha_1.createMath)();
                data.img = captchaInfo.data;
                data.uuid = (0, index_2.GenerateUUID)();
                await this.redisService.set(index_3.CacheEnum.CAPTCHA_CODE_KEY + data.uuid, captchaInfo.text.toLowerCase(), 1000 * 60 * 5);
            }
            return result_1.ResultData.ok(data, '操作成功');
        }
        catch (err) {
            return result_1.ResultData.fail(500, '生成验证码错误，请重试');
        }
    }
    async getInfo(user) {
        return {
            msg: '操作成功',
            code: 200,
            permissions: user.permissions,
            roles: user.roles,
            user: user.user,
        };
    }
    getRouters(user) {
        const userId = user.user.userId.toString();
        return this.mainService.getRouters(+userId);
    }
};
exports.MainController = MainController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户登录',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.LoginDto,
        required: true,
    }),
    (0, common_1.Post)('/login'),
    (0, common_1.HttpCode)(200),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_decorator_1.ClientInfo)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.LoginDto, Object]),
    __metadata("design:returntype", void 0)
], MainController.prototype, "login", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '退出登录',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.LoginDto,
        required: true,
    }),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, common_1.Post)('/logout'),
    (0, common_1.HttpCode)(200),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_decorator_1.ClientInfo)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MainController.prototype, "logout", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户注册',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.RegisterDto,
        required: true,
    }),
    (0, common_1.Post)('/register'),
    (0, common_1.HttpCode)(200),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.RegisterDto]),
    __metadata("design:returntype", void 0)
], MainController.prototype, "register", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '账号自助-是否开启用户注册功能',
    }),
    (0, common_1.Get)('/registerUser'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MainController.prototype, "registerUser", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取验证图片',
    }),
    (0, common_1.Get)('/captchaImage'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MainController.prototype, "captchaImage", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '用户信息',
    }),
    (0, common_1.Get)('/getInfo'),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MainController.prototype, "getInfo", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '路由信息',
    }),
    (0, common_1.Get)('/getRouters'),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MainController.prototype, "getRouters", null);
exports.MainController = MainController = __decorate([
    (0, swagger_1.ApiTags)('根目录'),
    (0, common_1.Controller)('/'),
    __metadata("design:paramtypes", [main_service_1.MainService,
        redis_service_1.RedisService,
        config_service_1.ConfigService])
], MainController);
//# sourceMappingURL=main.controller.js.map