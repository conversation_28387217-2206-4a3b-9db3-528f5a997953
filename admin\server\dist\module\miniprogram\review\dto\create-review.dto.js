"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateReviewDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateReviewDto {
    constructor() {
        this.isAnonymous = '0';
    }
}
exports.CreateReviewDto = CreateReviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    __metadata("design:type", Number)
], CreateReviewDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '商品ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNotEmpty)({ message: '商品ID不能为空' }),
    __metadata("design:type", Number)
], CreateReviewDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORD123456' }),
    (0, class_validator_1.IsString)({ message: '订单ID必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单ID不能为空' }),
    __metadata("design:type", String)
], CreateReviewDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格ID', required: false, example: 1 }),
    (0, class_validator_1.IsInt)({ message: '商品规格ID必须是整数' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateReviewDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评分 1-5', example: 5 }),
    (0, class_validator_1.IsInt)({ message: '评分必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '评分最小为1' }),
    (0, class_validator_1.Max)(5, { message: '评分最大为5' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNotEmpty)({ message: '评分不能为空' }),
    __metadata("design:type", Number)
], CreateReviewDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价内容', example: '水果非常新鲜，口感很好！' }),
    (0, class_validator_1.IsString)({ message: '评价内容必须是字符串' }),
    (0, class_validator_1.Length)(1, 1000, { message: '评价内容长度必须在1-1000个字符之间' }),
    (0, class_validator_1.IsNotEmpty)({ message: '评价内容不能为空' }),
    __metadata("design:type", String)
], CreateReviewDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价图片URL数组', required: false, example: ['http://example.com/image1.jpg'] }),
    (0, class_validator_1.IsArray)({ message: '评价图片必须是数组' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateReviewDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否匿名评价，0-否 1-是', example: '0' }),
    (0, class_validator_1.IsString)({ message: '匿名标志必须是字符串' }),
    (0, class_validator_1.IsIn)(['0', '1'], { message: '匿名标志必须是0或1' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateReviewDto.prototype, "isAnonymous", void 0);
//# sourceMappingURL=create-review.dto.js.map