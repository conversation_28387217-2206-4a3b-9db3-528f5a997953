"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const dict_service_1 = require("./dict.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let DictController = class DictController {
    constructor(dictService) {
        this.dictService = dictService;
    }
    createType(createDictTypeDto, req) {
        createDictTypeDto['createBy'] = req.user.userName;
        return this.dictService.createType(createDictTypeDto);
    }
    refreshCache() {
        return this.dictService.resetDictCache();
    }
    deleteType(ids) {
        const dictIds = ids.split(',').map((id) => +id);
        return this.dictService.deleteType(dictIds);
    }
    updateType(updateDictTypeDto) {
        return this.dictService.updateType(updateDictTypeDto);
    }
    findAllType(query) {
        return this.dictService.findAllType(query);
    }
    findOptionselect() {
        return this.dictService.findOptionselect();
    }
    findOneType(id) {
        return this.dictService.findOneType(+id);
    }
    createDictData(createDictDataDto, req) {
        createDictDataDto['createBy'] = req.user.userName;
        return this.dictService.createDictData(createDictDataDto);
    }
    deleteDictData(ids) {
        const dictIds = ids.split(',').map((id) => +id);
        return this.dictService.deleteDictData(dictIds);
    }
    updateDictData(updateDictDataDto) {
        return this.dictService.updateDictData(updateDictDataDto);
    }
    findAllData(query) {
        return this.dictService.findAllData(query);
    }
    findOneDictData(dictCode) {
        return this.dictService.findOneDictData(+dictCode);
    }
    findOneDataType(dictType) {
        return this.dictService.findOneDataType(dictType);
    }
    async export(res, body) {
        return this.dictService.export(res, body);
    }
    async exportData(res, body) {
        return this.dictService.exportData(res, body);
    }
};
exports.DictController = DictController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典类型-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateDictTypeDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:add'),
    (0, common_1.HttpCode)(200),
    (0, common_1.Post)('/type'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateDictTypeDto, Object]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "createType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-刷新缓存',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:remove'),
    (0, common_1.Delete)('/type/refreshCache'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DictController.prototype, "refreshCache", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典类型-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:remove'),
    (0, common_1.Delete)('/type/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "deleteType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典类型-修改',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:edit'),
    (0, common_1.Put)('/type'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateDictTypeDto]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "updateType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典类型-列表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:list'),
    (0, common_1.Get)('/type/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListDictType]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findAllType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '全部字典类型-下拉数据',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:query'),
    (0, common_1.Get)('/type/optionselect'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findOptionselect", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典类型-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:query'),
    (0, common_1.Get)('/type/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findOneType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-创建',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:add'),
    (0, common_1.HttpCode)(200),
    (0, common_1.Post)('/data'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateDictDataDto, Object]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "createDictData", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:remove'),
    (0, common_1.Delete)('/data/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "deleteDictData", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-修改',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:edit'),
    (0, common_1.Put)('/data'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateDictDataDto]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "updateDictData", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-列表',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:list'),
    (0, common_1.Get)('/data/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListDictData]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findAllData", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-详情',
    }),
    (0, common_1.Get)('/data/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findOneDictData", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '字典数据-类型-详情【走缓存】',
    }),
    (0, common_1.Get)('/data/type/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], DictController.prototype, "findOneDataType", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出字典组为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:export'),
    (0, common_1.Post)('/type/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListDictType]),
    __metadata("design:returntype", Promise)
], DictController.prototype, "export", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出字典内容为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:dict:export'),
    (0, common_1.Post)('/data/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListDictType]),
    __metadata("design:returntype", Promise)
], DictController.prototype, "exportData", null);
exports.DictController = DictController = __decorate([
    (0, swagger_1.ApiTags)('字典管理'),
    (0, common_1.Controller)('system/dict'),
    __metadata("design:paramtypes", [dict_service_1.DictService])
], DictController);
//# sourceMappingURL=dict.controller.js.map