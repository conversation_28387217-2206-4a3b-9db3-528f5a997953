export declare class PaymentRequestDto {
    orderId: string;
    paymentMethod: string;
    description?: string;
}
export declare class BalanceRechargeDto {
    amount: number;
    description?: string;
}
export declare class UserBalanceRechargeDto {
    userId: number;
    amount: number;
    remark?: string;
}
export declare class ResourceDto {
    algorithm: string;
    associated_data: string;
    ciphertext: string;
    nonce: string;
    original_type: string;
}
export declare class PaymentNotifyDto {
    id: string;
    create_time: string;
    resource_type: string;
    event_type: string;
    summary: string;
    resource: ResourceDto;
}
export declare class RefundRequestDto {
    paymentId: number;
    refundAmount: number;
    refundReason: string;
}
