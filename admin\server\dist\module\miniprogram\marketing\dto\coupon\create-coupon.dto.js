"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCouponDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const coupon_interface_1 = require("../../interfaces/coupon.interface");
class CreateCouponDto {
    constructor() {
        this.status = '1';
        this.conditionAmount = 0;
        this.goodsType = '1';
        this.totalCount = 0;
        this.perUserLimit = 1;
        this.distributeType = coupon_interface_1.CouponDistributeType.PUBLIC;
    }
}
exports.CreateCouponDto = CreateCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券名称', example: '新人专享满100减10元' }),
    (0, class_validator_1.IsNotEmpty)({ message: '优惠券名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '优惠券名称必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券类型 1满减券 2折扣券 3无门槛券', example: '1' }),
    (0, class_validator_1.IsNotEmpty)({ message: '优惠券类型不能为空' }),
    (0, class_validator_1.IsString)({ message: '优惠券类型必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券状态 0无效 1有效', example: '1', default: '1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '优惠券状态必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券描述', example: '仅限生鲜水果类商品使用', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '优惠券描述必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '满减条件金额(分)', example: 10000, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '满减条件金额必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '满减条件金额不能小于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "conditionAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠金额(分)，满减券和无门槛券必填', example: 1000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '优惠金额必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '优惠金额不能小于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣率(0-100)，折扣券必填', example: 80, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '折扣率必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '折扣率不能小于0' }),
    (0, class_validator_1.Max)(100, { message: '折扣率不能大于100' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "discountRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '可使用商品范围 1全部商品 2指定商品 3指定商品类别', example: '1', default: '1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '可使用商品范围必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "goodsType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID列表，当goodsType=2时必填', example: '1,2,3', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '商品ID列表必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "goodsIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品类别ID列表，当goodsType=3时必填', example: '1,2', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '商品类别ID列表必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "categoryIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期开始时间', example: '2024-07-01 00:00:00' }),
    (0, class_validator_1.IsNotEmpty)({ message: '有效期开始时间不能为空' }),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)({ message: '有效期开始时间格式不正确' }),
    __metadata("design:type", Date)
], CreateCouponDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有效期结束时间', example: '2024-07-31 23:59:59' }),
    (0, class_validator_1.IsNotEmpty)({ message: '有效期结束时间不能为空' }),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)({ message: '有效期结束时间格式不正确' }),
    __metadata("design:type", Date)
], CreateCouponDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券总数量，0表示无限制', example: 100, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '优惠券总数量必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '优惠券总数量不能小于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每人限领数量', example: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每人限领数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每人限领数量不能小于1' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "perUserLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注', example: '双11活动优惠券', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发放方式 1公开领取 2指定发放', example: '1', default: '1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '发放方式必须是字符串' }),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "distributeType", void 0);
//# sourceMappingURL=create-coupon.dto.js.map