import { BusinessType } from '../constant/business.constant';
export type OperlogConfig = Partial<{
    businessType?: (typeof BusinessType)[keyof Omit<typeof BusinessType, 'prototype'>];
}> | undefined;
export declare const Operlog: (logConfig?: OperlogConfig) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
