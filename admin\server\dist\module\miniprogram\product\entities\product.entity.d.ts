import { CategoryEntity } from '../../category/entities/category.entity';
import { ProductSpecEntity } from './product-spec.entity';
export declare class ProductEntity {
    productId: number;
    name: string;
    price: number;
    originPlace: string;
    images: string;
    description: string;
    details: string;
    categoryId: number;
    salesCount: number;
    status: number;
    hasMultiSpecs: number;
    createTime: Date;
    updateTime: Date;
    category: CategoryEntity;
    specs: ProductSpecEntity[];
}
