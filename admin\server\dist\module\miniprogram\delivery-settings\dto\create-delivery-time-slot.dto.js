"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDeliveryTimeSlotDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateDeliveryTimeSlotDto {
}
exports.CreateDeliveryTimeSlotDto = CreateDeliveryTimeSlotDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送时间段', example: '上午9:00-12:00' }),
    (0, class_validator_1.IsNotEmpty)({ message: '配送时间段不能为空' }),
    (0, class_validator_1.IsString)({ message: '配送时间段必须是字符串' }),
    (0, class_validator_1.Length)(2, 50, { message: '配送时间段长度为2-50个字符' }),
    __metadata("design:type", String)
], CreateDeliveryTimeSlotDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送时间', example: '10:00:00' }),
    (0, class_validator_1.IsNotEmpty)({ message: '配送时间不能为空' }),
    (0, class_validator_1.IsString)({ message: '配送时间必须是字符串' }),
    (0, class_validator_1.Matches)(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/, {
        message: '配送时间格式不正确，应为HH:MM:SS格式'
    }),
    __metadata("design:type", String)
], CreateDeliveryTimeSlotDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '排序必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(999, { message: '排序不能大于999' }),
    __metadata("design:type", Number)
], CreateDeliveryTimeSlotDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '启用状态必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '启用状态只能是0或1' }),
    (0, class_validator_1.Max)(1, { message: '启用状态只能是0或1' }),
    __metadata("design:type", Number)
], CreateDeliveryTimeSlotDto.prototype, "isActive", void 0);
//# sourceMappingURL=create-delivery-time-slot.dto.js.map