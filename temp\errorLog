[Nest] 6928  - 2025/08/04 15:10:52     LOG [OrderController] 配送员确认取货: OrderId=ORD20250731511854, DeliveryStaffId=undefined
[Nest] 6928  - 2025/08/04 15:10:52     LOG [OrderService] [DEBUG] 配送员确认取货: OrderId=ORD20250731511854, DeliveryStaffId=undefined
[Nest] 6928  - 2025/08/04 15:10:52     LOG [OrderService] [DEBUG] 订单退款: OrderId=ORD20250731511854, Reason=退款测试2 香蕉
[Nest] 6928  - 2025/08/04 15:10:52   ERROR [OrderService] [ERROR] 订单退款失败: 只有待发货或配送中的订单才能退款
HttpException: 只有待发货或配送中的订单才能退款
    at OrderService.refundOrder (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:1963:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2305:30)
    at async EntityManager.transaction (D:\workspace\初鲜果味小程序\admin\server\node_modules\typeorm\entity-manager\src\entity-manager\EntityManager.ts:156:28)
    at async OrderService.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2282:12)
    at async OrderController.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.controller.ts:315:14)
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-execution-context.js:46:28
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-proxy.js:9:17
[Nest] 6928  - 2025/08/04 15:10:52   ERROR [OrderService] [ERROR] 配送员确认取货失败: 只有待发货或配送中的订单才能退款
HttpException: 只有待发货或配送中的订单才能退款
    at OrderService.refundOrder (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:1963:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2305:30)
    at async EntityManager.transaction (D:\workspace\初鲜果味小程序\admin\server\node_modules\typeorm\entity-manager\src\entity-manager\EntityManager.ts:156:28)
    at async OrderService.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2282:12)
    at async OrderController.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.controller.ts:315:14)
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-execution-context.js:46:28
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-proxy.js:9:17
[Nest] 6928  - 2025/08/04 15:10:52   ERROR [OrderController] 配送员确认取货失败: 只有待发货或配送中的订单才能退款
HttpException: 只有待发货或配送中的订单才能退款
    at OrderService.refundOrder (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:1963:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2305:30)
    at async EntityManager.transaction (D:\workspace\初鲜果味小程序\admin\server\node_modules\typeorm\entity-manager\src\entity-manager\EntityManager.ts:156:28)
    at async OrderService.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.service.ts:2282:12)
    at async OrderController.confirmRefundPickup (D:\workspace\初鲜果味小程序\admin\server\src\module\miniprogram\order\order.controller.ts:315:14)
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-execution-context.js:46:28
    at async D:\workspace\初鲜果味小程序\admin\server\node_modules\@nestjs\core\router\router-proxy.js:9:17
[Nest] 6928  - 2025/08/04 15:11:10   ERROR [RedisModule] default: read ECONNRESET +17103ms
Error: read ECONNRESET