import { Response } from 'express';
import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { SysDictTypeEntity } from './entities/dict.type.entity';
import { SysDictDataEntity } from './entities/dict.data.entity';
import { CreateDictTypeDto, UpdateDictTypeDto, ListDictType, CreateDictDataDto, UpdateDictDataDto, ListDictData } from './dto/index';
import { RedisService } from 'src/module/common/redis/redis.service';
export declare class DictService {
    private readonly sysDictTypeEntityRep;
    private readonly sysDictDataEntityRep;
    private readonly redisService;
    constructor(sysDictTypeEntityRep: Repository<SysDictTypeEntity>, sysDictDataEntityRep: Repository<SysDictDataEntity>, redisService: RedisService);
    createType(CreateDictTypeDto: CreateDictTypeDto): Promise<ResultData>;
    deleteType(dictIds: number[]): Promise<ResultData>;
    updateType(updateDictTypeDto: UpdateDictTypeDto): Promise<ResultData>;
    findAllType(query: ListDictType): Promise<ResultData>;
    findOneType(dictId: number): Promise<ResultData>;
    findOptionselect(): Promise<ResultData>;
    createDictData(createDictDataDto: CreateDictDataDto): Promise<ResultData>;
    deleteDictData(dictIds: number[]): Promise<ResultData>;
    updateDictData(updateDictDataDto: UpdateDictDataDto): Promise<ResultData>;
    findAllData(query: ListDictData): Promise<ResultData>;
    findOneDataType(dictType: string): Promise<ResultData>;
    findOneDictData(dictCode: number): Promise<ResultData>;
    export(res: Response, body: ListDictType): Promise<void>;
    exportData(res: Response, body: ListDictType): Promise<void>;
    resetDictCache(): Promise<ResultData>;
    clearDictCache(): Promise<void>;
    loadingDictCache(): Promise<void>;
}
