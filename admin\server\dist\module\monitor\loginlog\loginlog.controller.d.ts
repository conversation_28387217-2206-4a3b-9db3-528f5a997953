import { Response } from 'express';
import { LoginlogService } from './loginlog.service';
import { ListLoginlogDto } from './dto/index';
export declare class LoginlogController {
    private readonly loginlogService;
    constructor(loginlogService: LoginlogService);
    findAll(query: ListLoginlogDto): Promise<import("../../../common/utils/result").ResultData>;
    removeAll(): Promise<import("../../../common/utils/result").ResultData>;
    remove(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListLoginlogDto): Promise<void>;
}
