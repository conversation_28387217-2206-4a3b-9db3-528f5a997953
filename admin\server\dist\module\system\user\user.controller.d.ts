import { UserService } from './user.service';
import { Response } from 'express';
import { UploadService } from 'src/module/upload/upload.service';
import { CreateUserDto, UpdateUserDto, ListUserDto, ChangeStatusDto, ResetPwdDto, UpdateProfileDto, UpdatePwdDto } from './dto/index';
import { ResultData } from 'src/common/utils/result';
import { UserDto, UserToolType } from 'src/module/system/user/user.decorator';
export declare class UserController {
    private readonly userService;
    private readonly uploadService;
    constructor(userService: UserService, uploadService: UploadService);
    profile(user: UserDto): ResultData;
    updateProfile(user: UserDto, updateProfileDto: UpdateProfileDto): Promise<ResultData>;
    avatar(avatarfile: Express.Multer.File, user: UserDto): Promise<ResultData>;
    updatePwd(user: UserDto, updatePwdDto: UpdatePwdDto): Promise<ResultData>;
    create(createUserDto: CreateUserDto, { injectCreate }: UserToolType): Promise<ResultData>;
    findAll(query: ListUserDto, user: UserDto): Promise<ResultData>;
    deptTree(): Promise<ResultData>;
    findPostAndRoleAll(): Promise<ResultData>;
    authRole(id: string): Promise<ResultData>;
    updateAuthRole(query: any): Promise<ResultData>;
    findOne(userId: string): Promise<ResultData>;
    changeStatus(changeStatusDto: ChangeStatusDto): Promise<ResultData>;
    update(updateUserDto: UpdateUserDto, user: UserDto): Promise<ResultData>;
    resetPwd(body: ResetPwdDto): Promise<ResultData>;
    remove(ids: string): Promise<ResultData>;
    export(res: Response, body: ListUserDto, user: UserDto): Promise<void>;
}
