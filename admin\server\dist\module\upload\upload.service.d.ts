import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { ResultData } from 'src/common/utils/result';
import { SysUploadEntity } from './entities/upload.entity';
import { ChunkFileDto, ChunkMergeFileDto } from './dto/index';
import COS from 'cos-nodejs-sdk-v5';
export declare class UploadService {
    private readonly sysUploadEntityRep;
    private config;
    private thunkDir;
    private cos;
    private isLocal;
    private logger;
    constructor(sysUploadEntityRep: Repository<SysUploadEntity>, config: ConfigService);
    singleFileUpload(file: Express.Multer.File): Promise<any>;
    getChunkUploadId(): Promise<ResultData>;
    chunkFileUpload(file: Express.Multer.File, body: ChunkFileDto): Promise<ResultData>;
    checkChunkFile(body: any): Promise<ResultData>;
    mkdirsSync(dirname: any): boolean;
    chunkMergeFile(body: ChunkMergeFileDto): Promise<ResultData>;
    thunkStreamMerge(sourceFilesDir: any, targetFile: any): Promise<unknown>;
    thunkStreamMergeProgress(fileList: any, fileWriteStream: any, sourceFilesDir: any, onResolve: any): void;
    saveFileLocal(file: Express.Multer.File): Promise<{
        fileName: string;
        newFileName: string;
        url: string;
    }>;
    getNewFileName(originalname: string): string;
    saveFileCos(targetDir: string, file: Express.Multer.File): Promise<{
        fileName: string;
        newFileName: string;
        url: string;
    }>;
    uploadCos(targetFile: string, buffer: COS.UploadBody): Promise<string>;
    getChunkUploadResult(uploadId: string): Promise<ResultData>;
    uploadLargeFileCos(sourceFile: string, targetFile: string): Promise<string>;
    cosHeadObject(targetFile: string): Promise<any>;
    getAuthorization(Key: string): Promise<ResultData>;
    deleteFile(fileUrl: string): Promise<boolean>;
}
