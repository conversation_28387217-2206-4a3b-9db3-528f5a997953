import { Response } from 'express';
import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { CreateConfigDto, UpdateConfigDto, ListConfigDto } from './dto/index';
import { SysConfigEntity } from './entities/config.entity';
import { RedisService } from 'src/module/common/redis/redis.service';
export declare class ConfigService {
    private readonly sysConfigEntityRep;
    private readonly redisService;
    constructor(sysConfigEntityRep: Repository<SysConfigEntity>, redisService: RedisService);
    create(createConfigDto: CreateConfigDto): Promise<ResultData>;
    findAll(query: ListConfigDto): Promise<ResultData>;
    findOne(configId: number): Promise<ResultData>;
    findOneByConfigKey(configKey: string): Promise<ResultData>;
    getConfigValue(configKey: string): Promise<string>;
    update(updateConfigDto: UpdateConfigDto): Promise<ResultData>;
    remove(configIds: number[]): Promise<ResultData>;
    export(res: Response, body: ListConfigDto): Promise<void>;
    resetConfigCache(): Promise<ResultData>;
    clearConfigCache(): Promise<void>;
    loadingConfigCache(): Promise<void>;
}
