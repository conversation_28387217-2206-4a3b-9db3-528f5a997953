"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildMenus = void 0;
const class_validator_1 = require("class-validator");
const Lodash = __importStar(require("lodash"));
const UserConstants = __importStar(require("../user/user.constant"));
const buildMenus = (arr) => {
    arr.sort((a, b) => a.parentId - b.parentId);
    const kData = {};
    const lData = [];
    arr.forEach((m) => {
        m = {
            ...m,
            id: m.menuId,
            parentId: m.parentId,
        };
        kData[m.id] = {
            ...m,
            id: m.id,
            parentId: m.parentId,
        };
        if (m.parentId === 0) {
            lData.push(kData[m.id]);
        }
        else {
            kData[m.parentId] = kData[m.parentId] || {};
            kData[m.parentId].children = kData[m.parentId].children || [];
            kData[m.parentId].children.push(kData[m.id]);
        }
    });
    return formatTreeNodeBuildMenus(lData);
};
exports.buildMenus = buildMenus;
const formatTreeNodeBuildMenus = (menus) => {
    return menus.map((menu) => {
        const router = {};
        router.hidden = menu.visible === '1';
        router.name = getRouteName(menu);
        router.path = getRouterPath(menu);
        router.component = getComponent(menu);
        router.query = menu.query;
        router.meta = setMeta(menu);
        if (menu.children && menu.children.length > 0 && menu.menuType === UserConstants.TYPE_DIR) {
            router.alwaysShow = true;
            router.redirect = 'noRedirect';
            router.children = formatTreeNodeBuildMenus(menu.children);
        }
        else if (isMenuFrame(menu)) {
            router.meta = null;
            const childrenList = [];
            const childrenRouter = {};
            childrenRouter.path = menu.path;
            childrenRouter.component = menu.component;
            childrenRouter.name = Lodash.capitalize(menu.path);
            childrenRouter.meta = setMeta(menu);
            childrenRouter.query = menu.query;
            childrenList.push(childrenRouter);
            router.children = childrenList;
        }
        else if (menu.parentId === 0 && isInnerLink(menu)) {
            router.meta = {
                name: menu.name,
                icon: menu.icon,
            };
            router.path = '/';
            const childrenList = [];
            const childrenRouter = {};
            childrenRouter.path = innerLinkReplaceEach(menu.path);
            childrenRouter.component = UserConstants.INNER_LINK;
            childrenRouter.name = Lodash.capitalize(menu.name);
            childrenRouter.meta = {
                name: menu.name,
                icon: menu.icon,
                path: menu.path,
            };
            childrenList.push(childrenRouter);
            router.children = childrenList;
        }
        return router;
    });
};
const setMeta = (menu) => {
    const meta = {
        title: menu.menuName,
        icon: menu.icon,
        noCache: menu.isCache === '1',
    };
    if ((0, class_validator_1.isURL)(menu.link)) {
        meta['link'] = menu.link;
    }
    return meta;
};
const getRouteName = (menu) => {
    let routerName = Lodash.capitalize(menu.path);
    if (isMenuFrame(menu)) {
        routerName = '';
    }
    return routerName;
};
const isMenuFrame = (menu) => {
    return menu.parentId === 0 && menu.menuType === UserConstants.TYPE_MENU && menu.isFrame === UserConstants.NO_FRAME;
};
const isInnerLink = (menu) => {
    return menu.isFrame === UserConstants.NO_FRAME && (0, class_validator_1.isURL)(menu.path);
};
const isParentView = (menu) => {
    return menu.parentId !== 0 && menu.menuType === UserConstants.TYPE_DIR;
};
const getComponent = (menu) => {
    let component = UserConstants.LAYOUT;
    if (menu.component && !isMenuFrame(menu)) {
        component = menu.component;
    }
    else if (!menu.component && menu.parentId !== 0 && isInnerLink(menu)) {
        component = UserConstants.INNER_LINK;
    }
    else if (!menu.component && isParentView(menu)) {
        component = UserConstants.PARENT_VIEW;
    }
    return component;
};
const innerLinkReplaceEach = (path) => {
    const replacements = [
        ['http://', ''],
        ['https://', ''],
        ['www.', ''],
        ['.', '/'],
        [':', '/'],
    ];
    for (const [oldValue, newValue] of replacements) {
        path = path.replace(new RegExp(oldValue, 'g'), newValue);
    }
    return path;
};
const getRouterPath = (menu) => {
    let routerPath = menu.path;
    if (menu.parentId !== 0 && isInnerLink(menu)) {
        routerPath = innerLinkReplaceEach(routerPath);
    }
    if (menu.parentId === 0 && menu.menuType === UserConstants.TYPE_DIR && menu.isFrame === UserConstants.NO_FRAME) {
        routerPath = '/' + menu.path;
    }
    else if (isMenuFrame(menu)) {
        routerPath = '/';
    }
    return routerPath;
};
//# sourceMappingURL=utils.js.map