import { DeptService } from './dept.service';
import { CreateDeptDto, UpdateDeptDto, ListDeptDto } from './dto/index';
export declare class DeptController {
    private readonly deptService;
    constructor(deptService: DeptService);
    create(createDeptDto: CreateDeptDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListDeptDto): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): Promise<import("../../../common/utils/result").ResultData>;
    findListExclude(id: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updateDeptDto: UpdateDeptDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(id: string): Promise<import("../../../common/utils/result").ResultData>;
}
