import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { SysDeptEntity } from './entities/dept.entity';
import { CreateDeptDto, UpdateDeptDto, ListDeptDto } from './dto/index';
import { DataScopeEnum } from 'src/common/enum/index';
export declare class DeptService {
    private readonly sysDeptEntityRep;
    constructor(sysDeptEntityRep: Repository<SysDeptEntity>);
    create(createDeptDto: CreateDeptDto): Promise<ResultData>;
    findAll(query: ListDeptDto): Promise<ResultData>;
    findOne(deptId: number): Promise<ResultData>;
    findDeptIdsByDataScope(deptId: number, dataScope: DataScopeEnum): Promise<number[]>;
    private addQueryForDeptDataScope;
    private addQueryForDeptAndChildDataScope;
    findListExclude(id: number): Promise<ResultData>;
    update(updateDeptDto: UpdateDeptDto): Promise<ResultData>;
    remove(deptId: number): Promise<ResultData>;
    deptTree(): Promise<any[]>;
}
