import { PagingDto } from 'src/common/dto/index';
import { genTableCloumnUpdate } from './create-genTableCloumn-dto';
export declare class CreateGenTableDto {
    tableName: string;
    tableComment: string;
    className: string;
    packageName: string;
    moduleName: string;
    businessName: string;
    functionName: string;
    functionAuthor: string;
    createBy: string;
}
export declare class UpdateGenTableDto extends CreateGenTableDto {
    tableId: number;
}
export declare class GenDbTableList extends PagingDto {
    tableName?: string;
    tableComment?: string;
}
export declare class TableName {
    tableNames: string;
}
export declare class TableId {
    tableIds: string;
}
export declare class GenTableList extends PagingDto {
    tableNames?: string;
    tableComment?: string;
}
export declare class GenTableUpdate {
    tableId: number;
    tableName?: string;
    tableComment?: string;
    className?: string;
    functionAuthor?: string;
    remark?: string;
    tplCategory?: string;
    packageName?: string;
    moduleName?: string;
    businessName?: string;
    functionName?: string;
    genType?: string;
    columns?: genTableCloumnUpdate[];
    tplWebType?: string;
}
