"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderDetailDto = exports.OrderItemDetailDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class OrderItemDetailDto {
}
exports.OrderItemDetailDto = OrderItemDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单商品ID' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "itemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称' }),
    __metadata("design:type", String)
], OrderItemDetailDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品图片' }),
    __metadata("design:type", String)
], OrderItemDetailDto.prototype, "productImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品单价' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品小计' }),
    __metadata("design:type", Number)
], OrderItemDetailDto.prototype, "totalPrice", void 0);
class OrderDetailDto {
}
exports.OrderDetailDto = OrderDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货地址ID' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "addressId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '拼团ID' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "userGroupBuyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为拼团发起人' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "isGroupInitiator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单类型' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单总金额' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "totalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠金额' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '实付金额' }),
    __metadata("design:type", Number)
], OrderDetailDto.prototype, "finalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送方式' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "deliveryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '预约配送时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "deliveryTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单状态' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发货时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "shipmentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '完成时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "completionTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '取消原因' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "cancelReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人姓名' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "receiverName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货人电话' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "receiverPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收货地址' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "receiverAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], OrderDetailDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单备注' }),
    __metadata("design:type", String)
], OrderDetailDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单商品列表', type: [OrderItemDetailDto] }),
    __metadata("design:type", Array)
], OrderDetailDto.prototype, "orderItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息' }),
    __metadata("design:type", Object)
], OrderDetailDto.prototype, "user", void 0);
//# sourceMappingURL=order-detail.dto.js.map