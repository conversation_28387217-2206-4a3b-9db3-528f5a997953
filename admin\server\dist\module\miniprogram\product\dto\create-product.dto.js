"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProductDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const product_spec_dto_1 = require("./product-spec.dto");
class CreateProductDto {
    constructor() {
        this.hasMultiSpecs = 1;
    }
}
exports.CreateProductDto = CreateProductDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称', example: '新鲜苹果' }),
    (0, class_validator_1.IsNotEmpty)({ message: '商品名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '商品名称必须是字符串' }),
    __metadata("design:type", String)
], CreateProductDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品价格', example: 15.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '商品价格不能为空' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '商品价格必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '商品价格不能为负数' }),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', required: false, example: '新鲜甜美的苹果' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '商品描述必须是字符串' }),
    __metadata("design:type", String)
], CreateProductDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品详情', required: false, example: '<p>这是一个富文本</p>' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '商品详情必须是字符串' }),
    __metadata("design:type", String)
], CreateProductDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品图片URL列表，逗号分隔', required: false, example: 'https://example.com/image1.jpg,https://example.com/image2.jpg' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '商品图片URL列表必须是字符串' }),
    __metadata("design:type", String)
], CreateProductDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类ID', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: '分类ID不能为空' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '分类ID必须是整数' }),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1上架，0下架', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否多规格商品：1是，0否', required: false, example: 1, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '多规格标志必须是0或1' }),
    __metadata("design:type", Number)
], CreateProductDto.prototype, "hasMultiSpecs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格列表', required: false, type: [product_spec_dto_1.CreateProductSpecDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: '商品规格必须是数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => product_spec_dto_1.CreateProductSpecDto),
    __metadata("design:type", Array)
], CreateProductDto.prototype, "specs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格列表（后端命名）', required: false, type: [product_spec_dto_1.CreateProductSpecDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: '商品规格必须是数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => product_spec_dto_1.CreateProductSpecDto),
    __metadata("design:type", Array)
], CreateProductDto.prototype, "specList", void 0);
//# sourceMappingURL=create-product.dto.js.map