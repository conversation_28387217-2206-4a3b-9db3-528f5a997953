import { Repository } from 'typeorm';
import { Footprint } from './entities/footprint.entity';
import { ProductEntity } from '../product/entities/product.entity';
import { ResultData } from '../../../common/utils/result';
import { SysUploadEntity } from '../../upload/entities/upload.entity';
export declare class FootprintService {
    private readonly footprintRepository;
    private readonly productRepository;
    private readonly uploadRepository;
    private readonly logger;
    constructor(footprintRepository: Repository<Footprint>, productRepository: Repository<ProductEntity>, uploadRepository: Repository<SysUploadEntity>);
    recordFootprint(userId: number, productId: number): Promise<void>;
    private processProductImages;
    findUserFootprints(userId: number, pageNum?: number, pageSize?: number): Promise<ResultData>;
    removeFootprints(userId: number, footprintIds: number[]): Promise<ResultData>;
    clearUserFootprints(userId: number): Promise<ResultData>;
}
