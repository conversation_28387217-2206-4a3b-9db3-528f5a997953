"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProductController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const product_service_1 = require("./product.service");
const create_product_dto_1 = require("./dto/create-product.dto");
const update_product_dto_1 = require("./dto/update-product.dto");
const product_query_dto_1 = require("./dto/product-query.dto");
const auth_guard_1 = require("../../../common/guards/auth.guard");
const user_decorator_1 = require("../../system/user/user.decorator");
const product_spec_dto_1 = require("./dto/product-spec.dto");
let ProductController = ProductController_1 = class ProductController {
    constructor(productService) {
        this.productService = productService;
        this.logger = new common_1.Logger(ProductController_1.name);
    }
    async create(createProductDto) {
        this.logger.log(`创建商品请求: ${JSON.stringify(createProductDto)}`);
        return await this.productService.create(createProductDto);
    }
    async findAll(queryDto) {
        this.logger.log(`查询商品列表请求: ${JSON.stringify(queryDto)}`);
        return await this.productService.findAll(queryDto);
    }
    async findEnabled(queryDto) {
        this.logger.log(`查询上架商品列表请求: ${JSON.stringify(queryDto)}`);
        return await this.productService.findEnabled(queryDto);
    }
    async findOne(id, userId) {
        this.logger.log(`查询商品详情请求: ID=${id}, UserId=${userId || '未登录'}`);
        return await this.productService.findOne(id, userId);
    }
    async update(id, updateProductDto) {
        this.logger.log(`更新商品请求: ID=${id}, Data=${JSON.stringify(updateProductDto)}`);
        return await this.productService.update(id, updateProductDto);
    }
    async remove(id) {
        this.logger.log(`删除商品请求: ID=${id}`);
        return await this.productService.remove(id);
    }
    async updateStatus(body) {
        this.logger.log(`批量更新商品状态请求: ${JSON.stringify(body)}`);
        const { ids, status } = body;
        return await this.productService.updateStatus(ids, status);
    }
    async increaseSales(id, body) {
        this.logger.log(`增加商品销量请求: ID=${id}, Count=${body.count}`);
        return await this.productService.increaseSales(id, body.count);
    }
    async addSpec(id, createSpecDto) {
        this.logger.log(`添加商品规格请求: ProductID=${id}, Data=${JSON.stringify(createSpecDto)}`);
        return await this.productService.addSpec(id, createSpecDto);
    }
    async findSpecs(id) {
        this.logger.log(`查询商品规格列表请求: ProductID=${id}`);
        return await this.productService.findSpecs(id);
    }
    async updateSpec(specId, updateSpecDto) {
        this.logger.log(`更新商品规格请求: SpecID=${specId}, Data=${JSON.stringify(updateSpecDto)}`);
        return await this.productService.updateSpec(specId, updateSpecDto);
    }
    async removeSpec(specId) {
        this.logger.log(`删除商品规格请求: SpecID=${specId}`);
        return await this.productService.removeSpec(specId);
    }
    async setDefaultSpec(specId) {
        this.logger.log(`设置默认规格请求: SpecID=${specId}`);
        return await this.productService.setDefaultSpec(specId);
    }
};
exports.ProductController = ProductController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建商品' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_product_dto_1.CreateProductDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询商品列表（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [product_query_dto_1.ProductQueryDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('enabled'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取上架商品列表（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [product_query_dto_1.ProductQueryDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "findEnabled", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '查询商品详情（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新商品' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_product_dto_1.UpdateProductDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '删除商品' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "remove", null);
__decorate([
    (0, common_1.Put)('status/batch'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '批量更新商品状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)(':id/sales'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '增加商品销量' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "increaseSales", null);
__decorate([
    (0, common_1.Post)(':id/specs'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '添加商品规格' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '添加成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, product_spec_dto_1.CreateProductSpecDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "addSpec", null);
__decorate([
    (0, common_1.Get)(':id/specs'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取商品规格列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "findSpecs", null);
__decorate([
    (0, common_1.Put)('specs/:specId'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新商品规格' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('specId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, product_spec_dto_1.UpdateProductSpecDto]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "updateSpec", null);
__decorate([
    (0, common_1.Delete)('specs/:specId'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '删除商品规格' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('specId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "removeSpec", null);
__decorate([
    (0, common_1.Put)('specs/:specId/default'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '设置默认规格' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功' }),
    __param(0, (0, common_1.Param)('specId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProductController.prototype, "setDefaultSpec", null);
exports.ProductController = ProductController = ProductController_1 = __decorate([
    (0, swagger_1.ApiTags)('商品管理'),
    (0, common_1.Controller)('miniprogram/product'),
    __metadata("design:paramtypes", [product_service_1.ProductService])
], ProductController);
//# sourceMappingURL=product.controller.js.map