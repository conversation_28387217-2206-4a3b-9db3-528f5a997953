"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointRecord = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
let PointRecord = class PointRecord extends base_1.BaseEntity {
};
exports.PointRecord = PointRecord;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分记录ID', example: 1 }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'record_id', type: 'int', comment: '积分记录ID主键' }),
    __metadata("design:type", Number)
], PointRecord.prototype, "recordId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1001 }),
    (0, typeorm_1.Index)('idx_user_id'),
    (0, typeorm_1.Column)({ name: 'user_id', type: 'int', comment: '用户ID外键' }),
    __metadata("design:type", Number)
], PointRecord.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分变化', example: 100 }),
    (0, typeorm_1.Column)({ name: 'points', type: 'int', comment: '积分变化值(正数为增加，负数为减少)' }),
    __metadata("design:type", Number)
], PointRecord.prototype, "points", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分类型', example: 1 }),
    (0, typeorm_1.Index)('idx_type'),
    (0, typeorm_1.Column)({ name: 'type', type: 'tinyint', width: 1, comment: '类型 1注册2下单3评价4活动5过期' }),
    __metadata("design:type", Number)
], PointRecord.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '相关订单ID', example: '202407050001' }),
    (0, typeorm_1.Column)({ name: 'order_id', type: 'varchar', length: 32, nullable: true, comment: '相关订单ID' }),
    __metadata("design:type", String)
], PointRecord.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分说明', example: '首次下单奖励' }),
    (0, typeorm_1.Column)({ name: 'description', type: 'varchar', length: 255, comment: '积分说明' }),
    __metadata("design:type", String)
], PointRecord.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', example: '2025-07-01 00:00:00' }),
    (0, typeorm_1.Column)({ name: 'expire_time', type: 'datetime', nullable: true, comment: '积分过期时间' }),
    __metadata("design:type", Date)
], PointRecord.prototype, "expireTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否已使用', example: 0 }),
    (0, typeorm_1.Column)({ name: 'is_used', type: 'tinyint', width: 1, default: 0, comment: '是否已使用 0未使用1已使用' }),
    __metadata("design:type", Number)
], PointRecord.prototype, "isUsed", void 0);
exports.PointRecord = PointRecord = __decorate([
    (0, typeorm_1.Entity)({ name: 'point_records' })
], PointRecord);
//# sourceMappingURL=point-record.entity.js.map