"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysDictTypeEntity = exports.Dict = void 0;
class Dict {
}
exports.Dict = Dict;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let SysDictTypeEntity = class SysDictTypeEntity extends base_1.BaseEntity {
};
exports.SysDictTypeEntity = SysDictTypeEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '字典主键' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'dict_id', comment: '字典主键' }),
    __metadata("design:type", Number)
], SysDictTypeEntity.prototype, "dictId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_name', length: 100, comment: '字典名称' }),
    __metadata("design:type", String)
], SysDictTypeEntity.prototype, "dictName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_type', unique: true, length: 100, comment: '字典类型' }),
    __metadata("design:type", String)
], SysDictTypeEntity.prototype, "dictType", void 0);
exports.SysDictTypeEntity = SysDictTypeEntity = __decorate([
    (0, typeorm_1.Entity)('sys_dict_type', {
        comment: '字典类型表',
    })
], SysDictTypeEntity);
//# sourceMappingURL=dict.type.entity.js.map