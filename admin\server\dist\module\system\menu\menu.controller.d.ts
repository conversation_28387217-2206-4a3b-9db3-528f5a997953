import { MenuService } from './menu.service';
import { CreateMenuDto, UpdateMenuDto, ListDeptDto } from './dto/index';
export declare class MenuController {
    private readonly menuService;
    constructor(menuService: MenuService);
    create(createMenuDto: CreateMenuDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListDeptDto): Promise<import("../../../common/utils/result").ResultData>;
    treeSelect(): Promise<import("../../../common/utils/result").ResultData>;
    roleMenuTreeselect(menuId: string): Promise<any>;
    findOne(menuId: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updateMenuDto: UpdateMenuDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(menuId: string): Promise<import("../../../common/utils/result").ResultData>;
}
