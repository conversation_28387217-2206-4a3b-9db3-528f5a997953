"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Footprint = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const product_entity_1 = require("../../product/entities/product.entity");
const user_entity_1 = require("../../user/entities/user.entity");
let Footprint = class Footprint extends base_1.BaseEntity {
};
exports.Footprint = Footprint;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '足迹ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'footprint_id', comment: '足迹ID' }),
    __metadata("design:type", Number)
], Footprint.prototype, "footprintId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ name: 'user_id', comment: '用户ID' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], Footprint.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    (0, typeorm_1.Column)({ name: 'product_id', comment: '商品ID' }),
    (0, typeorm_1.Index)('idx_product_id'),
    __metadata("design:type", Number)
], Footprint.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户', type: () => user_entity_1.MiniprogramUser }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id', referencedColumnName: 'userId' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], Footprint.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品', type: () => product_entity_1.ProductEntity }),
    (0, typeorm_1.ManyToOne)(() => product_entity_1.ProductEntity, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id', referencedColumnName: 'productId' }),
    __metadata("design:type", product_entity_1.ProductEntity)
], Footprint.prototype, "product", void 0);
exports.Footprint = Footprint = __decorate([
    (0, typeorm_1.Entity)('user_footprint'),
    (0, typeorm_1.Index)('idx_user_product', ['userId', 'productId'], { unique: true })
], Footprint);
//# sourceMappingURL=footprint.entity.js.map