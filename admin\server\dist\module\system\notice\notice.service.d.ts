import { Repository } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { SysNoticeEntity } from './entities/notice.entity';
import { CreateNoticeDto, UpdateNoticeDto, ListNoticeDto } from './dto/index';
export declare class NoticeService {
    private readonly sysNoticeEntityRep;
    constructor(sysNoticeEntityRep: Repository<SysNoticeEntity>);
    create(createNoticeDto: CreateNoticeDto): Promise<ResultData>;
    findAll(query: ListNoticeDto): Promise<ResultData>;
    findOne(noticeId: number): Promise<ResultData>;
    update(updateNoticeDto: UpdateNoticeDto): Promise<ResultData>;
    remove(noticeIds: number[]): Promise<ResultData>;
}
