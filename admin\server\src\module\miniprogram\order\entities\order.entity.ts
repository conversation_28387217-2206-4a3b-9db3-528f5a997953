import { <PERSON><PERSON>ty, Column, PrimaryColumn, Index, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../../../common/entities/base';
import { OrderItemEntity } from './order-item.entity';
import { UserAddress } from '../../address/entities/address.entity';
import { MiniprogramUser } from '../../user/entities/user.entity';

@Entity('orders', { comment: '订单信息表' })
@Index('idx_user_id', ['userId'])
@Index('idx_status', ['status'])
@Index('idx_create_time', ['createTime'])
@Index('idx_order_type', ['orderType'])
export class OrderEntity extends BaseEntity {
  @ApiProperty({ description: '订单ID' })
  @PrimaryColumn({ type: 'varchar', name: 'order_id', length: 32, comment: '订单ID主键' })
  public orderId: string;

  @ApiProperty({ description: '用户ID' })
  @Column({ type: 'int', name: 'user_id', comment: '用户ID外键' })
  @Index('idx_user_id')
  public userId: number;

  @ApiProperty({ description: '收货地址ID' })
  @Column({ type: 'int', name: 'address_id', comment: '收货地址ID外键' })
  public addressId: number;

  @ApiProperty({ description: '用户拼团ID', required: false })
  @Column({
    type: 'varchar',
    name: 'user_group_buy_id',
    length: 32,
    nullable: true,
    comment: '拼团组ID，用于标识具体的拼团组，所有参与同一拼团的订单共享相同的ID',
  })
  public userGroupBuyId: string | null;

  @ApiProperty({ description: '是否为拼团发起人', required: false })
  @Column({
    type: 'tinyint',
    name: 'is_group_initiator',
    default: 0,
    comment: '是否为拼团发起人：0否1是',
  })
  public isGroupInitiator: number;

  @ApiProperty({ description: '订单类型', example: '1' })
  @Column({
    type: 'char',
    name: 'order_type',
    length: 1,
    default: '1',
    comment: '订单类型：1普通订单2团购订单',
  })
  @Index('idx_order_type')
  public orderType: string;

  @ApiProperty({ description: '订单总金额', example: 99.99 })
  @Column({
    type: 'decimal',
    name: 'total_amount',
    precision: 10,
    scale: 2,
    comment: '订单总金额',
  })
  public totalAmount: number;

  @ApiProperty({ description: '优惠金额', example: 10.0 })
  @Column({
    type: 'decimal',
    name: 'discount_amount',
    precision: 10,
    scale: 2,
    default: 0,
    comment: '优惠金额',
  })
  public discountAmount: number;

  @ApiProperty({ description: '实付金额', example: 89.99 })
  @Column({
    type: 'decimal',
    name: 'final_amount',
    precision: 10,
    scale: 2,
    comment: '实付金额',
  })
  public finalAmount: number;

  @ApiProperty({ description: '配送方式', example: '1' })
  @Column({
    type: 'char',
    name: 'delivery_type',
    length: 1,
    default: '1',
    comment: '配送方式：1配送2自提',
  })
  public deliveryType: string;

  @ApiProperty({ description: '预约配送时间', required: false })
  @Column({
    type: 'datetime',
    name: 'delivery_time',
    nullable: true,
    comment: '预约配送时间',
  })
  public deliveryTime: Date | null;

  @ApiProperty({ description: '订单状态', example: '1' })
  @Column({
    type: 'char',
    name: 'status',
    length: 1,
    default: '1',
    comment: '订单状态：1待支付 2待发货 3配送中 4已完成 5已取消(含所有退款情况) 6团购失败已退款 7退款中(已完成订单等配送员取货)',
  })
  @Index('idx_status')
  public status: string;

  @ApiProperty({ description: '评价状态', example: '0' })
  @Column({
    type: 'char',
    name: 'review_status',
    length: 1,
    default: '0',
    comment: '评价状态：0未评价1已评价',
  })
  public reviewStatus: string;

  @ApiProperty({ description: '支付时间', required: false })
  @Column({
    type: 'datetime',
    name: 'payment_time',
    nullable: true,
    comment: '支付时间',
  })
  public paymentTime: Date | null;

  @ApiProperty({ description: '发货时间', required: false })
  @Column({
    type: 'datetime',
    name: 'shipment_time',
    nullable: true,
    comment: '发货时间',
  })
  public shipmentTime: Date | null;

  @ApiProperty({ description: '完成时间', required: false })
  @Column({
    type: 'datetime',
    name: 'completion_time',
    nullable: true,
    comment: '完成时间',
  })
  public completionTime: Date | null;

  @ApiProperty({ description: '取消原因', required: false })
  @Column({
    type: 'varchar',
    name: 'cancel_reason',
    length: 200,
    nullable: true,
    comment: '取消原因',
  })
  public cancelReason: string | null;

  @ApiProperty({ description: '收货人姓名' })
  @Column({ type: 'varchar', name: 'receiver_name', length: 50, comment: '收货人姓名' })
  public receiverName: string;

  @ApiProperty({ description: '收货人电话' })
  @Column({ type: 'varchar', name: 'receiver_phone', length: 20, comment: '收货人电话' })
  public receiverPhone: string;

  @ApiProperty({ description: '收货地址' })
  @Column({ type: 'varchar', name: 'receiver_address', length: 500, comment: '收货地址' })
  public receiverAddress: string;

  @ApiProperty({ description: '退款原因', required: false })
  @Column({
    type: 'varchar',
    name: 'refund_reason',
    length: 500,
    nullable: true,
    comment: '退款原因',
  })
  public refundReason: string | null;

  @ApiProperty({ description: '退款完成时间', required: false })
  @Column({
    type: 'datetime',
    name: 'refund_time',
    nullable: true,
    comment: '退款完成时间',
  })
  public refundTime: Date | null;

  @ApiProperty({ description: '退款类型', required: false })
  @Column({
    type: 'char',
    name: 'refund_type',
    length: 1,
    nullable: true,
    comment: '退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动',
  })
  public refundType: string | null;

  // 关联用户表 - 移除外键约束
  @ManyToOne(() => MiniprogramUser, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'user_id', referencedColumnName: 'userId' })
  public user?: MiniprogramUser;

  // 关联地址表 - 移除外键约束
  @ManyToOne(() => UserAddress, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'addressId' })
  public address?: UserAddress;

  // 关联订单商品表 - 移除外键约束
  @OneToMany(() => OrderItemEntity, (orderItem) => orderItem.order, { cascade: true, createForeignKeyConstraints: false })
  public orderItems?: OrderItemEntity[];
}
