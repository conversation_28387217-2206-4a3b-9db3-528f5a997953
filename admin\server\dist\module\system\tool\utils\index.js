"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StringUtils = void 0;
exports.arraysContains = arraysContains;
exports.getColumnLength = getColumnLength;
exports.convertToCamelCase = convertToCamelCase;
exports.capitalize = capitalize;
function arraysContains(array, value) {
    return array.includes(value);
}
function getColumnLength(columnType) {
    const match = columnType.match(/\((\d+)\)/);
    return match ? parseInt(match[1], 10) : 0;
}
class StringUtils {
    static toCamelCase(str) {
        return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
    }
    static toPascalCase(str) {
        return str[0].toUpperCase() + this.toCamelCase(str).slice(1);
    }
}
exports.StringUtils = StringUtils;
function convertToCamelCase(str) {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
//# sourceMappingURL=index.js.map