import { OperlogService } from './operlog.service';
import { CreateOperlogDto } from './dto/create-operlog.dto';
import { UpdateOperlogDto } from './dto/update-operlog.dto';
export declare class OperlogController {
    private readonly operlogService;
    constructor(operlogService: OperlogService);
    create(createOperlogDto: CreateOperlogDto): string;
    removeAll(): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: any): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): string;
    update(id: string, updateOperlogDto: UpdateOperlogDto): string;
    remove(id: string): string;
}
