"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobLog = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let JobLog = class JobLog {
};
exports.JobLog = JobLog;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务日志ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'job_log_id', comment: '任务日志ID' }),
    __metadata("design:type", Number)
], JobLog.prototype, "jobLogId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称' }),
    (0, typeorm_1.Column)({ name: 'job_name', length: 64, comment: '任务名称' }),
    __metadata("design:type", String)
], JobLog.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务组名' }),
    (0, typeorm_1.Column)({ name: 'job_group', length: 64, comment: '任务组名' }),
    __metadata("design:type", String)
], JobLog.prototype, "jobGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '调用目标字符串' }),
    (0, typeorm_1.Column)({ name: 'invoke_target', length: 500, comment: '调用目标字符串' }),
    __metadata("design:type", String)
], JobLog.prototype, "invokeTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '日志信息' }),
    (0, typeorm_1.Column)({ name: 'job_message', length: 500, nullable: true, comment: '日志信息' }),
    __metadata("design:type", String)
], JobLog.prototype, "jobMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '执行状态（0正常 1失败）' }),
    (0, typeorm_1.Column)({ name: 'status', type: 'char', length: 1, default: '0', comment: '执行状态（0正常 1失败）' }),
    __metadata("design:type", String)
], JobLog.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '异常信息' }),
    (0, typeorm_1.Column)({ name: 'exception_info', length: 2000, nullable: true, comment: '异常信息' }),
    __metadata("design:type", String)
], JobLog.prototype, "exceptionInfo", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time', comment: '创建时间' }),
    __metadata("design:type", Date)
], JobLog.prototype, "createTime", void 0);
exports.JobLog = JobLog = __decorate([
    (0, typeorm_1.Entity)('sys_job_log', {
        comment: '任务调度日志表',
    })
], JobLog);
//# sourceMappingURL=job-log.entity.js.map