"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysDictDataEntity = exports.Dict = void 0;
class Dict {
}
exports.Dict = Dict;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let SysDictDataEntity = class SysDictDataEntity extends base_1.BaseEntity {
};
exports.SysDictDataEntity = SysDictDataEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '字典主键' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'dict_code', comment: '字典主键' }),
    __metadata("design:type", Number)
], SysDictDataEntity.prototype, "dictCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'dict_sort', default: 0, comment: '字典排序' }),
    __metadata("design:type", Number)
], SysDictDataEntity.prototype, "dictSort", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_label', length: 100, comment: '字典标签' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "dictLabel", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_value', length: 100, comment: '字典键值' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "dictValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dict_type', length: 100, comment: '字典类型' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "dictType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'css_class', length: 100, default: '', comment: '样式属性' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "cssClass", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'list_class', length: 100, comment: '表格回显样式' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "listClass", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'is_default', length: 1, default: 'N', comment: '是否默认' }),
    __metadata("design:type", String)
], SysDictDataEntity.prototype, "isDefault", void 0);
exports.SysDictDataEntity = SysDictDataEntity = __decorate([
    (0, typeorm_1.Entity)('sys_dict_data', {
        comment: '字典数据表',
    })
], SysDictDataEntity);
//# sourceMappingURL=dict.data.entity.js.map