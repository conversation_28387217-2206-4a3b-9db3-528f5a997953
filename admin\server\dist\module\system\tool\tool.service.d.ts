import { DataSource, Repository } from 'typeorm';
import { TableName, GenDbTableList, GenTableList, GenTableUpdate } from './dto/create-genTable-dto';
import { ResultData } from 'src/common/utils/result';
import { GenTableEntity } from './entities/gen-table.entity';
import { GenTableColumnEntity } from './entities/gen-table-cloumn.entity';
import { UserDto } from 'src/module/system/user/user.decorator';
export declare class ToolService {
    private readonly dataSource;
    private readonly genTableEntityRep;
    private readonly genTableColumnEntityRep;
    constructor(dataSource: DataSource, genTableEntityRep: Repository<GenTableEntity>, genTableColumnEntityRep: Repository<GenTableColumnEntity>);
    findAll(query: GenTableList): Promise<ResultData>;
    importTable(table: TableName, user: UserDto): Promise<ResultData>;
    synchDb(tableName: string): Promise<ResultData>;
    selectDbTableListByNames(tableNames: string[]): Promise<any>;
    getTableColumnInfo(tableName: string): Promise<any>;
    findOne(id: number): Promise<ResultData>;
    findOneByTableName(tableName: string): Promise<{
        columns: GenTableColumnEntity[];
        tableId: number;
        tableName: string;
        tableComment: string;
        subTableName: string;
        subTableFkName: string;
        className: string;
        tplCategory: string;
        tplWebType: string;
        packageName: string;
        moduleName: string;
        businessName: string;
        functionName: string;
        functionAuthor: string;
        genType: string;
        genPath: string;
        options: string;
        createBy: string;
        createTime: Date;
        updateBy: string;
        updateTime: Date;
        remark: string;
        status: string;
        delFlag: string;
    }>;
    genUpdate(genTableUpdate: GenTableUpdate): Promise<ResultData>;
    remove(id: number): Promise<ResultData>;
    batchGenCode(table: TableName, res: any): Promise<void>;
    getPrimaryKey(columns: any): Promise<any>;
    preview(id: number): Promise<ResultData>;
    genDbList(q: GenDbTableList): Promise<ResultData>;
    initTableColumn(column: any, table: any): void;
}
