"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceChangeListDto = exports.BalanceChangeOrderInfoDto = exports.BalanceChangeOrderItemDto = exports.RefundResultDto = exports.PaymentListDto = exports.UserBalanceRechargeResultDto = exports.BalanceRechargeResultDto = exports.PaymentResultDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PaymentResultDto {
}
exports.PaymentResultDto = PaymentResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    __metadata("design:type", Number)
], PaymentResultDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORDER_20241215_001' }),
    __metadata("design:type", String)
], PaymentResultDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: '2', enum: ['1', '2', '3', '4'] }),
    __metadata("design:type", String)
], PaymentResultDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    __metadata("design:type", Number)
], PaymentResultDto.prototype, "paymentAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方交易号', required: false, example: 'wx_transaction_123456' }),
    __metadata("design:type", String)
], PaymentResultDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false, example: '2024-12-15T10:00:00.000Z' }),
    __metadata("design:type", Date)
], PaymentResultDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付参数（用于调起支付）', required: false }),
    __metadata("design:type", Object)
], PaymentResultDto.prototype, "paymentParams", void 0);
class BalanceRechargeResultDto {
}
exports.BalanceRechargeResultDto = BalanceRechargeResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    __metadata("design:type", Number)
], BalanceRechargeResultDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值单号', example: 'RECHARGE_20241215_001' }),
    __metadata("design:type", String)
], BalanceRechargeResultDto.prototype, "rechargeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: '1', enum: ['1', '2', '3', '4'] }),
    __metadata("design:type", String)
], BalanceRechargeResultDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 100 }),
    __metadata("design:type", Number)
], BalanceRechargeResultDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方交易号', required: false, example: 'wx_transaction_123456' }),
    __metadata("design:type", String)
], BalanceRechargeResultDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false, example: '2024-12-15T10:00:00.000Z' }),
    __metadata("design:type", Date)
], BalanceRechargeResultDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付参数（用于调起支付）', required: false }),
    __metadata("design:type", Object)
], BalanceRechargeResultDto.prototype, "paymentParams", void 0);
class UserBalanceRechargeResultDto {
}
exports.UserBalanceRechargeResultDto = UserBalanceRechargeResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    __metadata("design:type", Number)
], UserBalanceRechargeResultDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值前余额', example: 100 }),
    __metadata("design:type", Number)
], UserBalanceRechargeResultDto.prototype, "beforeBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 50 }),
    __metadata("design:type", Number)
], UserBalanceRechargeResultDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值后余额', example: 150 }),
    __metadata("design:type", Number)
], UserBalanceRechargeResultDto.prototype, "afterBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值时间', example: '2024-12-15T10:00:00.000Z' }),
    __metadata("design:type", Date)
], UserBalanceRechargeResultDto.prototype, "rechargeTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作人', example: 'admin' }),
    __metadata("design:type", String)
], UserBalanceRechargeResultDto.prototype, "operator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注', required: false, example: '后台管理员充值' }),
    __metadata("design:type", String)
], UserBalanceRechargeResultDto.prototype, "remark", void 0);
class PaymentListDto {
}
exports.PaymentListDto = PaymentListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    __metadata("design:type", Number)
], PaymentListDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORDER_20241215_001' }),
    __metadata("design:type", String)
], PaymentListDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1001 }),
    __metadata("design:type", Number)
], PaymentListDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式', example: '1' }),
    __metadata("design:type", String)
], PaymentListDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    __metadata("design:type", Number)
], PaymentListDto.prototype, "paymentAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: '2' }),
    __metadata("design:type", String)
], PaymentListDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方交易号', required: false }),
    __metadata("design:type", String)
], PaymentListDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    __metadata("design:type", Date)
], PaymentListDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额', example: 0 }),
    __metadata("design:type", Number)
], PaymentListDto.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentListDto.prototype, "createTime", void 0);
class RefundResultDto {
}
exports.RefundResultDto = RefundResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付记录ID', example: 1 }),
    __metadata("design:type", Number)
], RefundResultDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额', example: 50.0 }),
    __metadata("design:type", Number)
], RefundResultDto.prototype, "refundAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款时间' }),
    __metadata("design:type", Date)
], RefundResultDto.prototype, "refundTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款状态', example: 'success' }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "refundStatus", void 0);
class BalanceChangeOrderItemDto {
}
exports.BalanceChangeOrderItemDto = BalanceChangeOrderItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    __metadata("design:type", Number)
], BalanceChangeOrderItemDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称', example: '新鲜苹果' }),
    __metadata("design:type", String)
], BalanceChangeOrderItemDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品主图URL', example: 'http://example.com/apple.jpg' }),
    __metadata("design:type", String)
], BalanceChangeOrderItemDto.prototype, "productImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量', example: 2 }),
    __metadata("design:type", Number)
], BalanceChangeOrderItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品单价', example: 5.0 }),
    __metadata("design:type", Number)
], BalanceChangeOrderItemDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品小计', example: 10.0 }),
    __metadata("design:type", Number)
], BalanceChangeOrderItemDto.prototype, "totalPrice", void 0);
class BalanceChangeOrderInfoDto {
}
exports.BalanceChangeOrderInfoDto = BalanceChangeOrderInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 'ORD_20240101000001' }),
    __metadata("design:type", String)
], BalanceChangeOrderInfoDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单商品列表', type: [BalanceChangeOrderItemDto] }),
    __metadata("design:type", Array)
], BalanceChangeOrderInfoDto.prototype, "items", void 0);
class BalanceChangeListDto {
}
exports.BalanceChangeListDto = BalanceChangeListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易ID', example: 'RECHARGE_20240101000001' }),
    __metadata("design:type", String)
], BalanceChangeListDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1001 }),
    __metadata("design:type", Number)
], BalanceChangeListDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易类型', example: 'recharge', enum: ['recharge', 'consume'] }),
    __metadata("design:type", String)
], BalanceChangeListDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '变动金额', example: 100.0 }),
    __metadata("design:type", Number)
], BalanceChangeListDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易描述', example: '余额充值' }),
    __metadata("design:type", String)
], BalanceChangeListDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易时间', example: '2024-01-01T10:00:00.000Z' }),
    __metadata("design:type", Date)
], BalanceChangeListDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易状态', example: '2', enum: ['1', '2', '3', '4', '5'] }),
    __metadata("design:type", String)
], BalanceChangeListDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单信息 (仅当交易类型为消费时存在)', type: () => BalanceChangeOrderInfoDto, required: false }),
    __metadata("design:type", BalanceChangeOrderInfoDto)
], BalanceChangeListDto.prototype, "orderInfo", void 0);
//# sourceMappingURL=payment-response.dto.js.map