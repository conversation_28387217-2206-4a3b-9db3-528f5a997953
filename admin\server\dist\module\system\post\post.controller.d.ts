import { PostService } from './post.service';
import { CreatePostDto, UpdatePostDto, ListPostDto } from './dto/index';
import { Response } from 'express';
export declare class PostController {
    private readonly postService;
    constructor(postService: PostService);
    create(createPostDto: CreatePostDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListPostDto): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updatePostDto: UpdatePostDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListPostDto): Promise<void>;
}
