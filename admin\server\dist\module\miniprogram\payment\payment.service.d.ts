import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { PaymentEntity } from './entities/payment.entity';
import { OrderEntity } from '../order/entities/order.entity';
import { MiniprogramUser } from '../user/entities/user.entity';
import { OrderService } from '../order/order.service';
import { PaymentRequestDto, PaymentNotifyDto, RefundRequestDto, UserBalanceRechargeDto, BalanceRechargeDto } from './dto/payment-request.dto';
import { ResultData } from '../../../common/utils/result';
export declare class PaymentService {
    private readonly paymentRepository;
    private readonly orderRepository;
    private readonly userRepository;
    private readonly configService;
    private readonly orderService;
    private readonly logger;
    private readonly wechatConfig;
    private readonly certSerialNo;
    constructor(paymentRepository: Repository<PaymentEntity>, orderRepository: Repository<OrderEntity>, userRepository: Repository<MiniprogramUser>, configService: ConfigService, orderService: OrderService);
    createPayment(paymentRequest: PaymentRequestDto, userId: number): Promise<ResultData>;
    createBalanceRecharge(rechargeDto: BalanceRechargeDto, userId: number): Promise<ResultData>;
    rechargeUserBalance(rechargeDto: UserBalanceRechargeDto, operatorId: number): Promise<ResultData>;
    generateWechatPayParams(payment: PaymentEntity, openid: string, description?: string): Promise<any>;
    private generateNonceStr;
    private generatePaySign;
    handlePaymentNotify(orderId: string, notifyData: PaymentNotifyDto): Promise<any>;
    private decryptNotifyData;
    private updateUserBalance;
    private updateOrderStatus;
    private handleBalancePayment;
    getPaymentStatus(orderId: string, userId: number): Promise<ResultData>;
    getUserPaymentList(userId: number, pageNum?: number, pageSize?: number): Promise<ResultData>;
    requestRefund(refundRequest: RefundRequestDto, userId: number): Promise<ResultData>;
    getUserBalance(userId: number): Promise<ResultData>;
    getUserBalanceChangeList(userId: number, pageNum?: number, pageSize?: number): Promise<ResultData>;
    private generateTimestamp;
    private generateRandomString;
    private processWechatRefund;
    private processBalanceRefund;
}
