"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const class_validator_1 = require("class-validator");
const result_1 = require("../../../common/utils/result");
const index_1 = require("../../../common/utils/index");
const gen_table_entity_1 = require("./entities/gen-table.entity");
const gen_table_cloumn_entity_1 = require("./entities/gen-table-cloumn.entity");
const config_1 = __importDefault(require("./config"));
const gen_constant_1 = require("../../../common/constant/gen.constant");
const lodash_1 = require("lodash");
const index_2 = require("./utils/index");
const index_3 = require("./template/index");
const archiver_1 = __importDefault(require("archiver"));
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
let ToolService = class ToolService {
    constructor(dataSource, genTableEntityRep, genTableColumnEntityRep) {
        this.dataSource = dataSource;
        this.genTableEntityRep = genTableEntityRep;
        this.genTableColumnEntityRep = genTableColumnEntityRep;
    }
    async findAll(query) {
        const { pageNum = 1, pageSize = 10, tableNames, tableComment } = query;
        const entity = this.genTableEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (tableNames) {
            entity.andWhere('entity.tableName LIKE :tableNames', { tableNames: `%${tableNames}%` });
        }
        if (tableComment) {
            entity.andWhere('entity.comment LIKE :tableComment', { tableComment: `%${tableComment}%` });
        }
        const [list, total] = await entity
            .skip((pageNum - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total: total,
        });
    }
    async importTable(table, user) {
        const tableNames = table.tableNames.split(',');
        const tableList = await this.selectDbTableListByNames(tableNames);
        for (const table of tableList) {
            const tableName = table.tableName;
            const tableData = {
                tableName: tableName,
                tableComment: table.tableComment?.trim() || table.tableName,
                className: config_1.default.autoRemovePre ? index_2.StringUtils.toPascalCase(tableName.replace(new RegExp(config_1.default.tablePrefix.join('|')), '')) : index_2.StringUtils.toPascalCase(tableName),
                packageName: config_1.default.packageName,
                moduleName: config_1.default.moduleName,
                businessName: tableName.slice(tableName.lastIndexOf('_') + 1),
                functionName: table.tableComment?.trim() || table.tableName,
                functionAuthor: config_1.default.author,
                createBy: user.userName,
            };
            const tableInfo = await this.genTableEntityRep.save(tableData);
            const tableColumn = await this.getTableColumnInfo(tableName);
            for (const column of tableColumn) {
                this.initTableColumn(column, tableInfo);
                column.sort = Number(column.sort);
                await this.genTableColumnEntityRep.save(column);
            }
        }
        return result_1.ResultData.ok('添加成功');
    }
    async synchDb(tableName) {
        const table = await this.findOneByTableName(tableName);
        if (!table)
            throw new common_1.BadRequestException('同步数据失败，原表结构不存在！');
        const tableColumns = table.columns;
        const columns = await this.getTableColumnInfo(tableName);
        if (!columns || !columns?.length)
            throw new common_1.BadRequestException('同步数据失败，原表结构不存在！');
        const tableColumnMap = {};
        for (const v of tableColumns) {
            tableColumnMap[v.columnName] = v;
        }
        for (const column of columns) {
            this.initTableColumn(column, table);
            if (tableColumnMap[column.columnName]) {
                const prevColumn = tableColumnMap[column.columnName];
                column.columnId = prevColumn.columnId;
                column.sort = Number(column.sort);
                if (column.isList === '1') {
                    column.dictType = prevColumn.dictType;
                    column.queryType = prevColumn.queryType;
                }
                await this.genTableColumnEntityRep.update({ columnId: column.columnId }, column);
            }
            else {
                column.sort = Number(column);
                await this.genTableEntityRep.save(column);
            }
        }
        if (tableColumns.length > 0) {
            const delColumns = tableColumns.filter((v) => !columns.some((z) => z.columnName === v.columnName)).map((v) => v.columnId);
            if (delColumns.length > 0) {
                await this.genTableColumnEntityRep.delete(delColumns);
            }
        }
        return result_1.ResultData.ok();
    }
    selectDbTableListByNames(tableNames) {
        if (!tableNames.length)
            return null;
        return this.dataSource.query(`select table_name as tableName, table_comment as tableComment, create_time as createTime, update_time as updateTime from information_schema.tables
      where table_schema = (select database())
      and table_name NOT LIKE 'qrtz_%' and table_name NOT LIKE 'gen_%'
      and table_name NOT IN (select table_name from gen_table)
      and table_name IN (${'?,'.repeat(tableNames.length).slice(0, -1)})`, tableNames);
    }
    async getTableColumnInfo(tableName) {
        if (!tableName)
            return null;
        return this.dataSource.query(`SELECT column_name AS columnName,
      (CASE WHEN (is_nullable = 'no' && column_key != 'PRI') THEN '1' ELSE '0' END) AS isRequired,
      (CASE WHEN column_key = 'PRI' THEN '1' ELSE '0' END) AS isPk,
      ordinal_position AS sort, 
      column_comment AS columnComment, 
      column_default AS columnDefault,
      (CASE WHEN extra = 'auto_increment' THEN '1' ELSE '0' END) AS isIncrement, 
      SUBSTRING_INDEX(column_type, '(', 1) AS columnType
      FROM information_schema.columns WHERE table_schema = (SELECT DATABASE())  AND table_name = '${tableName}' ORDER BY ordinal_position`);
    }
    async findOne(id) {
        const data = await this.genTableEntityRep.findOne({ where: { tableId: id, delFlag: '0' } });
        const columns = await this.genTableColumnEntityRep.find({ where: { tableId: id, delFlag: '0' } });
        return result_1.ResultData.ok({ info: { ...data, columns } });
    }
    async findOneByTableName(tableName) {
        const data = await this.genTableEntityRep.findOne({ where: { tableName: tableName, delFlag: '0' } });
        const columns = await this.genTableColumnEntityRep.find({ where: { tableId: data.tableId, delFlag: '0' } });
        return { ...data, columns };
    }
    async genUpdate(genTableUpdate) {
        for (const item of genTableUpdate.columns) {
            if (item.columnId)
                await this.genTableColumnEntityRep.update({ columnId: item.columnId }, item);
        }
        delete genTableUpdate.columns;
        await this.genTableEntityRep.update({ tableId: +genTableUpdate.tableId }, genTableUpdate);
        return result_1.ResultData.ok({ genTableUpdate });
    }
    async remove(id) {
        await this.genTableEntityRep.delete({ tableId: id });
        await this.genTableColumnEntityRep.delete({ tableId: id });
        return result_1.ResultData.ok();
    }
    async batchGenCode(table, res) {
        const zipFilePath = path.join(__dirname, 'temp.zip');
        const output = fs.createWriteStream(zipFilePath);
        const archive = (0, archiver_1.default)('zip', {
            zlib: { level: 9 },
        });
        output.on('close', async () => {
            res.download(zipFilePath, 'download.zip', async (err) => {
                if (!err)
                    await fs.remove(zipFilePath);
                else
                    res.status(500).send('Error downloading file');
            });
        });
        archive.on('error', (err) => {
            throw err;
        });
        const tableNamesList = table.tableNames.split(',');
        const tableList = await Promise.all(tableNamesList.map(async (item) => {
            const data = await this.genTableEntityRep.findOne({ where: { tableName: item, delFlag: '0' } });
            const columns = await this.genTableColumnEntityRep.find({ where: { tableId: data.tableId, delFlag: '0' } });
            const primaryKey = await this.getPrimaryKey(columns);
            return { primaryKey, BusinessName: data.businessName, ...data, columns };
        }));
        archive.pipe(output);
        for (const item of tableList) {
            const list = (0, index_3.index)(item);
            const templates = [
                { content: list['tool/template/nestjs/entity.ts.vm'], path: `nestjs/${item.BusinessName}/entities/${item.businessName}.entity.ts` },
                { content: list['tool/template/nestjs/dto.ts.vm'], path: `nestjs/${item.BusinessName}/dto/${item.businessName}.dto.ts` },
                { content: list['tool/template/nestjs/controller.ts.vm'], path: `nestjs/${item.BusinessName}/${item.businessName}.controller.ts` },
                { content: list['tool/template/nestjs/service.ts.vm'], path: `nestjs/${item.BusinessName}/${item.businessName}.service.ts` },
                { content: list['tool/template/nestjs/module.ts.vm'], path: `nestjs/${item.BusinessName}/${item.businessName}.module.ts` },
                { content: list['tool/template/vue/api.js.vm'], path: `vue/${item.BusinessName}/${item.businessName}.js` },
                { content: list['tool/template/vue/indexVue.vue.vm'], path: `vue/${item.BusinessName}/${item.businessName}/index.vue` },
                { content: list['tool/template/vue/dialogVue.vue.vm'], path: `vue/${item.BusinessName}/${item.businessName}/components/indexDialog.vue` },
            ];
            for (const template of templates) {
                if (!template.content)
                    throw new Error('One or more templates are undefined');
                archive.append(Buffer.from(template.content), { name: template.path });
            }
        }
        await archive.finalize();
    }
    async getPrimaryKey(columns) {
        for (const column of columns) {
            if (column.isPk === '1') {
                return column.javaField;
            }
        }
        return null;
    }
    async preview(id) {
        const data = await this.genTableEntityRep.findOne({ where: { tableId: id, delFlag: '0' } });
        const columns = await this.genTableColumnEntityRep.find({ where: { tableId: id, delFlag: '0' } });
        const primaryKey = await this.getPrimaryKey(columns);
        const info = { primaryKey, BusinessName: (0, index_2.capitalize)(data.businessName), ...data, columns };
        return result_1.ResultData.ok((0, index_3.index)(info));
    }
    async genDbList(q) {
        const params = [];
        let sql = `
    select table_name as tableName, table_comment as tableComment, create_time as createTime, update_time as updateTime from information_schema.tables
    where table_schema = (select database())
    and table_name NOT LIKE 'qrtz_%' and table_name NOT LIKE 'gen_%'
    and table_name NOT IN (select table_name from gen_table)`;
        let sqlCount = `
    select count(*) as total from information_schema.tables
    where table_schema = (select database())
    and table_name NOT LIKE 'qrtz_%' and table_name NOT LIKE 'gen_%'
    and table_name NOT IN (select table_name from gen_table)
    `;
        if ((0, class_validator_1.isNotEmpty)(q.tableName)) {
            sql += ` and table_name like concat("%", ?,"%") `;
            sqlCount += ` and table_name like concat("%", ?,"%") `;
            params.push(q.tableName);
        }
        if ((0, class_validator_1.isNotEmpty)(q.tableComment)) {
            sql += ` and table_comment like concat("%", ?,"%") `;
            sqlCount += ` and table_comment like concat("%", ?,"%") `;
            params.push(q.tableComment);
        }
        sql += `
      ORDER BY create_time desc, update_time desc
      limit ${(q.pageNum - 1) * q.pageSize},${q.pageSize}
      	`;
        const data = {
            list: await this.dataSource.query(sql, params).then((res) => res.map((v) => ({
                ...v,
                createTime: (0, index_1.FormatDate)(v.createTime),
                updateTime: (0, index_1.FormatDate)(v.updateTime),
            }))),
            total: Number((await this.dataSource.query(sqlCount, params))[0]?.total),
        };
        return result_1.ResultData.ok(data);
    }
    initTableColumn(column, table) {
        const columnName = column.columnName;
        const dataType = column.columnType;
        column.tableId = table.tableId;
        column.javaField = (0, lodash_1.camelCase)(columnName);
        column.javaType = gen_constant_1.GenConstants.TYPE_STRING;
        column.queryType = gen_constant_1.GenConstants.QUERY_EQ;
        column.createBy = column.createBy || 'admin';
        column.columnComment = column.columnComment || column.columnName;
        column.createTime = column.createTime || (0, index_1.GetNowDate)();
        column.updateBy = 'admin';
        column.updateTime = (0, index_1.GetNowDate)();
        if ((0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNTYPE_TEXT, dataType)) {
            column.htmlType = gen_constant_1.GenConstants.HTML_TEXTAREA;
        }
        else if ((0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNTYPE_STR, dataType)) {
            const len = (0, index_2.getColumnLength)(dataType);
            column.htmlType = len >= 500 ? gen_constant_1.GenConstants.HTML_TEXTAREA : gen_constant_1.GenConstants.HTML_INPUT;
        }
        else if ((0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNTYPE_TIME, dataType)) {
            column.javaType = gen_constant_1.GenConstants.TYPE_DATE;
            column.htmlType = gen_constant_1.GenConstants.HTML_DATETIME;
        }
        else if ((0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNTYPE_NUMBER, dataType)) {
            column.htmlType = gen_constant_1.GenConstants.HTML_INPUT;
            column.javaType = gen_constant_1.GenConstants.TYPE_NUMBER;
        }
        column.isRequired = gen_constant_1.GenConstants.NOT_REQUIRE;
        if (!(0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNNAME_NOT_INSERT, columnName)) {
            column.isInsert = gen_constant_1.GenConstants.REQUIRE;
        }
        if (!(0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNNAME_NOT_EDIT, columnName)) {
            column.isEdit = gen_constant_1.GenConstants.REQUIRE;
        }
        if (!(0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNNAME_NOT_LIST, columnName)) {
            column.isList = gen_constant_1.GenConstants.REQUIRE;
        }
        if (!(0, index_2.arraysContains)(gen_constant_1.GenConstants.COLUMNNAME_NOT_QUERY, columnName) && column.htmlType != gen_constant_1.GenConstants.HTML_TEXTAREA) {
            column.isQuery = gen_constant_1.GenConstants.REQUIRE;
        }
        if (column.isPk == '1') {
            column.isInsert = gen_constant_1.GenConstants.NOT_REQUIRE;
            column.isEdit = gen_constant_1.GenConstants.REQUIRE;
            column.isQuery = gen_constant_1.GenConstants.REQUIRE;
            column.isList = gen_constant_1.GenConstants.REQUIRE;
        }
        const lowerColumnName = (0, lodash_1.toLower)(columnName);
        if (lowerColumnName.includes('name')) {
            column.queryType = gen_constant_1.GenConstants.QUERY_LIKE;
        }
        if (lowerColumnName.includes('status')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_RADIO;
        }
        else if (lowerColumnName.includes('type') || lowerColumnName.includes('sex')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_SELECT;
        }
        else if (lowerColumnName.includes('time') || lowerColumnName.includes('_date') || lowerColumnName.includes('Date')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_DATETIME;
            column.queryType = gen_constant_1.GenConstants.QUERY_BETWEEN;
        }
        else if (lowerColumnName.includes('image')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_IMAGE_UPLOAD;
        }
        else if (lowerColumnName.includes('file')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_FILE_UPLOAD;
        }
        else if (lowerColumnName.includes('content')) {
            column.htmlType = gen_constant_1.GenConstants.HTML_EDITOR;
        }
    }
};
exports.ToolService = ToolService;
exports.ToolService = ToolService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __param(1, (0, typeorm_1.InjectRepository)(gen_table_entity_1.GenTableEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(gen_table_cloumn_entity_1.GenTableColumnEntity)),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ToolService);
//# sourceMappingURL=tool.service.js.map