export declare class UserProfileDto {
    userId: number;
    openid: string;
    nickname: string;
    avatar: string;
    gender: string;
    phone: string;
    email: string;
    birthday: Date;
    region: string;
    level: number;
    points: number;
    balance: number;
    totalAmount: number;
    orderCount: number;
    userType: string;
    vipExpireTime: Date;
    tags: string;
    pushEnabled: string;
    createTime: Date;
    lastLoginTime: Date;
}
export declare class UserQueryDto {
    nickname?: string;
    phone?: string;
    level?: number;
    userType?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
    pageNum?: number;
    pageSize?: number;
}
export declare class UserStatsDto {
    footprintCount: number;
    favoriteCount: number;
    couponCount: number;
    balance: number;
}
