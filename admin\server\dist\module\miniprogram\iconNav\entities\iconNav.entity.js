"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IconNavEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let IconNavEntity = class IconNavEntity extends base_1.BaseEntity {
};
exports.IconNavEntity = IconNavEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金刚区ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'icon_id', comment: '金刚区ID' }),
    __metadata("design:type", Number)
], IconNavEntity.prototype, "iconId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图标名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, name: 'name', comment: '图标名称' }),
    __metadata("design:type", String)
], IconNavEntity.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, name: 'image_url', comment: '图片URL' }),
    __metadata("design:type", String)
], IconNavEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'sort_order', default: 0, comment: '排序顺序' }),
    __metadata("design:type", Number)
], IconNavEntity.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '链接URL' }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, name: 'link_url', nullable: true, comment: '链接URL' }),
    __metadata("design:type", String)
], IconNavEntity.prototype, "linkUrl", void 0);
exports.IconNavEntity = IconNavEntity = __decorate([
    (0, typeorm_1.Entity)('mini_icon_nav', { comment: '小程序金刚区表' })
], IconNavEntity);
//# sourceMappingURL=iconNav.entity.js.map