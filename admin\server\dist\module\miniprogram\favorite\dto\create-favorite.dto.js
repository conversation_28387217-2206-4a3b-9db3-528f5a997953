"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFavoriteDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateFavoriteDto {
}
exports.CreateFavoriteDto = CreateFavoriteDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '1001' }),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    __metadata("design:type", String)
], CreateFavoriteDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    (0, class_validator_1.IsInt)({ message: '商品ID必须是整数' }),
    (0, class_validator_1.IsNotEmpty)({ message: '商品ID不能为空' }),
    __metadata("design:type", Number)
], CreateFavoriteDto.prototype, "productId", void 0);
//# sourceMappingURL=create-favorite.dto.js.map