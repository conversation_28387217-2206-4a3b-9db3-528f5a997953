import { PaymentService } from './payment.service';
import { PaymentRequestDto, PaymentNotifyDto, RefundRequestDto, UserBalanceRechargeDto, BalanceRechargeDto } from './dto/payment-request.dto';
import { ResultData } from '../../../common/utils/result';
import { Response } from 'express';
export declare class PaymentController {
    private readonly paymentService;
    private readonly logger;
    constructor(paymentService: PaymentService);
    createPayment(userId: number, paymentRequest: PaymentRequestDto): Promise<ResultData>;
    createBalanceRecharge(userId: number, rechargeDto: BalanceRechargeDto): Promise<ResultData>;
    rechargeUserBalance(rechargeDto: UserBalanceRechargeDto, operatorId: number): Promise<ResultData>;
    handlePaymentNotify(orderId: string, notifyData: PaymentNotifyDto, res: Response): Promise<Response<any, Record<string, any>>>;
    getUserBalance(userId: number): Promise<ResultData>;
    getPaymentStatus(orderId: string, userId: number): Promise<ResultData>;
    getUserBalanceChangeList(pageNum: string, pageSize: string, userId: number): Promise<ResultData>;
    requestRefund(refundRequest: RefundRequestDto, userId: number): Promise<ResultData>;
}
