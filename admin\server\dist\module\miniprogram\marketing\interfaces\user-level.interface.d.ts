export declare enum PointRecordType {
    REGISTER = 1,
    ORDER = 2,
    REVIEW = 3,
    ACTIVITY = 4,
    EXPIRED = 5
}
export declare enum PointUsageStatus {
    UNUSED = 0,
    USED = 1
}
export interface IUserLevelManager {
    updateUserLevel(userId: number, points: number): Promise<number>;
    getUserDiscount(userId: number): Promise<number>;
}
export interface IPointManager {
    addPointRecord(userId: number, points: number, type: PointRecordType, description: string, orderId?: string): Promise<boolean>;
    calculateOrderPoints(orderId: string, userId: number, orderAmount: number): Promise<number>;
    checkPointsExpiration(userId: number): Promise<boolean>;
}
