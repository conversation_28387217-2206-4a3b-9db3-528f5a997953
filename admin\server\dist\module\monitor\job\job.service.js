"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var JobService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const cron_1 = require("cron");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const job_entity_1 = require("./entities/job.entity");
const result_1 = require("../../../common/utils/result");
const task_service_1 = require("./task.service");
const export_1 = require("../../../common/utils/export");
let JobService = JobService_1 = class JobService {
    constructor(schedulerRegistry, jobRepository, taskService) {
        this.schedulerRegistry = schedulerRegistry;
        this.jobRepository = jobRepository;
        this.taskService = taskService;
        this.logger = new common_1.Logger(JobService_1.name);
        this.initializeJobs();
    }
    async initializeJobs() {
        const jobs = await this.jobRepository.find({ where: { status: '0' } });
        jobs.forEach((job) => {
            this.addCronJob(job.jobName, job.cronExpression, job.invokeTarget);
        });
    }
    async list(query) {
        const { pageNum = 1, pageSize = 10, jobName, jobGroup, status } = query;
        const where = {};
        if (jobName) {
            where.jobName = (0, typeorm_2.Like)(`%${jobName}%`);
        }
        if (jobGroup) {
            where.jobGroup = jobGroup;
        }
        if (status) {
            where.status = status;
        }
        const [list, total] = await this.jobRepository.findAndCount({
            where,
            skip: (pageNum - 1) * pageSize,
            take: pageSize,
            order: {
                createTime: 'DESC',
            },
        });
        return result_1.ResultData.ok({ list, total });
    }
    async getJob(jobId) {
        const job = await this.jobRepository.findOne({ where: { jobId } });
        if (!job) {
            throw new Error('任务不存在');
        }
        return result_1.ResultData.ok(job);
    }
    async create(createJobDto, userName) {
        const job = this.jobRepository.create({
            ...createJobDto,
            createBy: userName,
            updateBy: userName,
        });
        await this.jobRepository.save(job);
        if (job.status === '0') {
            this.addCronJob(job.jobName, job.cronExpression, createJobDto.invokeTarget);
        }
        return result_1.ResultData.ok();
    }
    async update(jobId, updateJobDto, userName) {
        const job = await this.jobRepository.findOne({ where: { jobId } });
        if (!job) {
            throw new Error('任务不存在');
        }
        if (updateJobDto.cronExpression !== job.cronExpression || updateJobDto.status !== job.status || updateJobDto.invokeTarget !== job.invokeTarget) {
            const cronJob = this.getCronJob(job.jobName);
            if (cronJob) {
                this.deleteCronJob(job.jobName);
            }
            if (updateJobDto.status === '0') {
                this.addCronJob(job.jobName, updateJobDto.cronExpression || job.cronExpression, updateJobDto.invokeTarget);
            }
        }
        await this.jobRepository.update(jobId, {
            ...updateJobDto,
            updateBy: userName,
            updateTime: new Date(),
        });
        return result_1.ResultData.ok();
    }
    async remove(jobIds) {
        const ids = Array.isArray(jobIds) ? jobIds : [jobIds];
        const jobs = await this.jobRepository.findByIds(ids);
        for (const job of jobs) {
            try {
                this.deleteCronJob(job.jobName);
            }
            catch (error) {
            }
        }
        await this.jobRepository.remove(jobs);
        return result_1.ResultData.ok();
    }
    async changeStatus(jobId, status, userName) {
        const job = await this.jobRepository.findOne({ where: { jobId } });
        if (!job) {
            throw new Error('任务不存在');
        }
        const cronJob = this.getCronJob(job.jobName);
        if (status === '0') {
            if (!cronJob) {
                this.addCronJob(job.jobName, job.cronExpression, job.invokeTarget);
            }
            else {
                cronJob.start();
            }
        }
        else {
            if (cronJob) {
                cronJob.stop();
            }
        }
        await this.jobRepository.update(jobId, {
            status,
            updateBy: userName,
            updateTime: new Date(),
        });
        return result_1.ResultData.ok();
    }
    async run(jobId) {
        const job = await this.jobRepository.findOne({ where: { jobId } });
        if (!job) {
            throw new Error('任务不存在');
        }
        await this.taskService.executeTask(job.invokeTarget, job.jobName, job.jobGroup);
        return result_1.ResultData.ok();
    }
    addCronJob(name, cronTime, invokeTarget) {
        cronTime = cronTime.replace('?', '*');
        const job = new cron_1.CronJob(cronTime, async () => {
            this.logger.warn(`定时任务 ${name} 正在执行，调用方法: ${invokeTarget}`);
            await this.taskService.executeTask(invokeTarget, name);
        });
        this.schedulerRegistry.addCronJob(name, job);
        job.start();
    }
    deleteCronJob(name) {
        this.schedulerRegistry.deleteCronJob(name);
    }
    getCronJob(name) {
        try {
            return this.schedulerRegistry.getCronJob(name);
        }
        catch (error) {
            return null;
        }
    }
    async export(res, body) {
        const list = await this.list(body);
        const options = {
            sheetName: '定时任务',
            data: list.data.list,
            header: [
                { title: '任务编号', dataIndex: 'jobId' },
                { title: '任务名称', dataIndex: 'jobName' },
                { title: '任务组名', dataIndex: 'jobGroup' },
                { title: '调用目标字符串', dataIndex: 'invokeTarget' },
                { title: 'cron执行表达式', dataIndex: 'cronExpression' },
            ],
            dictMap: {
                status: {
                    '0': '成功',
                    '1': '失败',
                },
                jobGroup: {
                    SYSTEM: '系统',
                    DEFAULT: '默认',
                },
            },
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.JobService = JobService;
exports.JobService = JobService = JobService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(job_entity_1.Job)),
    __metadata("design:paramtypes", [schedule_1.SchedulerRegistry,
        typeorm_2.Repository,
        task_service_1.TaskService])
], JobService);
//# sourceMappingURL=job.service.js.map