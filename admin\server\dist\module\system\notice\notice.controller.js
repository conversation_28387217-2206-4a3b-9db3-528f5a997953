"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoticeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const notice_service_1 = require("./notice.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let NoticeController = class NoticeController {
    constructor(noticeService) {
        this.noticeService = noticeService;
    }
    create(createConfigDto, req) {
        createConfigDto['createBy'] = req.user.userName;
        return this.noticeService.create(createConfigDto);
    }
    findAll(query) {
        return this.noticeService.findAll(query);
    }
    findOne(id) {
        return this.noticeService.findOne(+id);
    }
    update(updateNoticeDto) {
        return this.noticeService.update(updateNoticeDto);
    }
    remove(ids) {
        const noticeIds = ids.split(',').map((id) => +id);
        return this.noticeService.remove(noticeIds);
    }
};
exports.NoticeController = NoticeController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '通知公告-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateNoticeDto,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:notice:add'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateNoticeDto, Object]),
    __metadata("design:returntype", void 0)
], NoticeController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '通知公告-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ListNoticeDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:notice:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListNoticeDto]),
    __metadata("design:returntype", void 0)
], NoticeController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '通知公告-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:notice:query'),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NoticeController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '通知公告-更新',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:notice:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateNoticeDto]),
    __metadata("design:returntype", void 0)
], NoticeController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '通知公告-删除',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:notice:remove'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NoticeController.prototype, "remove", null);
exports.NoticeController = NoticeController = __decorate([
    (0, swagger_1.ApiTags)('通知公告'),
    (0, common_1.Controller)('system/notice'),
    __metadata("design:paramtypes", [notice_service_1.NoticeService])
], NoticeController);
//# sourceMappingURL=notice.controller.js.map