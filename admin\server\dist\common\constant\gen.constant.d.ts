export declare class GenConstants {
    static readonly TPL_CRUD: string;
    static readonly TPL_TREE: string;
    static readonly TPL_SUB: string;
    static readonly TREE_CODE: string;
    static readonly TREE_PARENT_CODE: string;
    static readonly TREE_NAME: string;
    static readonly PARENT_MENU_ID: string;
    static readonly PARENT_MENU_NAME: string;
    static readonly COLUMNTYPE_STR: string[];
    static readonly COLUMNTYPE_TEXT: string[];
    static readonly COLUMNTYPE_TIME: string[];
    static readonly COLUMNTYPE_NUMBER: string[];
    static readonly COLUMNNAME_NOT_INSERT: string[];
    static readonly COLUMNNAME_NOT_EDIT: string[];
    static readonly COLUMNNAME_NOT_LIST: string[];
    static readonly COLUMNNAME_NOT_QUERY: string[];
    static readonly BASE_ENTITY: string[];
    static readonly TREE_ENTITY: string[];
    static readonly HTML_INPUT: string;
    static readonly HTML_TEXTAREA: string;
    static readonly HTML_SELECT: string;
    static readonly HTML_RADIO: string;
    static readonly HTML_CHECKBOX: string;
    static readonly HTML_DATETIME: string;
    static readonly HTML_IMAGE_UPLOAD: string;
    static readonly HTML_FILE_UPLOAD: string;
    static readonly HTML_EDITOR: string;
    static readonly TYPE_STRING: string;
    static readonly TYPE_INTEGER: string;
    static readonly TYPE_LONG: string;
    static readonly TYPE_DOUBLE: string;
    static readonly TYPE_BIGDECIMAL: string;
    static readonly TYPE_DATE: string;
    static readonly QUERY_EQ: string;
    static readonly QUERY_NE: string;
    static readonly QUERY_GT: string;
    static readonly QUERY_GTE: string;
    static readonly QUERY_LT: string;
    static readonly QUERY_LTE: string;
    static readonly QUERY_LIKE: string;
    static readonly QUERY_BETWEEN: string;
    static readonly REQUIRE: string;
    static readonly NOT_REQUIRE: string;
    static readonly TYPE_NUMBER: string;
}
