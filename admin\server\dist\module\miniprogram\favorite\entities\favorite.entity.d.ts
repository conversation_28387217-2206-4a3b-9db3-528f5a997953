import { BaseEntity } from '../../../../common/entities/base';
import { MiniprogramUser } from '../../user/entities/user.entity';
import { ProductEntity } from '../../product/entities/product.entity';
export declare class FavoriteEntity extends BaseEntity {
    favoriteId: number;
    userId: string;
    productId: number;
    favoriteTime: Date;
    user: MiniprogramUser;
    product: ProductEntity;
}
