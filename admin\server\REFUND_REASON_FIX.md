# 订单列表接口退款原因返回null问题修复

## 问题描述
在订单列表接口中，退款原因字段返回的是null，而不是实际的退款原因。

## 问题分析
通过代码分析发现：

1. **小程序端的 `getUserOrders` 方法**（第486行）：
   - ✅ 正确地左连接了 `refund_requests` 表
   - ✅ 正确地返回了退款相关字段：`refundStatus`、`refundReason`、`refundRequestTime`

2. **管理端的 `findAll` 方法**（第946行）：
   - ❌ **没有**左连接 `refund_requests` 表
   - ❌ 只返回了订单表自身的 `refundType` 字段，**没有**返回退款申请表中的退款原因

3. **管理端的 `findSelfPickupOrders` 方法**（第1113行）：
   - ❌ **没有**左连接 `refund_requests` 表
   - ❌ 同样缺少退款申请相关字段

## 修复方案

### 1. 修复管理端订单列表接口 (`findAll`)

**文件**: `admin/server/src/module/miniprogram/order/order.service.ts`

**修改位置**: 第950-955行

**修改前**:
```typescript
const queryBuilder = this.orderRepository
  .createQueryBuilder('order')
  .leftJoinAndSelect('order.orderItems', 'orderItems')
  .leftJoinAndSelect('orderItems.product', 'product')
  .leftJoinAndSelect('order.user', 'user')
  .where('order.delFlag = :delFlag', { delFlag: '0' });
```

**修改后**:
```typescript
const queryBuilder = this.orderRepository
  .createQueryBuilder('order')
  .leftJoinAndSelect('order.orderItems', 'orderItems')
  .leftJoinAndSelect('orderItems.product', 'product')
  .leftJoinAndSelect('order.user', 'user')
  .leftJoin('refund_requests', 'refundRequest', 'refundRequest.order_id = order.orderId') // 左连接退款申请表
  .addSelect([
    'refundRequest.status AS refund_status',
    'refundRequest.refund_reason AS refund_reason',
    'refundRequest.request_time AS refund_request_time'
  ])
  .where('order.delFlag = :delFlag', { delFlag: '0' });
```

**修改位置**: 第1019-1046行

**修改前**:
```typescript
const orderData = {
  // ... 其他字段
  refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
};
```

**修改后**:
```typescript
const orderData = {
  // ... 其他字段
  refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
  // 添加退款申请相关字段
  refundStatus: order.refund_status || null, // 退款申请状态
  refundReason: order.refund_reason || null, // 退款原因
  refundRequestTime: order.refund_request_time || null, // 退款申请时间
};
```

### 2. 修复自提订单列表接口 (`findSelfPickupOrders`)

**修改位置**: 第1117-1125行

**修改前**:
```typescript
const queryBuilder = this.orderRepository
  .createQueryBuilder('order')
  .leftJoinAndSelect('order.orderItems', 'orderItems')
  .leftJoinAndSelect('orderItems.product', 'product')
  .leftJoinAndSelect('order.user', 'user')
  .leftJoin('payments', 'payment', 'payment.order_id = order.orderId AND payment.payment_status = :paymentStatus', { paymentStatus: '2' })
  .addSelect(['payment.payment_method', 'payment.payment_time', 'payment.payment_amount'])
  .where('order.delFlag = :delFlag', { delFlag: '0' })
  .andWhere('order.deliveryType = :deliveryType', { deliveryType: '2' }); // 自提类型订单
```

**修改后**:
```typescript
const queryBuilder = this.orderRepository
  .createQueryBuilder('order')
  .leftJoinAndSelect('order.orderItems', 'orderItems')
  .leftJoinAndSelect('orderItems.product', 'product')
  .leftJoinAndSelect('order.user', 'user')
  .leftJoin('payments', 'payment', 'payment.order_id = order.orderId AND payment.payment_status = :paymentStatus', { paymentStatus: '2' })
  .addSelect(['payment.payment_method', 'payment.payment_time', 'payment.payment_amount'])
  .leftJoin('refund_requests', 'refundRequest', 'refundRequest.order_id = order.orderId') // 左连接退款申请表
  .addSelect([
    'refundRequest.status AS refund_status',
    'refundRequest.refund_reason AS refund_reason',
    'refundRequest.request_time AS refund_request_time'
  ])
  .where('order.delFlag = :delFlag', { delFlag: '0' })
  .andWhere('order.deliveryType = :deliveryType', { deliveryType: '2' }); // 自提类型订单
```

**修改位置**: 第1197-1224行

同样添加退款申请相关字段到返回数据中。

## 修复结果

修复后，管理端的订单列表接口将能够正确返回：

- `refundStatus`: 退款申请状态（1待审核2已退款3已拒绝）
- `refundReason`: 退款原因（用户提交的退款原因）
- `refundRequestTime`: 退款申请时间

这样就解决了退款原因返回null的问题，使管理端和小程序端的行为保持一致。

## 测试验证

创建了单元测试文件 `order.service.spec.ts` 来验证修复的正确性，测试覆盖了：
1. 管理端订单列表接口是否正确连接退款申请表
2. 自提订单列表接口是否正确连接退款申请表
3. 返回数据是否包含退款相关字段

## 影响范围

此修复只影响管理端的订单列表接口，不会影响其他功能：
- ✅ 小程序端订单列表接口保持不变（已经是正确的）
- ✅ 订单详情接口保持不变
- ✅ 退款申请功能保持不变
- ✅ 只是让管理端能够正确显示退款原因
