"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Operlog = void 0;
const common_1 = require("@nestjs/common");
const operlog_interceptor_1 = require("../interceptor/operlog.interceptor");
const Operlog = (logConfig) => {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)('operlog', logConfig), (0, common_1.UseInterceptors)(operlog_interceptor_1.OperlogInterceptor));
};
exports.Operlog = Operlog;
//# sourceMappingURL=operlog.decorator.js.map