"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FootprintController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FootprintController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const footprint_service_1 = require("./footprint.service");
const user_decorator_1 = require("../../system/user/user.decorator");
const result_1 = require("../../../common/utils/result");
let FootprintController = FootprintController_1 = class FootprintController {
    constructor(footprintService) {
        this.footprintService = footprintService;
        this.logger = new common_1.Logger(FootprintController_1.name);
    }
    async findUserFootprints(userId, pageNum, pageSize) {
        this.logger.log(`查询用户足迹列表: userId=${userId}, pageNum=${pageNum}, pageSize=${pageSize}`);
        return await this.footprintService.findUserFootprints(userId, pageNum, pageSize);
    }
    async removeFootprint(footprintId, userId) {
        this.logger.log(`删除足迹: footprintId=${footprintId}, userId=${userId}`);
        return await this.footprintService.removeFootprints(userId, [footprintId]);
    }
    async removeFootprints(body) {
        const { userId, footprintIds } = body;
        this.logger.log(`批量删除足迹: userId=${userId}, footprintIds=${footprintIds}`);
        return await this.footprintService.removeFootprints(userId, footprintIds);
    }
    async clearUserFootprints(userId) {
        this.logger.log(`清空用户足迹: userId=${userId}`);
        return await this.footprintService.clearUserFootprints(userId);
    }
};
exports.FootprintController = FootprintController;
__decorate([
    (0, common_1.Get)('list'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户足迹列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('pageNum')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], FootprintController.prototype, "findUserFootprints", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '删除单条足迹' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], FootprintController.prototype, "removeFootprint", null);
__decorate([
    (0, common_1.Post)('delete/batch'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '批量删除足迹' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FootprintController.prototype, "removeFootprints", null);
__decorate([
    (0, common_1.Delete)('clear'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '清空用户足迹' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清空成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FootprintController.prototype, "clearUserFootprints", null);
exports.FootprintController = FootprintController = FootprintController_1 = __decorate([
    (0, swagger_1.ApiTags)('用户足迹'),
    (0, common_1.Controller)('miniprogram/footprint'),
    __metadata("design:paramtypes", [footprint_service_1.FootprintService])
], FootprintController);
//# sourceMappingURL=footprint.controller.js.map