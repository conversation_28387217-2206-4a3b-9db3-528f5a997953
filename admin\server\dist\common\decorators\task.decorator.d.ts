export declare const TASK_METADATA = "task_metadata";
export interface TaskMetadata {
    name: string;
    description?: string;
}
export declare class TaskRegistry {
    private static instance;
    private tasks;
    private constructor();
    static getInstance(): TaskRegistry;
    register(target: any, methodName: string, metadata: TaskMetadata): void;
    getTasks(): {
        classOrigin: any;
        methodName: string;
        metadata: TaskMetadata;
    }[];
    getTask(name: string): {
        classOrigin: any;
        methodName: string;
        metadata: TaskMetadata;
    };
}
export declare const Task: (metadata: TaskMetadata) => MethodDecorator;
