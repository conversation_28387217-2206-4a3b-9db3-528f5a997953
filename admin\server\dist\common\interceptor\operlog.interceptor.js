"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperlogInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const core_1 = require("@nestjs/core");
const operlog_service_1 = require("../../module/monitor/operlog/operlog.service");
let OperlogInterceptor = class OperlogInterceptor {
    constructor(logService) {
        this.logService = logService;
        this.reflector = new core_1.Reflector();
    }
    intercept(context, next) {
        const { summary } = this.reflector.getAllAndOverride(`swagger/apiOperation`, [context.getHandler()]);
        const logConfig = this.reflector.get('operlog', context.getHandler());
        const handlerName = context.getHandler().name;
        const now = Date.now();
        return next
            .handle()
            .pipe((0, operators_1.map)((resultData) => {
            const costTime = Date.now() - now;
            if (resultData.code === 200) {
                this.logService.logAction({ costTime, resultData, handlerName, title: summary, businessType: logConfig?.businessType });
            }
            else {
                this.logService.logAction({ costTime, errorMsg: resultData.msg, handlerName, title: summary, businessType: logConfig?.businessType });
            }
            return resultData;
        }))
            .pipe((0, operators_1.catchError)((err) => {
            const costTime = Date.now() - now;
            this.logService.logAction({ costTime, errorMsg: err.response, handlerName, title: summary, businessType: logConfig?.businessType });
            return (0, rxjs_1.throwError)(() => err);
        }));
    }
};
exports.OperlogInterceptor = OperlogInterceptor;
exports.OperlogInterceptor = OperlogInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [operlog_service_1.OperlogService])
], OperlogInterceptor);
//# sourceMappingURL=operlog.interceptor.js.map