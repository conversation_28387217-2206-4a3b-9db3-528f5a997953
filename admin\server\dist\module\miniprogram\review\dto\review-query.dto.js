"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class ReviewQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
        this.orderBy = 'createTime';
        this.orderType = 'desc';
    }
}
exports.ReviewQueryDto = ReviewQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', required: false, default: 1 }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码最小为1' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', required: false, default: 10 }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量最小为1' }),
    (0, class_validator_1.Max)(100, { message: '每页数量最大为100' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '商品ID必须是整数' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID', required: false }),
    (0, class_validator_1.IsString)({ message: '订单ID必须是字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ReviewQueryDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最低评分', required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '评分必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '评分最小为1' }),
    (0, class_validator_1.Max)(5, { message: '评分最大为5' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "minRating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最高评分', required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '评分必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '评分最小为1' }),
    (0, class_validator_1.Max)(5, { message: '评分最大为5' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "maxRating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有图片', required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '是否有图片必须是整数' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ReviewQueryDto.prototype, "hasImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序字段', required: false, default: 'createTime' }),
    (0, class_validator_1.IsString)({ message: '排序字段必须是字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ReviewQueryDto.prototype, "orderBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序方式 asc/desc', required: false, default: 'desc' }),
    (0, class_validator_1.IsString)({ message: '排序方式必须是字符串' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ReviewQueryDto.prototype, "orderType", void 0);
//# sourceMappingURL=review-query.dto.js.map