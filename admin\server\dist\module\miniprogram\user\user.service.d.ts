import { Repository, DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AxiosService } from '../../common/axios/axios.service';
import { MiniprogramUser } from './entities/user.entity';
import { WechatPhoneAuthDto } from './dto/create-user.dto';
import { UpdateUserDto, AdminUpdateUserDto } from './dto/update-user.dto';
import { UserQueryDto } from './dto/user-profile.dto';
import { ResultData } from '../../../common/utils/result';
export declare class UserService {
    private readonly userRepository;
    private readonly configService;
    private readonly axiosService;
    private readonly dataSource;
    private readonly logger;
    constructor(userRepository: Repository<MiniprogramUser>, configService: ConfigService, axiosService: AxiosService, dataSource: DataSource);
    wechatPhoneAuth(wechatPhoneAuthDto: WechatPhoneAuthDto, ip: string): Promise<{
        user: {
            userId: number;
            openid: string;
            unionid: string;
            nickname: string;
            avatar: string;
            phone: string;
            email: string;
            gender: string;
            birthday: Date;
            region: string;
            status: string;
            remark: string;
            lastLoginTime: Date;
            createTime: Date;
            userType: string;
            vipExpireTime: Date;
            pushEnabled: string;
            tags: string;
        };
        isNewUser: boolean;
        message: string;
    }>;
    private getWechatPhoneNumber;
    private getWechatUserInfo;
    getUserProfile(userId: number): Promise<{
        userId: number;
        openid: string;
        unionid: string;
        nickname: string;
        avatar: string;
        phone: string;
        email: string;
        gender: string;
        birthday: Date;
        region: string;
        status: string;
        remark: string;
        lastLoginTime: Date;
        createTime: Date;
        userType: string;
        vipExpireTime: Date;
        pushEnabled: string;
        tags: string;
    }>;
    updateUserProfile(userId: number, updateUserDto: UpdateUserDto): Promise<{
        userId: number;
        openid: string;
        unionid: string;
        nickname: string;
        avatar: string;
        phone: string;
        email: string;
        gender: string;
        birthday: Date;
        region: string;
        status: string;
        remark: string;
        lastLoginTime: Date;
        createTime: Date;
        userType: string;
        vipExpireTime: Date;
        pushEnabled: string;
        tags: string;
    }>;
    getUserList(queryDto: UserQueryDto): Promise<{
        list: {
            userId: number;
            openid: string;
            unionid: string;
            nickname: string;
            avatar: string;
            phone: string;
            email: string;
            gender: string;
            birthday: Date;
            region: string;
            status: string;
            remark: string;
            lastLoginTime: Date;
            createTime: Date;
            userType: string;
            vipExpireTime: Date;
            pushEnabled: string;
            tags: string;
        }[];
        total: number;
        pageNum: number;
        pageSize: number;
    }>;
    adminUpdateUser(userId: number, updateDto: AdminUpdateUserDto): Promise<{
        userId: number;
        openid: string;
        unionid: string;
        nickname: string;
        avatar: string;
        phone: string;
        email: string;
        gender: string;
        birthday: Date;
        region: string;
        status: string;
        remark: string;
        lastLoginTime: Date;
        createTime: Date;
        userType: string;
        vipExpireTime: Date;
        pushEnabled: string;
        tags: string;
    }>;
    getUserStats(userId: number): Promise<ResultData>;
    private formatUserProfile;
}
