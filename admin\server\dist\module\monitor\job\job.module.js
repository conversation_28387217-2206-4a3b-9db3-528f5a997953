"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const job_service_1 = require("./job.service");
const job_controller_1 = require("./job.controller");
const job_entity_1 = require("./entities/job.entity");
const job_log_entity_1 = require("./entities/job-log.entity");
const task_service_1 = require("./task.service");
const job_log_service_1 = require("./job-log.service");
const job_log_controller_1 = require("./job-log.controller");
const backup_service_1 = require("../../backup/backup.service");
let JobModule = class JobModule {
};
exports.JobModule = JobModule;
exports.JobModule = JobModule = __decorate([
    (0, common_1.Module)({
        imports: [schedule_1.ScheduleModule.forRoot(), typeorm_1.TypeOrmModule.forFeature([job_entity_1.Job, job_log_entity_1.JobLog])],
        controllers: [job_controller_1.JobController, job_log_controller_1.JobLogController],
        providers: [job_service_1.JobService, task_service_1.TaskService, job_log_service_1.JobLogService, backup_service_1.BackupService],
        exports: [job_service_1.JobService],
    })
], JobModule);
//# sourceMappingURL=job.module.js.map