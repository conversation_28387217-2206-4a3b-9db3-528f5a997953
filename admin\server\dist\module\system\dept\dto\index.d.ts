export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare class CreateDeptDto {
    parentId: number;
    deptName: string;
    orderNum: number;
    leader?: string;
    phone?: string;
    email?: string;
    status?: string;
}
export declare class UpdateDeptDto extends CreateDeptDto {
    deptId: number;
}
export declare class ListDeptDto {
    deptName?: string;
    status?: string;
}
