"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FavoriteService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const favorite_entity_1 = require("./entities/favorite.entity");
const product_entity_1 = require("../product/entities/product.entity");
const result_1 = require("../../../common/utils/result");
const upload_entity_1 = require("../../upload/entities/upload.entity");
let FavoriteService = FavoriteService_1 = class FavoriteService {
    constructor(favoriteRepository, productRepository, uploadRepository) {
        this.favoriteRepository = favoriteRepository;
        this.productRepository = productRepository;
        this.uploadRepository = uploadRepository;
        this.logger = new common_1.Logger(FavoriteService_1.name);
    }
    async processProductImages(images) {
        try {
            if (!images) {
                return { imageUrl: '', imageInfo: null };
            }
            const firstImageUrl = images.split(',')[0];
            if (!firstImageUrl || !firstImageUrl.trim()) {
                return { imageUrl: '', imageInfo: null };
            }
            return {
                imageUrl: firstImageUrl.trim(),
                imageInfo: {
                    url: firstImageUrl.trim(),
                    fileName: firstImageUrl.split('/').pop() || 'image',
                },
            };
        }
        catch (error) {
            this.logger.error(`处理商品图片失败: ${error.message}`, error.stack);
            return { imageUrl: '', imageInfo: null };
        }
    }
    async addFavorite(createFavoriteDto) {
        try {
            const { userId, productId } = createFavoriteDto;
            const product = await this.productRepository.findOne({ where: { productId } });
            if (!product) {
                return result_1.ResultData.fail(404, '商品不存在');
            }
            const existFavorite = await this.favoriteRepository.findOne({
                where: { userId, productId, delFlag: '0' },
            });
            if (existFavorite) {
                return result_1.ResultData.fail(400, '已收藏该商品');
            }
            const favorite = this.favoriteRepository.create({
                userId,
                productId,
                createBy: userId,
            });
            const savedFavorite = await this.favoriteRepository.save(favorite);
            const { imageUrl } = await this.processProductImages(product.images);
            const result = {
                favoriteId: savedFavorite.favoriteId,
                userId: savedFavorite.userId,
                productId: savedFavorite.productId,
                favoriteTime: savedFavorite.favoriteTime || savedFavorite.createTime,
                productName: product.name,
                productPrice: product.price,
                productImage: imageUrl,
            };
            return result_1.ResultData.ok(result, '收藏成功');
        }
        catch (error) {
            this.logger.error(`添加收藏失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '添加收藏失败');
        }
    }
    async cancelFavorite(userId, favoriteId) {
        try {
            const favorite = await this.favoriteRepository.findOne({
                where: { favoriteId, userId, delFlag: '0' },
            });
            if (!favorite) {
                return result_1.ResultData.fail(404, '收藏不存在');
            }
            favorite.delFlag = '1';
            favorite.updateBy = userId;
            await this.favoriteRepository.save(favorite);
            return result_1.ResultData.ok(null, '取消收藏成功');
        }
        catch (error) {
            this.logger.error(`取消收藏失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '取消收藏失败');
        }
    }
    async getFavoriteList(userId, query) {
        try {
            const { pageNum = 1, pageSize = 10 } = query;
            const queryBuilder = this.favoriteRepository
                .createQueryBuilder('favorite')
                .leftJoinAndSelect('favorite.product', 'product')
                .where('favorite.userId = :userId', { userId })
                .andWhere('favorite.delFlag = :delFlag', { delFlag: '0' })
                .orderBy('favorite.favoriteTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [favorites, total] = await queryBuilder.getManyAndCount();
            const totalPages = Math.ceil(total / pageSize);
            const rows = await Promise.all(favorites.map(async (favorite) => {
                const product = favorite.product;
                const { imageUrl } = await this.processProductImages(product?.images || '');
                return {
                    favoriteId: favorite.favoriteId,
                    userId: favorite.userId,
                    productId: favorite.productId,
                    favoriteTime: favorite.favoriteTime || favorite.createTime,
                    productName: product ? product.name : '',
                    productPrice: product ? product.price : 0,
                    productImage: imageUrl,
                };
            }));
            const result = {
                rows,
                total,
                pageNum: Number(pageNum),
                pageSize: Number(pageSize),
                totalPages,
            };
            return result_1.ResultData.ok(result, '获取收藏列表成功');
        }
        catch (error) {
            this.logger.error(`获取收藏列表失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '获取收藏列表失败');
        }
    }
    async checkFavorite(userId, productId) {
        try {
            const favorite = await this.favoriteRepository.findOne({
                where: { userId, productId, delFlag: '0' },
            });
            const isFavorite = !!favorite;
            return result_1.ResultData.ok({ isFavorite, favoriteId: favorite?.favoriteId || '' }, '检查收藏状态成功');
        }
        catch (error) {
            this.logger.error(`检查收藏状态失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '检查收藏状态失败');
        }
    }
};
exports.FavoriteService = FavoriteService;
exports.FavoriteService = FavoriteService = FavoriteService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(favorite_entity_1.FavoriteEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(product_entity_1.ProductEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(upload_entity_1.SysUploadEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], FavoriteService);
//# sourceMappingURL=favorite.service.js.map