import { Repository, DataSource } from 'typeorm';
import { OrderEntity } from './entities/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatisticsQueryDto } from './dto/order-statistics.dto';
import { ResultData } from '../../../common/utils/result';
import { PaymentService } from '../payment/payment.service';
import { PaymentEntity } from '../payment/entities/payment.entity';
import { MiniprogramUser } from '../user/entities/user.entity';
import { NotificationGateway } from '../../notification/notification.gateway';
import { RefundRequestEntity } from './entities/refund-request.entity';
import { CreateRefundRequestDto, ProcessRefundRequestDto } from './dto/refund-request.dto';
export declare class OrderService {
    private readonly orderRepository;
    private readonly paymentRepository;
    private readonly userRepository;
    private readonly refundRequestRepository;
    private readonly dataSource;
    private readonly paymentService;
    private readonly notificationGateway;
    private readonly logger;
    constructor(orderRepository: Repository<OrderEntity>, paymentRepository: Repository<PaymentEntity>, userRepository: Repository<MiniprogramUser>, refundRequestRepository: Repository<RefundRequestEntity>, dataSource: DataSource, paymentService: PaymentService, notificationGateway: NotificationGateway);
    private generateOrderId;
    createOrder(userId: number, createOrderDto: CreateOrderDto): Promise<ResultData>;
    getUserOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    getOrderDetail(orderId: string, userId?: number): Promise<ResultData>;
    updateOrderStatus(orderId: string, updateOrderDto: UpdateOrderDto, operatorId?: number): Promise<ResultData>;
    updateOrderEvaluationStatus(orderId: string, userId: number, status: string): Promise<ResultData>;
    cancelOrder(orderId: string, userId: number, reason?: string): Promise<ResultData>;
    findAll(queryDto: OrderQueryDto): Promise<ResultData>;
    findSelfPickupOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    getOrderStats(): Promise<ResultData>;
    getDetailedOrderStatistics(query: OrderStatisticsQueryDto): Promise<ResultData>;
    private getSalesTrend;
    private getHotProducts;
    private getSummaryStatistics;
    debugCheckTables(): Promise<{
        tableExists: boolean;
        orderCounts: any;
        recentOrders: any;
        orderItemCount: any;
        timestamp: string;
    }>;
    getUserOrderStats(userId: number): Promise<ResultData>;
    private isValidStatusTransition;
    private formatAddress;
    checkGroupBuyStatus(activityId: number): Promise<ResultData>;
    private generateGroupBuyId;
    private processProductImages;
    private getGroupBuyParticipants;
    private extractGroupBuyIds;
    deliverOrder(orderId: string): Promise<ResultData>;
    refundOrder(orderId: string, refundReason?: string, refundType?: string): Promise<ResultData>;
    private processRefund;
    exportOrders(queryDto: OrderQueryDto): Promise<ResultData>;
    batchCancelOrders(orderIds: string[]): Promise<ResultData>;
    private getStatusText;
    createRefundRequest(userId: number, orderId: string, createRefundRequestDto: CreateRefundRequestDto): Promise<ResultData>;
    confirmRefundPickup(orderId: string, deliveryStaffId?: number): Promise<ResultData>;
    processRefundRequest_DEPRECATED(processDto: ProcessRefundRequestDto): Promise<ResultData>;
}
