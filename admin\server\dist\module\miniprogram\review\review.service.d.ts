import { Repository } from 'typeorm';
import { ResultData } from '../../../common/utils/result';
import { Review } from './entities/review.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { ReviewQueryDto } from './dto/review-query.dto';
import { ReviewReplyDto } from './dto/review-reply.dto';
import { OrderService } from '../order/order.service';
export declare class ReviewService {
    private readonly reviewRepository;
    private readonly orderService;
    private readonly logger;
    constructor(reviewRepository: Repository<Review>, orderService: OrderService);
    create(createReviewDto: CreateReviewDto): Promise<ResultData>;
    findAll(queryDto: ReviewQueryDto): Promise<ResultData>;
    findOne(id: number): Promise<ResultData>;
    getProductReviewStats(productId: number): Promise<ResultData>;
    reply(id: number, replyDto: ReviewReplyDto, adminId: string): Promise<ResultData>;
    remove(id: number, userId: string): Promise<ResultData>;
    like(id: number): Promise<ResultData>;
}
