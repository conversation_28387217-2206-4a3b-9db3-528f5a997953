"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListToTree = ListToTree;
exports.GetNowDate = GetNowDate;
exports.FormatDate = FormatDate;
exports.DeepClone = DeepClone;
exports.GenerateUUID = GenerateUUID;
exports.Uniq = Uniq;
exports.Paginate = Paginate;
exports.DataScopeFilter = DataScopeFilter;
exports.isObject = isObject;
exports.mergeDeep = mergeDeep;
const Lodash = __importStar(require("lodash"));
const uuid_1 = require("uuid");
const dayjs_1 = __importDefault(require("dayjs"));
const isLeapYear_1 = __importDefault(require("dayjs/plugin/isLeapYear"));
const timezone_1 = __importDefault(require("dayjs/plugin/timezone"));
const utc_1 = __importDefault(require("dayjs/plugin/utc"));
require("dayjs/locale/zh-cn");
dayjs_1.default.extend(utc_1.default);
dayjs_1.default.extend(timezone_1.default);
dayjs_1.default.extend(isLeapYear_1.default);
dayjs_1.default.locale('zh-cn');
dayjs_1.default.tz.setDefault('Asia/Beijing');
const index_1 = require("../enum/index");
function ListToTree(arr, getId, getLabel) {
    const kData = {};
    const lData = [];
    arr.forEach((m) => {
        const id = getId(m);
        const label = getLabel(m);
        const parentId = +m.parentId;
        kData[id] = {
            id,
            label,
            parentId,
            children: [],
        };
        if (parentId === 0) {
            lData.push(kData[id]);
        }
    });
    arr.forEach((m) => {
        const id = getId(m);
        const parentId = +m.parentId;
        if (parentId !== 0) {
            if (kData[parentId]) {
                kData[parentId].children.push(kData[id]);
            }
            else {
                console.warn(`Parent menuId: ${parentId} not found for child menuId: ${id}`);
            }
        }
    });
    return lData;
}
function GetNowDate() {
    return (0, dayjs_1.default)().format('YYYY-MM-DD HH:mm:ss');
}
function FormatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    return date && (0, dayjs_1.default)(date).format(format);
}
function DeepClone(obj) {
    return Lodash.cloneDeep(obj);
}
function GenerateUUID() {
    const uuid = (0, uuid_1.v4)();
    return uuid.replaceAll('-', '');
}
function Uniq(list) {
    return Lodash.uniq(list);
}
function Paginate(data, filterParam) {
    if (data.pageSize <= 0 || data.pageNum < 0) {
        return [];
    }
    let arrayData = Lodash.toArray(data.list);
    if (Object.keys(filterParam).length > 0) {
        arrayData = Lodash.filter(arrayData, (item) => {
            const arr = [];
            if (filterParam.ipaddr) {
                arr.push(Boolean(item.ipaddr.includes(filterParam.ipaddr)));
            }
            if (filterParam.userName && item.userName) {
                arr.push(Boolean(item.userName.includes(filterParam.userName)));
            }
            return !Boolean(arr.includes(false));
        });
    }
    const pageData = arrayData.slice((data.pageNum - 1) * data.pageSize, data.pageNum * data.pageSize);
    return pageData;
}
async function DataScopeFilter(entity, dataScope) {
    switch (dataScope) {
        case index_1.DataScopeEnum.DATA_SCOPE_CUSTOM:
            break;
        default:
            break;
    }
    return entity;
}
function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
}
function mergeDeep(target, ...sources) {
    if (!sources.length)
        return target;
    const source = sources.shift();
    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key])
                    Object.assign(target, { [key]: {} });
                mergeDeep(target[key], source[key]);
            }
            else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }
    return mergeDeep(target, ...sources);
}
//# sourceMappingURL=index.js.map