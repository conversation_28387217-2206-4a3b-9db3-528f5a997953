"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceTem = void 0;
const Lodash = __importStar(require("lodash"));
const gen_constant_1 = require("../../../../../common/constant/gen.constant");
const serviceTem = (options) => {
    const { BusinessName, primaryKey, businessName } = options;
    return `
import { InjectRepository } from '@nestjs/typeorm';
import { Repository ,Not ,In,Like} from 'typeorm';
import { Injectable } from '@nestjs/common';
import { ResultData } from 'src/common/utils/result';
import { Create${Lodash.upperFirst(BusinessName)}Dto, Update${Lodash.upperFirst(BusinessName)}Dto, Query${Lodash.upperFirst(BusinessName)}Dto } from './dto/${businessName}.dto';
import { ${Lodash.upperFirst(BusinessName)}Entity } from './entities/${businessName}.entity';

@Injectable()
export class ${Lodash.upperFirst(BusinessName)}Service {
constructor(
    @InjectRepository(${Lodash.upperFirst(BusinessName)}Entity)
    private readonly ${businessName}EntityRep: Repository<${Lodash.upperFirst(BusinessName)}Entity>,
) {}
    async create(create${Lodash.upperFirst(BusinessName)}Dto: Create${Lodash.upperFirst(BusinessName)}Dto) {
        const res = await this.${businessName}EntityRep.save(create${Lodash.upperFirst(BusinessName)}Dto);
        return ResultData.ok(res);
    }

    async findAll(query :Query${Lodash.upperFirst(BusinessName)}Dto ) {
        const entity = this.${businessName}EntityRep.createQueryBuilder('entity');
        entity.where({ delFlag: '0'});
        ${getListQueryStr(options)}  
        entity.select([${getListFiledSelectStr(options)}])
        if (query.orderByColumn && query.isAsc) {
          const key = query.isAsc === 'ascending' ? 'ASC' : 'DESC';
          entity.orderBy(\`entity.\${query.orderByColumn}\`, key);
        }
        entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        const [list, total] = await entity.getManyAndCount();
        
        return ResultData.ok({
            list,
            total,
        });
    }

    async findOne(id: number) {
        const res = await this.${businessName}EntityRep.findOne({
            where: {
                delFlag: '0',
                ${primaryKey}: id,
            },
        });
        return ResultData.ok(res);
    }

    async update(update${Lodash.upperFirst(BusinessName)}Dto: Update${Lodash.upperFirst(BusinessName)}Dto) {
        const res = await this.${businessName}EntityRep.update({  ${primaryKey}: update${Lodash.upperFirst(BusinessName)}Dto.${primaryKey} }, update${Lodash.upperFirst(BusinessName)}Dto);
        return ResultData.ok({value:res.affected >= 1});
    }

    async remove(${primaryKey}s: number[]) {
        const res = await this.${businessName}EntityRep.update(
            { ${primaryKey}: In(${primaryKey}s) },
            {
                delFlag: '1',
            },
        );
        return ResultData.ok({value:res.affected >= 1});
    }
}`;
};
exports.serviceTem = serviceTem;
const getListFiledSelectStr = (options) => {
    const { columns } = options;
    return columns
        .filter((column) => column.isList == '1')
        .map((column) => {
        return `"entity.${column.javaField}"`;
    })
        .join(',');
};
const getListQueryStr = (options) => {
    const { columns } = options;
    return columns
        .filter((column) => column.isQuery == '1')
        .map((column) => {
        switch (column.queryType) {
            case gen_constant_1.GenConstants.QUERY_EQ:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} = :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_NE:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} != :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_GT:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} > :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_GTE:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} >= :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_LT:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} < :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_LTE:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} <= :${column.javaField}", {${column.javaField}: query.${column.javaField}});
        }`;
            case gen_constant_1.GenConstants.QUERY_LIKE:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} LIKE :${column.javaField}", {${column.javaField}: \`%\${query.${column.javaField}}%\`});
        }`;
            case gen_constant_1.GenConstants.QUERY_BETWEEN:
                return `if(query.${column.javaField}){
          entity.andWhere("entity.${column.javaField} BETWEEN :start AND :end", { start: query.params.beginTime, end: query.params.endTime });
        }`;
            default:
                return ``;
        }
    })
        .join('\n\t');
};
//# sourceMappingURL=service.js.map