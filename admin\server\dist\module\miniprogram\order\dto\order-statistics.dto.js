"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderStatisticsQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class OrderStatisticsQueryDto {
    constructor() {
        this.period = 'day';
        this.range = 7;
        this.topCount = 10;
    }
}
exports.OrderStatisticsQueryDto = OrderStatisticsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '统计周期', enum: ['day', 'week', 'month'], required: false, default: 'day' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrderStatisticsQueryDto.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '时间范围长度', required: false, default: 7 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], OrderStatisticsQueryDto.prototype, "range", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '热销商品排名数量', required: false, default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], OrderStatisticsQueryDto.prototype, "topCount", void 0);
//# sourceMappingURL=order-statistics.dto.js.map