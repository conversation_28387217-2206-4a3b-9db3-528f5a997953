"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const index_1 = __importDefault(require("./config/index"));
const core_1 = require("@nestjs/core");
const auth_guard_1 = require("./common/guards/auth.guard");
const permission_guard_1 = require("./common/guards/permission.guard");
const roles_guard_1 = require("./common/guards/roles.guard");
const main_module_1 = require("./module/main/main.module");
const upload_module_1 = require("./module/upload/upload.module");
const system_module_1 = require("./module/system/system.module");
const common_module_1 = require("./module/common/common.module");
const monitor_module_1 = require("./module/monitor/monitor.module");
const miniprogram_module_1 = require("./module/miniprogram/miniprogram.module");
const notification_module_1 = require("./module/notification/notification.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                cache: true,
                load: [index_1.default],
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (config) => {
                    return {
                        type: 'mysql',
                        entities: [`${__dirname}/**/*.entity{.ts,.js}`],
                        autoLoadEntities: true,
                        keepConnectionAlive: true,
                        timezone: '+08:00',
                        ...config.get('db.mysql'),
                    };
                },
            }),
            main_module_1.MainModule,
            upload_module_1.UploadModule,
            common_module_1.CommonModule,
            system_module_1.SystemModule,
            monitor_module_1.MonitorModule,
            miniprogram_module_1.MiniprogramModule,
            notification_module_1.NotificationModule,
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: auth_guard_1.JwtAuthGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: roles_guard_1.RolesGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: permission_guard_1.PermissionGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map