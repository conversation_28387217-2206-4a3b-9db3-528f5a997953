"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Review = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const user_entity_1 = require("../../user/entities/user.entity");
const product_entity_1 = require("../../product/entities/product.entity");
const order_entity_1 = require("../../order/entities/order.entity");
let Review = class Review extends base_1.BaseEntity {
};
exports.Review = Review;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'review_id', comment: '评价ID' }),
    __metadata("design:type", Number)
], Review.prototype, "reviewId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'user_id', comment: '用户ID' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", Number)
], Review.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'product_id', comment: '商品ID' }),
    (0, typeorm_1.Index)('idx_product_id'),
    __metadata("design:type", Number)
], Review.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'order_id', length: 50, comment: '订单ID' }),
    (0, typeorm_1.Index)('idx_order_id'),
    __metadata("design:type", String)
], Review.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格ID', required: false }),
    (0, typeorm_1.Column)({ type: 'int', name: 'spec_id', nullable: true, comment: '商品规格ID' }),
    __metadata("design:type", Number)
], Review.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评分 1-5' }),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'rating', comment: '评分 1-5' }),
    __metadata("design:type", Number)
], Review.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价内容' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'content', length: 1000, comment: '评价内容' }),
    __metadata("design:type", String)
], Review.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价图片，JSON数组格式', required: false }),
    (0, typeorm_1.Column)({ type: 'text', name: 'images', nullable: true, comment: '评价图片，JSON数组格式' }),
    __metadata("design:type", String)
], Review.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否匿名评价，0-否 1-是' }),
    (0, typeorm_1.Column)({ type: 'char', name: 'is_anonymous', length: 1, default: '0', comment: '是否匿名评价，0-否 1-是' }),
    __metadata("design:type", String)
], Review.prototype, "isAnonymous", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商家回复', required: false }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'reply', length: 500, nullable: true, comment: '商家回复' }),
    __metadata("design:type", String)
], Review.prototype, "reply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '回复时间', required: false }),
    (0, typeorm_1.Column)({ type: 'datetime', name: 'reply_time', nullable: true, comment: '回复时间' }),
    __metadata("design:type", Date)
], Review.prototype, "replyTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有用数量' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'like_count', default: 0, comment: '有用数量' }),
    __metadata("design:type", Number)
], Review.prototype, "likeCount", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id', referencedColumnName: 'userId' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], Review.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.ProductEntity, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id', referencedColumnName: 'productId' }),
    __metadata("design:type", product_entity_1.ProductEntity)
], Review.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => order_entity_1.OrderEntity, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'order_id', referencedColumnName: 'orderId' }),
    __metadata("design:type", order_entity_1.OrderEntity)
], Review.prototype, "order", void 0);
exports.Review = Review = __decorate([
    (0, typeorm_1.Entity)('mini_review')
], Review);
//# sourceMappingURL=review.entity.js.map