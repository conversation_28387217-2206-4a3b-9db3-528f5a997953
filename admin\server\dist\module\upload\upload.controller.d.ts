import { UploadService } from './upload.service';
import { ChunkFileDto, ChunkMergeFileDto } from './dto/index';
import { ResultData } from 'src/common/utils/result';
export declare class UploadController {
    private readonly uploadService;
    constructor(uploadService: UploadService);
    singleFileUpload(file: Express.Multer.File): Promise<ResultData>;
    getChunkUploadId(): Promise<ResultData>;
    chunkFileUpload(file: Express.Multer.File, body: ChunkFileDto): Promise<ResultData>;
    chunkMergeFile(body: ChunkMergeFileDto): Promise<ResultData>;
    getChunkUploadResult(query: {
        uploadId: string;
    }): Promise<ResultData>;
    getAuthorization(query: {
        key: string;
    }): Promise<ResultData>;
    deleteFile(fileUrl: string): Promise<ResultData>;
}
