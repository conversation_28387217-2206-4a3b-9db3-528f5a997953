"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const order_controller_1 = require("./order.controller");
const order_service_1 = require("./order.service");
const order_entity_1 = require("./entities/order.entity");
const order_item_entity_1 = require("./entities/order-item.entity");
const refund_request_entity_1 = require("./entities/refund-request.entity");
const payment_module_1 = require("../payment/payment.module");
const upload_entity_1 = require("../../upload/entities/upload.entity");
const payment_entity_1 = require("../payment/entities/payment.entity");
const user_entity_1 = require("../user/entities/user.entity");
const notification_module_1 = require("../../notification/notification.module");
let OrderModule = class OrderModule {
};
exports.OrderModule = OrderModule;
exports.OrderModule = OrderModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                order_entity_1.OrderEntity,
                order_item_entity_1.OrderItemEntity,
                refund_request_entity_1.RefundRequestEntity,
                payment_entity_1.PaymentEntity,
                user_entity_1.MiniprogramUser,
                upload_entity_1.SysUploadEntity
            ]),
            (0, common_1.forwardRef)(() => payment_module_1.PaymentModule),
            notification_module_1.NotificationModule
        ],
        controllers: [order_controller_1.OrderController],
        providers: [order_service_1.OrderService],
        exports: [order_service_1.OrderService],
    })
], OrderModule);
//# sourceMappingURL=order.module.js.map