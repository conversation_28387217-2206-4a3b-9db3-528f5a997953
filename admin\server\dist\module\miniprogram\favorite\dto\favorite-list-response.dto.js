"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteListResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const favorite_response_dto_1 = require("./favorite-response.dto");
class FavoriteListResponseDto {
}
exports.FavoriteListResponseDto = FavoriteListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收藏列表', type: [favorite_response_dto_1.FavoriteResponseDto] }),
    __metadata("design:type", Array)
], FavoriteListResponseDto.prototype, "rows", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总记录数', example: 100 }),
    __metadata("design:type", Number)
], FavoriteListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], FavoriteListResponseDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], FavoriteListResponseDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 10 }),
    __metadata("design:type", Number)
], FavoriteListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=favorite-list-response.dto.js.map