"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("./user.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const user_profile_dto_1 = require("./dto/user-profile.dto");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("../../../module/system/user/user.decorator");
const common_2 = require("@nestjs/common");
let UserController = UserController_1 = class UserController {
    constructor(userService) {
        this.userService = userService;
        this.logger = new common_2.Logger(UserController_1.name);
    }
    async wechatPhoneAuth(wechatPhoneAuthDto, req) {
        try {
            console.log('微信授权手机号接口开始，请求数据:', JSON.stringify(wechatPhoneAuthDto));
            const ip = req.ip || req.connection.remoteAddress || '';
            console.log('客户端IP:', ip);
            const result = await this.userService.wechatPhoneAuth(wechatPhoneAuthDto, ip);
            console.log('服务处理成功，返回数据:', JSON.stringify(result));
            return result_1.ResultData.ok({
                user: result.user,
                isNewUser: result.isNewUser,
            }, result.message);
        }
        catch (error) {
            console.error('微信授权手机号接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async getUserProfile(userId) {
        const result = await this.userService.getUserProfile(userId);
        return result_1.ResultData.ok(result, '获取成功');
    }
    async updateUserProfile(userId, updateUserDto) {
        const result = await this.userService.updateUserProfile(userId, updateUserDto);
        return result_1.ResultData.ok(result, '更新成功');
    }
    async getUserList(queryDto) {
        const result = await this.userService.getUserList(queryDto);
        return result_1.ResultData.ok(result, '获取成功');
    }
    async adminUpdateUser(userId, updateDto) {
        const result = await this.userService.adminUpdateUser(userId, updateDto);
        return result_1.ResultData.ok(result, '更新成功');
    }
    async getUserStats(userId) {
        this.logger.log(`获取用户统计信息: userId=${userId}`);
        return await this.userService.getUserStats(userId);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Post)('wechat-phone-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '微信授权手机号登录/注册',
        description: '通过微信手机号快速验证组件获取用户手机号，实现一键登录/注册功能。支持与微信登录结合使用。',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '登录/注册成功',
        type: user_profile_dto_1.UserProfileDto,
        schema: {
            example: {
                code: 200,
                msg: '登录成功',
                data: {
                    user: {
                        userId: 1,
                        nickname: '张三',
                        avatar: 'https://example.com/avatar.jpg',
                        phone: '13812345678',
                        gender: '1',
                    },
                    isNewUser: false,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.WechatPhoneAuthDto, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "wechatPhoneAuth", null);
__decorate([
    (0, common_1.Get)('profile/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户资料' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: user_profile_dto_1.UserProfileDto }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserProfile", null);
__decorate([
    (0, common_1.Put)('profile/:userId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '更新用户资料' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_profile_dto_1.UserProfileDto }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUserProfile", null);
__decorate([
    (0, common_1.Get)('admin/list'),
    (0, swagger_1.ApiOperation)({ summary: '管理员获取用户列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_profile_dto_1.UserQueryDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserList", null);
__decorate([
    (0, common_1.Put)('admin/:userId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '管理员更新用户信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: user_profile_dto_1.UserProfileDto }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_user_dto_1.AdminUpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "adminUpdateUser", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户统计信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserStats", null);
exports.UserController = UserController = UserController_1 = __decorate([
    (0, swagger_1.ApiTags)('小程序用户管理'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, common_1.Controller)('miniprogram/user'),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
//# sourceMappingURL=user.controller.js.map