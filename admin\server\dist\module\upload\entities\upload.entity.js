"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysUploadEntity = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../../common/entities/base");
let SysUploadEntity = class SysUploadEntity extends base_1.BaseEntity {
};
exports.SysUploadEntity = SysUploadEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', name: 'upload_id', comment: '任务Id' }),
    __metadata("design:type", String)
], SysUploadEntity.prototype, "uploadId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        comment: '文件大小',
        name: 'size',
    }),
    __metadata("design:type", Number)
], SysUploadEntity.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        comment: '文件路径',
        name: 'file_name',
    }),
    __metadata("design:type", String)
], SysUploadEntity.prototype, "fileName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        comment: '文件名',
        name: 'new_file_name',
    }),
    __metadata("design:type", String)
], SysUploadEntity.prototype, "newFileName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        comment: '文件地址',
        name: 'url',
    }),
    __metadata("design:type", String)
], SysUploadEntity.prototype, "url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        comment: '拓展名',
        nullable: true,
        name: 'ext',
    }),
    __metadata("design:type", String)
], SysUploadEntity.prototype, "ext", void 0);
exports.SysUploadEntity = SysUploadEntity = __decorate([
    (0, typeorm_1.Entity)('sys_upload', {
        comment: '文件上传记录',
    })
], SysUploadEntity);
//# sourceMappingURL=upload.entity.js.map