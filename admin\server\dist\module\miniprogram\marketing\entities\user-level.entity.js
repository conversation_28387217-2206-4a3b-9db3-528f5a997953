"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLevel = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
let UserLevel = class UserLevel extends base_1.BaseEntity {
};
exports.UserLevel = UserLevel;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级ID', example: 1 }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'level_id', type: 'int', comment: '等级ID主键' }),
    __metadata("design:type", Number)
], UserLevel.prototype, "levelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称', example: '普通会员' }),
    (0, typeorm_1.Column)({ name: 'level_name', type: 'varchar', length: 50, comment: '等级名称' }),
    __metadata("design:type", String)
], UserLevel.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级值', example: 1 }),
    (0, typeorm_1.Index)('idx_level_value'),
    (0, typeorm_1.Column)({ name: 'level_value', type: 'int', comment: '等级值，从1开始' }),
    __metadata("design:type", Number)
], UserLevel.prototype, "levelValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所需积分', example: 0 }),
    (0, typeorm_1.Column)({ name: 'required_points', type: 'int', comment: '达到该等级所需积分' }),
    __metadata("design:type", Number)
], UserLevel.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣率', example: 1.0 }),
    (0, typeorm_1.Column)({ name: 'discount_rate', type: 'decimal', precision: 3, scale: 2, default: 1.0, comment: '会员折扣率(0.1-1.0)' }),
    __metadata("design:type", Number)
], UserLevel.prototype, "discountRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '积分倍率', example: 1.0 }),
    (0, typeorm_1.Column)({ name: 'points_rate', type: 'decimal', precision: 3, scale: 2, default: 1.0, comment: '积分获取倍率' }),
    __metadata("design:type", Number)
], UserLevel.prototype, "pointsRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级图标', example: 'level1.png' }),
    (0, typeorm_1.Column)({ name: 'icon', type: 'varchar', length: 255, nullable: true, comment: '等级图标URL' }),
    __metadata("design:type", String)
], UserLevel.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级描述', example: '初级会员等级' }),
    (0, typeorm_1.Column)({ name: 'description', type: 'varchar', length: 255, nullable: true, comment: '等级描述' }),
    __metadata("design:type", String)
], UserLevel.prototype, "description", void 0);
exports.UserLevel = UserLevel = __decorate([
    (0, typeorm_1.Entity)({ name: 'user_levels' })
], UserLevel);
//# sourceMappingURL=user-level.entity.js.map