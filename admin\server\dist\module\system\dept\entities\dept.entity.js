"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysDeptEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let SysDeptEntity = class SysDeptEntity extends base_1.BaseEntity {
};
exports.SysDeptEntity = SysDeptEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '部门ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'dept_id', comment: '部门ID' }),
    __metadata("design:type", Number)
], SysDeptEntity.prototype, "deptId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '父部门ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'parent_id', default: 0, comment: '父部门ID' }),
    __metadata("design:type", Number)
], SysDeptEntity.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'ancestors', length: 50, default: '0', comment: '祖级列表' }),
    __metadata("design:type", String)
], SysDeptEntity.prototype, "ancestors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dept_name', length: 30, comment: '部门名称' }),
    __metadata("design:type", String)
], SysDeptEntity.prototype, "deptName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'order_num', default: 0, comment: '显示顺序' }),
    __metadata("design:type", Number)
], SysDeptEntity.prototype, "orderNum", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'leader', length: 20, comment: '负责人' }),
    __metadata("design:type", String)
], SysDeptEntity.prototype, "leader", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '联系电话' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'phone', default: '', length: 11, comment: '联系电话' }),
    __metadata("design:type", String)
], SysDeptEntity.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '邮箱' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'email', length: 50, default: '', comment: '邮箱' }),
    __metadata("design:type", String)
], SysDeptEntity.prototype, "email", void 0);
exports.SysDeptEntity = SysDeptEntity = __decorate([
    (0, typeorm_1.Entity)('sys_dept', {
        comment: '部门表',
    })
], SysDeptEntity);
//# sourceMappingURL=dept.entity.js.map