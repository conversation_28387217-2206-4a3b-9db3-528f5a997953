export declare class OrderStatisticsQueryDto {
    period?: 'day' | 'week' | 'month';
    range?: number;
    topCount?: number;
}
export interface SalesStatisticsItem {
    date: string;
    sales: number;
    orderCount: number;
}
export interface HotProductItem {
    productId: number;
    productName: string;
    categoryName: string;
    totalSales: number;
    totalQuantity: number;
    orderCount: number;
    productImage: string;
}
export interface OrderStatisticsResponseDto {
    salesTrend: SalesStatisticsItem[];
    hotProducts: HotProductItem[];
    summary: {
        totalSales: number;
        totalOrders: number;
        completedOrders: number;
        pendingOrders: number;
        cancelledOrders: number;
        salesGrowth: number;
        ordersGrowth: number;
        completedGrowth: number;
    };
}
