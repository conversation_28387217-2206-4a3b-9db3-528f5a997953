"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.genTableCloumnUpdate = exports.CreateGenTableCloumnDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateGenTableCloumnDto {
}
exports.CreateGenTableCloumnDto = CreateGenTableCloumnDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '归属表编号' }),
    __metadata("design:type", Number)
], CreateGenTableCloumnDto.prototype, "tableId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '创建人' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "createBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列类型' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "columnType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列描述' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "columnComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '列名称' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "columnName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: 'JAVA字段名' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "javaField", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: 'JAVA类型' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "javaType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '查询方式（等于、不等于、大于、小于、范围）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "queryType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否为插入字段（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isInsert", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "htmlType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否编辑字段（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isEdit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否列表字段（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isList", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否查询字段（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isQuery", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否主键（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isPk", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否自增（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isIncrement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '是否必填（1是）' }),
    __metadata("design:type", String)
], CreateGenTableCloumnDto.prototype, "isRequired", void 0);
class genTableCloumnUpdate {
}
exports.genTableCloumnUpdate = genTableCloumnUpdate;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], genTableCloumnUpdate.prototype, "columnId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "columnComment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "javaType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "javaField", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "isInsert", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "isEdit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "isList", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "isQuery", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "queryType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "isRequired", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "htmlType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], genTableCloumnUpdate.prototype, "dictType", void 0);
//# sourceMappingURL=create-genTableCloumn-dto.js.map