"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const favorite_service_1 = require("./favorite.service");
const dto_1 = require("./dto");
const auth_guard_1 = require("../../../common/guards/auth.guard");
const user_decorator_1 = require("../../system/user/user.decorator");
const result_1 = require("../../../common/utils/result");
let FavoriteController = class FavoriteController {
    constructor(favoriteService) {
        this.favoriteService = favoriteService;
    }
    async addFavorite(createFavoriteDto) {
        return await this.favoriteService.addFavorite(createFavoriteDto);
    }
    async cancelFavorite(userId, favoriteId) {
        return await this.favoriteService.cancelFavorite(userId, favoriteId);
    }
    async getFavoriteList(query) {
        return await this.favoriteService.getFavoriteList(query.userId, query);
    }
    async checkFavorite(userId, productId) {
        return await this.favoriteService.checkFavorite(userId, productId);
    }
};
exports.FavoriteController = FavoriteController;
__decorate([
    (0, common_1.Post)(),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '添加收藏', description: '用户添加商品收藏' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateFavoriteDto]),
    __metadata("design:returntype", Promise)
], FavoriteController.prototype, "addFavorite", null);
__decorate([
    (0, common_1.Delete)(':favoriteId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '取消收藏', description: '用户取消商品收藏' }),
    (0, swagger_1.ApiParam)({ name: 'favoriteId', description: '收藏ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)('userId')),
    __param(1, (0, common_1.Param)('favoriteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], FavoriteController.prototype, "cancelFavorite", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取收藏列表', description: '获取用户收藏的商品列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.FavoriteQueryDto]),
    __metadata("design:returntype", Promise)
], FavoriteController.prototype, "getFavoriteList", null);
__decorate([
    (0, common_1.Post)('check/:productId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '检查收藏状态', description: '检查用户是否已收藏指定商品' }),
    (0, swagger_1.ApiParam)({ name: 'productId', description: '商品ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查成功', type: result_1.ResultData }),
    __param(0, (0, common_1.Body)('userId')),
    __param(1, (0, common_1.Param)('productId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], FavoriteController.prototype, "checkFavorite", null);
exports.FavoriteController = FavoriteController = __decorate([
    (0, swagger_1.ApiTags)('收藏管理'),
    (0, common_1.Controller)('miniprogram/favorite'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [favorite_service_1.FavoriteService])
], FavoriteController);
//# sourceMappingURL=favorite.controller.js.map