"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const export_1 = require("../../../common/utils/export");
const config_entity_1 = require("./entities/config.entity");
const redis_service_1 = require("../../common/redis/redis.service");
const index_1 = require("../../../common/enum/index");
const redis_decorator_1 = require("../../../common/decorators/redis.decorator");
let ConfigService = class ConfigService {
    constructor(sysConfigEntityRep, redisService) {
        this.sysConfigEntityRep = sysConfigEntityRep;
        this.redisService = redisService;
    }
    async create(createConfigDto) {
        await this.sysConfigEntityRep.save(createConfigDto);
        return result_1.ResultData.ok();
    }
    async findAll(query) {
        const entity = this.sysConfigEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.configName) {
            entity.andWhere(`entity.configName LIKE "%${query.configName}%"`);
        }
        if (query.configKey) {
            entity.andWhere(`entity.configKey LIKE "%${query.configKey}%"`);
        }
        if (query.configType) {
            entity.andWhere('entity.configType = :configType', { configType: query.configType });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOne(configId) {
        const data = await this.sysConfigEntityRep.findOne({
            where: {
                configId: configId,
            },
        });
        return result_1.ResultData.ok(data);
    }
    async findOneByConfigKey(configKey) {
        const data = await this.getConfigValue(configKey);
        return result_1.ResultData.ok(data);
    }
    async getConfigValue(configKey) {
        const data = await this.sysConfigEntityRep.findOne({ where: { configKey: configKey } });
        return data.configValue;
    }
    async update(updateConfigDto) {
        await this.sysConfigEntityRep.update({
            configId: updateConfigDto.configId,
        }, updateConfigDto);
        return result_1.ResultData.ok();
    }
    async remove(configIds) {
        const list = await this.sysConfigEntityRep.find({
            where: {
                configId: (0, typeorm_2.In)(configIds),
                delFlag: '0',
            },
            select: ['configType', 'configKey'],
        });
        const item = list.find((item) => item.configType === 'Y');
        if (item) {
            return result_1.ResultData.fail(500, `内置参数【${item.configKey}】不能删除`);
        }
        const data = await this.sysConfigEntityRep.update({ configId: (0, typeorm_2.In)(configIds) }, {
            delFlag: '1',
        });
        return result_1.ResultData.ok(data);
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAll(body);
        const options = {
            sheetName: '参数管理',
            data: list.data.list,
            header: [
                { title: '参数主键', dataIndex: 'configId' },
                { title: '参数名称', dataIndex: 'configName' },
                { title: '参数键名', dataIndex: 'configKey' },
                { title: '参数键值', dataIndex: 'configValue' },
                { title: '系统内置', dataIndex: 'configType' },
            ],
            dictMap: {
                configType: {
                    Y: '是',
                    N: '否',
                },
            },
        };
        (0, export_1.ExportTable)(options, res);
    }
    async resetConfigCache() {
        await this.clearConfigCache();
        await this.loadingConfigCache();
        return result_1.ResultData.ok();
    }
    async clearConfigCache() { }
    async loadingConfigCache() {
        const entity = this.sysConfigEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        const list = await entity.getMany();
        list.forEach((item) => {
            if (item.configKey) {
                this.redisService.set(`${index_1.CacheEnum.SYS_CONFIG_KEY}${item.configKey}`, item.configValue);
            }
        });
    }
};
exports.ConfigService = ConfigService;
__decorate([
    (0, redis_decorator_1.Cacheable)(index_1.CacheEnum.SYS_CONFIG_KEY, '{configKey}'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ConfigService.prototype, "getConfigValue", null);
__decorate([
    (0, redis_decorator_1.CacheEvict)(index_1.CacheEnum.SYS_CONFIG_KEY, '*'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConfigService.prototype, "clearConfigCache", null);
exports.ConfigService = ConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(config_entity_1.SysConfigEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        redis_service_1.RedisService])
], ConfigService);
//# sourceMappingURL=config.service.js.map