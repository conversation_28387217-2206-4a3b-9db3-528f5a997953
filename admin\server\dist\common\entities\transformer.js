"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.blobJSONTransformer = void 0;
const core_1 = require("@xobj/core");
exports.blobJSONTransformer = {
    to: (value) => (value ? Buffer.from((0, core_1.encode)(value)) : undefined),
    from: (value) => {
        if (value?.length)
            return (0, core_1.decode)(new Uint8Array(value).buffer);
        return null;
    },
};
//# sourceMappingURL=transformer.js.map