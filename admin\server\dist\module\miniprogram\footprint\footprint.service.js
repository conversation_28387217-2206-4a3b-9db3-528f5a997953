"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FootprintService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FootprintService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const footprint_entity_1 = require("./entities/footprint.entity");
const product_entity_1 = require("../product/entities/product.entity");
const result_1 = require("../../../common/utils/result");
const upload_entity_1 = require("../../upload/entities/upload.entity");
let FootprintService = FootprintService_1 = class FootprintService {
    constructor(footprintRepository, productRepository, uploadRepository) {
        this.footprintRepository = footprintRepository;
        this.productRepository = productRepository;
        this.uploadRepository = uploadRepository;
        this.logger = new common_1.Logger(FootprintService_1.name);
    }
    async recordFootprint(userId, productId) {
        if (!userId || !productId) {
            return;
        }
        try {
            const product = await this.productRepository.findOne({
                where: { productId },
            });
            if (!product) {
                return;
            }
            const existingFootprint = await this.footprintRepository.findOne({
                where: { userId, productId },
            });
            if (existingFootprint) {
                await this.footprintRepository.update({ footprintId: existingFootprint.footprintId }, { updateTime: new Date() });
            }
            else {
                const footprint = new footprint_entity_1.Footprint();
                footprint.userId = userId;
                footprint.productId = productId;
                await this.footprintRepository.save(footprint);
            }
        }
        catch (error) {
            this.logger.error(`记录用户足迹失败: ${error.message}`, error.stack);
        }
    }
    async processProductImages(images) {
        try {
            if (!images) {
                return { imageUrl: '', imageInfo: null };
            }
            const firstImageUrl = images.split(',')[0];
            if (!firstImageUrl || !firstImageUrl.trim()) {
                return { imageUrl: '', imageInfo: null };
            }
            return {
                imageUrl: firstImageUrl.trim(),
                imageInfo: {
                    url: firstImageUrl.trim(),
                    fileName: firstImageUrl.split('/').pop() || 'image',
                },
            };
        }
        catch (error) {
            this.logger.error(`处理商品图片失败: ${error.message}`, error.stack);
            return { imageUrl: '', imageInfo: null };
        }
    }
    async findUserFootprints(userId, pageNum = 1, pageSize = 10) {
        try {
            if (!userId) {
                return result_1.ResultData.fail(400, '用户ID不能为空');
            }
            const queryBuilder = this.footprintRepository
                .createQueryBuilder('footprint')
                .leftJoinAndSelect('footprint.product', 'product')
                .where('footprint.userId = :userId', { userId })
                .orderBy('footprint.updateTime', 'DESC')
                .skip((pageNum - 1) * pageSize)
                .take(pageSize);
            const [footprints, total] = await queryBuilder.getManyAndCount();
            const rows = await Promise.all(footprints.map(async (footprint) => {
                let productImageUrl = '';
                if (footprint.product && footprint.product.images) {
                    const { imageUrl } = await this.processProductImages(footprint.product.images);
                    productImageUrl = imageUrl;
                }
                return {
                    footprintId: footprint.footprintId,
                    userId: footprint.userId,
                    productId: footprint.productId,
                    createTime: footprint.createTime,
                    updateTime: footprint.updateTime,
                    product: footprint.product
                        ? {
                            productId: footprint.product.productId,
                            productName: footprint.product.name,
                            price: footprint.product.price,
                            mainImage: productImageUrl,
                            originalImage: footprint.product.images,
                            sales: footprint.product.salesCount,
                        }
                        : null,
                };
            }));
            const result = {
                rows,
                total,
                pageNum: Number(pageNum),
                pageSize: Number(pageSize),
                totalPages: Math.ceil(total / pageSize),
            };
            this.logger.log(`查询用户足迹成功: userId=${userId}, total=${total}`);
            return result_1.ResultData.ok(result, '查询成功');
        }
        catch (error) {
            this.logger.error(`查询用户足迹失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '查询用户足迹失败');
        }
    }
    async removeFootprints(userId, footprintIds) {
        try {
            if (!userId || !footprintIds || footprintIds.length === 0) {
                return result_1.ResultData.fail(400, '参数错误');
            }
            const deleteResult = await this.footprintRepository.delete({
                userId,
                footprintId: (0, typeorm_2.In)(footprintIds),
            });
            return result_1.ResultData.ok({ affected: deleteResult.affected || 0 }, '删除成功');
        }
        catch (error) {
            this.logger.error(`删除用户足迹失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '删除用户足迹失败');
        }
    }
    async clearUserFootprints(userId) {
        try {
            if (!userId) {
                return result_1.ResultData.fail(400, '用户ID不能为空');
            }
            const deleteResult = await this.footprintRepository.delete({ userId });
            return result_1.ResultData.ok({ affected: deleteResult.affected || 0 }, '清空成功');
        }
        catch (error) {
            this.logger.error(`清空用户足迹失败: ${error.message}`, error.stack);
            return result_1.ResultData.fail(500, '清空用户足迹失败');
        }
    }
};
exports.FootprintService = FootprintService;
exports.FootprintService = FootprintService = FootprintService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(footprint_entity_1.Footprint)),
    __param(1, (0, typeorm_1.InjectRepository)(product_entity_1.ProductEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(upload_entity_1.SysUploadEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], FootprintService);
//# sourceMappingURL=footprint.service.js.map