export declare enum CouponType {
    AMOUNT = "1",
    DISCOUNT = "2",
    NO_THRESHOLD = "3"
}
export declare enum CouponStatus {
    INACTIVE = "0",
    ACTIVE = "1"
}
export declare enum UserCouponStatus {
    UNUSED = "0",
    USED = "1",
    EXPIRED = "2"
}
export declare enum CouponDistributeType {
    PUBLIC = "1",
    PRIVATE = "2"
}
export interface ICouponDiscount {
    calculateDiscount(totalAmount: number): {
        finalAmount: number;
        discountAmount: number;
    };
}
export interface ICouponValidator {
    validate(userId: number, couponId: number, totalAmount: number): Promise<boolean>;
}
