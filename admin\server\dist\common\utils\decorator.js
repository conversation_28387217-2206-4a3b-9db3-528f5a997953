"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paramsKeyFormat = paramsKeyFormat;
exports.paramsKeyGetObj = paramsKeyGetObj;
const lodash_1 = require("lodash");
function getArgs(func) {
    const funcString = func.toString();
    return funcString.slice(funcString.indexOf('(') + 1, funcString.indexOf(')')).match(/([^\s,]+)/g);
}
const stringFormat = (str, callback) => {
    return str.replace(/\{([^}]+)\}/g, (word, key) => callback(key));
};
function paramsKeyFormat(func, formatKey, args) {
    const originMethodArgs = getArgs(func);
    const paramsMap = {};
    originMethodArgs?.forEach((arg, index) => {
        paramsMap[arg] = args[index];
    });
    let isNotGet = false;
    const key = stringFormat(formatKey, (key) => {
        const str = (0, lodash_1.get)(paramsMap, key);
        if (!str)
            isNotGet = true;
        return str;
    });
    if (isNotGet) {
        return null;
    }
    return key;
}
function paramsKeyGetObj(func, formatKey, args) {
    const originMethodArgs = getArgs(func);
    const paramsMap = {};
    originMethodArgs?.forEach((arg, index) => {
        paramsMap[arg] = args[index];
    });
    const obj = (0, lodash_1.get)(paramsMap, formatKey);
    if (typeof obj === 'object')
        return obj;
    if (args[0] && typeof args[0] === 'object')
        return args[0];
    return null;
}
//# sourceMappingURL=decorator.js.map