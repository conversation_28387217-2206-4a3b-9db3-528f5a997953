"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const nestjs_redis_1 = require("@songkeys/nestjs-redis");
const common_1 = require("@nestjs/common");
const ioredis_1 = __importDefault(require("ioredis"));
let RedisService = class RedisService {
    constructor(client) {
        this.client = client;
    }
    getClient() {
        return this.client;
    }
    async getInfo() {
        const rawInfo = await this.client.info();
        const lines = rawInfo.split('\r\n');
        const parsedInfo = {};
        lines.forEach((line) => {
            const [key, value] = line.split(':');
            parsedInfo[key?.trim()] = value?.trim();
        });
        return parsedInfo;
    }
    async skipFind(data) {
        const rawInfo = await this.client.lrange(data.key, (data.pageNum - 1) * data.pageSize, data.pageNum * data.pageSize);
        return rawInfo;
    }
    async getDbSize() {
        return await this.client.dbsize();
    }
    async commandStats() {
        const rawInfo = await this.client.info('commandstats');
        const lines = rawInfo.split('\r\n');
        const commandStats = [];
        lines.forEach((line) => {
            const [key, value] = line.split(':');
            if (key && value) {
                commandStats.push({
                    name: key?.trim()?.replaceAll('cmdstat_', ''),
                    value: +value?.trim()?.split(',')[0]?.split('=')[1],
                });
            }
        });
        return commandStats;
    }
    async set(key, val, ttl) {
        const data = JSON.stringify(val);
        if (!ttl)
            return await this.client.set(key, data);
        return await this.client.set(key, data, 'PX', ttl);
    }
    async mget(keys) {
        if (!keys)
            return null;
        const list = await this.client.mget(keys);
        return list.map((item) => JSON.parse(item));
    }
    async get(key) {
        if (!key || key === '*')
            return null;
        const res = await this.client.get(key);
        return JSON.parse(res);
    }
    async del(keys) {
        if (!keys || keys === '*')
            return 0;
        if (typeof keys === 'string')
            keys = [keys];
        return await this.client.del(...keys);
    }
    async ttl(key) {
        if (!key)
            return null;
        return await this.client.ttl(key);
    }
    async keys(key) {
        return await this.client.keys(key);
    }
    async hset(key, field, value) {
        if (!key || !field)
            return null;
        return await this.client.hset(key, field, value);
    }
    async hmset(key, data, expire) {
        if (!key || !data)
            return 0;
        const result = await this.client.hmset(key, data);
        if (expire) {
            await this.client.expire(key, expire);
        }
        return result;
    }
    async hget(key, field) {
        if (!key || !field)
            return 0;
        return await this.client.hget(key, field);
    }
    async hvals(key) {
        if (!key)
            return [];
        return await this.client.hvals(key);
    }
    async hGetAll(key) {
        return await this.client.hgetall(key);
    }
    async hdel(key, fields) {
        if (!key || fields.length === 0)
            return 0;
        return await this.client.hdel(key, ...fields);
    }
    async hdelAll(key) {
        if (!key)
            return 0;
        const fields = await this.client.hkeys(key);
        if (fields.length === 0)
            return 0;
        return await this.hdel(key, fields);
    }
    async lLength(key) {
        if (!key)
            return 0;
        return await this.client.llen(key);
    }
    async lSet(key, index, val) {
        if (!key || index < 0)
            return null;
        return await this.client.lset(key, index, val);
    }
    async lIndex(key, index) {
        if (!key || index < 0)
            return null;
        return await this.client.lindex(key, index);
    }
    async lRange(key, start, stop) {
        if (!key)
            return null;
        return await this.client.lrange(key, start, stop);
    }
    async lLeftPush(key, ...val) {
        if (!key)
            return 0;
        return await this.client.lpush(key, ...val);
    }
    async lLeftPushIfPresent(key, ...val) {
        if (!key)
            return 0;
        return await this.client.lpushx(key, ...val);
    }
    async lLeftInsert(key, pivot, val) {
        if (!key || !pivot)
            return 0;
        return await this.client.linsert(key, 'BEFORE', pivot, val);
    }
    async lRightInsert(key, pivot, val) {
        if (!key || !pivot)
            return 0;
        return await this.client.linsert(key, 'AFTER', pivot, val);
    }
    async lRightPush(key, ...val) {
        if (!key)
            return 0;
        return await this.client.lpush(key, ...val);
    }
    async lRightPushIfPresent(key, ...val) {
        if (!key)
            return 0;
        return await this.client.rpushx(key, ...val);
    }
    async lLeftPop(key) {
        if (!key)
            return null;
        const result = await this.client.blpop(key);
        return result.length > 0 ? result[0] : null;
    }
    async lRightPop(key) {
        if (!key)
            return null;
        const result = await this.client.brpop(key);
        return result.length > 0 ? result[0] : null;
    }
    async lTrim(key, start, stop) {
        if (!key)
            return null;
        return await this.client.ltrim(key, start, stop);
    }
    async lRemove(key, count, val) {
        if (!key)
            return 0;
        return await this.client.lrem(key, count, val);
    }
    async lPoplPush(sourceKey, destinationKey, timeout) {
        if (!sourceKey || !destinationKey)
            return null;
        return await this.client.brpoplpush(sourceKey, destinationKey, timeout);
    }
    async reset() {
        const keys = await this.client.keys('*');
        return this.client.del(keys);
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, nestjs_redis_1.InjectRedis)()),
    __metadata("design:paramtypes", [ioredis_1.default])
], RedisService);
//# sourceMappingURL=redis.service.js.map