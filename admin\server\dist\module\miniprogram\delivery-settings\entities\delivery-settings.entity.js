"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliverySettings = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let DeliverySettings = class DeliverySettings {
};
exports.DeliverySettings = DeliverySettings;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送设置ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '配送设置ID' }),
    __metadata("design:type", Number)
], DeliverySettings.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '超市名称', example: '初鲜果味超市' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        comment: '超市名称',
        name: 'store_name'
    }),
    __metadata("design:type", String)
], DeliverySettings.prototype, "storeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '纬度', example: 22.5329 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 10,
        scale: 7,
        comment: '纬度'
    }),
    __metadata("design:type", Number)
], DeliverySettings.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '经度', example: 114.0544 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 10,
        scale: 7,
        comment: '经度'
    }),
    __metadata("design:type", Number)
], DeliverySettings.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送半径(km)', example: 5 }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 5,
        scale: 2,
        comment: '配送半径(km)',
        name: 'delivery_radius'
    }),
    __metadata("design:type", Number)
], DeliverySettings.prototype, "deliveryRadius", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: 1 }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '是否启用：1启用，0禁用',
        name: 'is_active'
    }),
    __metadata("design:type", Number)
], DeliverySettings.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    (0, typeorm_1.CreateDateColumn)({
        comment: '创建时间',
        name: 'create_time'
    }),
    __metadata("design:type", Date)
], DeliverySettings.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    (0, typeorm_1.UpdateDateColumn)({
        comment: '更新时间',
        name: 'update_time'
    }),
    __metadata("design:type", Date)
], DeliverySettings.prototype, "updateTime", void 0);
exports.DeliverySettings = DeliverySettings = __decorate([
    (0, typeorm_1.Entity)('delivery_settings', {
        comment: '配送设置表',
    })
], DeliverySettings);
//# sourceMappingURL=delivery-settings.entity.js.map