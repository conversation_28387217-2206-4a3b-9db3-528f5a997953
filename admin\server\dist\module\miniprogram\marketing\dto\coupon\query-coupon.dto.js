"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryCouponDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const dto_1 = require("../../../../../common/dto");
class QueryCouponDto extends dto_1.PagingDto {
}
exports.QueryCouponDto = QueryCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券名称', example: '满100减10', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '优惠券名称必须是字符串' }),
    __metadata("design:type", String)
], QueryCouponDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券类型 1满减券 2折扣券 3无门槛券', example: '1', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '优惠券类型必须是字符串' }),
    __metadata("design:type", String)
], QueryCouponDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠券状态 0无效 1有效', example: '1', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '优惠券状态必须是字符串' }),
    __metadata("design:type", String)
], QueryCouponDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', example: '2024-07-01 00:00:00', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)({ message: '开始时间格式不正确' }),
    __metadata("design:type", Date)
], QueryCouponDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', example: '2024-07-31 23:59:59', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)({ message: '结束时间格式不正确' }),
    __metadata("design:type", Date)
], QueryCouponDto.prototype, "endTime", void 0);
//# sourceMappingURL=query-coupon.dto.js.map