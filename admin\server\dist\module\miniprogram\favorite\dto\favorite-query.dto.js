"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class FavoriteQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.FavoriteQueryDto = FavoriteQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: '1001', required: true }),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    __metadata("design:type", String)
], FavoriteQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], FavoriteQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], FavoriteQueryDto.prototype, "pageSize", void 0);
//# sourceMappingURL=favorite-query.dto.js.map