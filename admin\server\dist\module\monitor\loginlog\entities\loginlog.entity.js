"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitorLoginlogEntity = void 0;
const typeorm_1 = require("typeorm");
const base_1 = require("../../../../common/entities/base");
let MonitorLoginlogEntity = class MonitorLoginlogEntity extends base_1.BaseStatusEntity {
};
exports.MonitorLoginlogEntity = MonitorLoginlogEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'info_id', comment: '访问ID' }),
    __metadata("design:type", Number)
], MonitorLoginlogEntity.prototype, "infoId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'user_name', length: 50, default: '', comment: '用户账号' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "userName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'ipaddr', length: 128, default: '', comment: '登录IP地址' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "ipaddr", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'login_location', length: 255, default: '', comment: '登录地点' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "loginLocation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'browser', length: 50, default: '', comment: '浏览器类型' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "browser", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'os', length: 50, default: '', comment: '操作系统' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "os", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp', name: 'login_time', comment: '访问时间' }),
    __metadata("design:type", Date)
], MonitorLoginlogEntity.prototype, "loginTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'msg', length: 255, default: '', comment: '提示消息' }),
    __metadata("design:type", String)
], MonitorLoginlogEntity.prototype, "msg", void 0);
exports.MonitorLoginlogEntity = MonitorLoginlogEntity = __decorate([
    (0, typeorm_1.Entity)('sys_logininfor', {
        comment: '系统访问记录',
    })
], MonitorLoginlogEntity);
//# sourceMappingURL=loginlog.entity.js.map