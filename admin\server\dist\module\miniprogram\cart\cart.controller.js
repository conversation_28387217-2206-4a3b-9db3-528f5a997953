"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CartController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cart_service_1 = require("./cart.service");
const add_to_cart_dto_1 = require("./dto/add-to-cart.dto");
const update_cart_dto_1 = require("./dto/update-cart.dto");
const cart_query_dto_1 = require("./dto/cart-query.dto");
const auth_guard_1 = require("../../../common/guards/auth.guard");
const user_decorator_1 = require("../../system/user/user.decorator");
let CartController = CartController_1 = class CartController {
    constructor(cartService) {
        this.cartService = cartService;
        this.logger = new common_1.Logger(CartController_1.name);
    }
    async addToCart(userId, addToCartDto) {
        this.logger.log(`添加商品到购物车请求: UserId=${userId}, Data=${JSON.stringify(addToCartDto)}`);
        return await this.cartService.addToCart(userId, addToCartDto);
    }
    async getCartItems(userId) {
        this.logger.log(`获取购物车列表请求: UserId=${userId}`);
        return await this.cartService.getCartItems(userId);
    }
    async updateCartItem(userId, cartId, updateCartDto) {
        this.logger.log(`更新购物车请求: UserId=${userId}, CartId=${cartId}, Data=${JSON.stringify(updateCartDto)}`);
        return await this.cartService.updateCartItem(userId, cartId, updateCartDto);
    }
    async removeFromCart(userId, cartId) {
        this.logger.log(`删除购物车商品请求: UserId=${userId}, CartId=${cartId}`);
        return await this.cartService.removeFromCart(userId, cartId);
    }
    async clearCart(userId) {
        this.logger.log(`清空购物车请求: UserId=${userId}`);
        return await this.cartService.clearCart(userId);
    }
    async getCartCount(userId) {
        this.logger.log(`获取购物车统计请求: UserId=${userId}`);
        return await this.cartService.getCartCount(userId);
    }
    async findAll(queryDto) {
        this.logger.log(`管理员查询购物车列表请求: ${JSON.stringify(queryDto)}`);
        return await this.cartService.findAll(queryDto);
    }
    async batchRemove(body) {
        this.logger.log(`批量删除购物车项请求: CartIds=${body.cartIds.join(',')}`);
        return await this.cartService.batchRemove(body.cartIds);
    }
};
exports.CartController = CartController;
__decorate([
    (0, common_1.Post)(':userId/add'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '添加商品到购物车（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '添加成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, add_to_cart_dto_1.AddToCartDto]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "addToCart", null);
__decorate([
    (0, common_1.Get)(':userId/items'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户购物车列表（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "getCartItems", null);
__decorate([
    (0, common_1.Put)(':userId/items/:cartId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新购物车商品数量（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('cartId', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, update_cart_dto_1.UpdateCartDto]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "updateCartItem", null);
__decorate([
    (0, common_1.Delete)(':userId/items/:cartId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '删除购物车商品（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('cartId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "removeFromCart", null);
__decorate([
    (0, common_1.Delete)(':userId/clear'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '清空用户购物车（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清空成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "clearCart", null);
__decorate([
    (0, common_1.Get)(':userId/count'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取购物车统计信息（小程序端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "getCartCount", null);
__decorate([
    (0, common_1.Get)('admin/list'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询购物车列表（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cart_query_dto_1.CartQueryDto]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "findAll", null);
__decorate([
    (0, common_1.Delete)('admin/batch'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '批量删除购物车项（管理端）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CartController.prototype, "batchRemove", null);
exports.CartController = CartController = CartController_1 = __decorate([
    (0, swagger_1.ApiTags)('购物车管理'),
    (0, common_1.Controller)('miniprogram/cart'),
    (0, common_1.UseGuards)(auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [cart_service_1.CartService])
], CartController);
//# sourceMappingURL=cart.controller.js.map