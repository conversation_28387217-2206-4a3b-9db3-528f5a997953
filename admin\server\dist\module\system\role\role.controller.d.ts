import { RoleService } from './role.service';
import { Response } from 'express';
import { CreateRoleDto, UpdateRoleDto, ListRoleDto, ChangeStatusDto, AuthUserCancelDto, AuthUserCancelAllDto, AuthUserSelectAllDto } from './dto/index';
import { AllocatedListDto } from '../user/dto/index';
import { UserService } from '../user/user.service';
import { UserDto } from 'src/module/system/user/user.decorator';
export declare class RoleController {
    private readonly roleService;
    private readonly userService;
    constructor(roleService: RoleService, userService: UserService);
    create(createRoleDto: CreateRoleDto): Promise<import("../../../common/utils/result").ResultData>;
    findAll(query: ListRoleDto, user: UserDto): Promise<import("../../../common/utils/result").ResultData>;
    deptTree(id: string): Promise<import("../../../common/utils/result").ResultData>;
    findOne(id: string): Promise<import("../../../common/utils/result").ResultData>;
    update(updateRoleDto: UpdateRoleDto): Promise<import("../../../common/utils/result").ResultData>;
    dataScope(updateRoleDto: UpdateRoleDto): Promise<import("../../../common/utils/result").ResultData>;
    changeStatus(changeStatusDto: ChangeStatusDto): Promise<import("../../../common/utils/result").ResultData>;
    remove(ids: string): Promise<import("../../../common/utils/result").ResultData>;
    authUserAllocatedList(query: AllocatedListDto): Promise<import("../../../common/utils/result").ResultData>;
    authUserUnAllocatedList(query: AllocatedListDto): Promise<import("../../../common/utils/result").ResultData>;
    authUserCancel(body: AuthUserCancelDto): Promise<import("../../../common/utils/result").ResultData>;
    authUserCancelAll(body: AuthUserCancelAllDto): Promise<import("../../../common/utils/result").ResultData>;
    authUserSelectAll(body: AuthUserSelectAllDto): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListRoleDto): Promise<void>;
}
