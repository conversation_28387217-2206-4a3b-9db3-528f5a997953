import { Repository } from 'typeorm';
import { UserAddress } from './entities/address.entity';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto, AddressQueryDto, DeliveryRangeDto } from './dto/update-address.dto';
import { DeliverySettingsService } from '../delivery-settings/delivery-settings.service';
interface AddressQueryParams {
    pageNum: number;
    pageSize: number;
    userId?: number;
    receiverName?: string;
    receiverPhone?: string;
    addressName?: string;
}
export declare class AddressService {
    private readonly addressRepository;
    private readonly deliverySettingsService;
    constructor(addressRepository: Repository<UserAddress>, deliverySettingsService: DeliverySettingsService);
    create(userId: number, createAddressDto: CreateAddressDto): Promise<UserAddress>;
    findByUserId(userId: number, queryDto?: AddressQueryDto): Promise<UserAddress[]>;
    findById(addressId: number, userId?: number): Promise<UserAddress>;
    update(addressId: number, userId: number, updateAddressDto: UpdateAddressDto): Promise<UserAddress>;
    setDefault(addressId: number, userId: number): Promise<UserAddress>;
    remove(addressId: number, userId: number): Promise<void>;
    getDefaultAddress(userId: number): Promise<UserAddress | null>;
    private clearDefaultAddress;
    getAddressStats(userId: number): Promise<any>;
    checkDeliveryRange(deliveryRangeDto: DeliveryRangeDto): Promise<{
        inRange: boolean;
        message?: string;
        distance?: number;
    }>;
    private calculateDistance;
    private deg2rad;
    findAll(queryParams: AddressQueryParams): Promise<[UserAddress[], number]>;
    adminUpdate(addressId: number, updateAddressDto: UpdateAddressDto): Promise<UserAddress>;
    adminRemove(addressId: number): Promise<void>;
    getAdminAddressStats(): Promise<any>;
}
export {};
