"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliverySettingsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const delivery_settings_service_1 = require("./delivery-settings.service");
const delivery_settings_controller_1 = require("./delivery-settings.controller");
const delivery_settings_entity_1 = require("./entities/delivery-settings.entity");
const delivery_time_slot_entity_1 = require("./entities/delivery-time-slot.entity");
let DeliverySettingsModule = class DeliverySettingsModule {
};
exports.DeliverySettingsModule = DeliverySettingsModule;
exports.DeliverySettingsModule = DeliverySettingsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([delivery_settings_entity_1.DeliverySettings, delivery_time_slot_entity_1.DeliveryTimeSlot])],
        controllers: [delivery_settings_controller_1.DeliverySettingsController],
        providers: [delivery_settings_service_1.DeliverySettingsService],
        exports: [delivery_settings_service_1.DeliverySettingsService],
    })
], DeliverySettingsModule);
//# sourceMappingURL=delivery-settings.module.js.map