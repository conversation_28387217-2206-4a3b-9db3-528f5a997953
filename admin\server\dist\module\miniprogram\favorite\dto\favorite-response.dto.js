"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FavoriteResponseDto {
}
exports.FavoriteResponseDto = FavoriteResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收藏ID', example: 1 }),
    __metadata("design:type", Number)
], FavoriteResponseDto.prototype, "favoriteId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    __metadata("design:type", String)
], FavoriteResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    __metadata("design:type", Number)
], FavoriteResponseDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '收藏时间', example: '2023-01-01 12:00:00' }),
    __metadata("design:type", Date)
], FavoriteResponseDto.prototype, "favoriteTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称', example: '有机苹果' }),
    __metadata("design:type", String)
], FavoriteResponseDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品价格', example: 12.5 }),
    __metadata("design:type", Number)
], FavoriteResponseDto.prototype, "productPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品图片', example: 'apple.jpg' }),
    __metadata("design:type", String)
], FavoriteResponseDto.prototype, "productImage", void 0);
//# sourceMappingURL=favorite-response.dto.js.map