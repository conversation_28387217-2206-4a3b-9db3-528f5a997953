import { Repository, FindManyOptions } from 'typeorm';
import { Response } from 'express';
import { ResultData } from 'src/common/utils/result';
import { SysRoleEntity } from './entities/role.entity';
import { SysRoleWithMenuEntity } from './entities/role-width-menu.entity';
import { SysRoleWithDeptEntity } from './entities/role-width-dept.entity';
import { SysDeptEntity } from '../dept/entities/dept.entity';
import { MenuService } from '../menu/menu.service';
import { CreateRoleDto, UpdateRoleDto, ListRoleDto, ChangeStatusDto } from './dto/index';
export declare class RoleService {
    private readonly sysRoleEntityRep;
    private readonly sysRoleWithMenuEntityRep;
    private readonly sysRoleWithDeptEntityRep;
    private readonly sysDeptEntityRep;
    private readonly menuService;
    constructor(sysRoleEntityRep: Repository<SysRoleEntity>, sysRoleWithMenuEntityRep: Repository<SysRoleWithMenuEntity>, sysRoleWithDeptEntityRep: Repository<SysRoleWithDeptEntity>, sysDeptEntityRep: Repository<SysDeptEntity>, menuService: MenuService);
    create(createRoleDto: CreateRoleDto): Promise<ResultData>;
    findAll(query: ListRoleDto): Promise<ResultData>;
    findOne(roleId: number): Promise<ResultData>;
    update(updateRoleDto: UpdateRoleDto): Promise<ResultData>;
    dataScope(updateRoleDto: UpdateRoleDto): Promise<ResultData>;
    changeStatus(changeStatusDto: ChangeStatusDto): Promise<ResultData>;
    remove(roleIds: number[]): Promise<ResultData>;
    deptTree(roleId: number): Promise<ResultData>;
    findRoles(where: FindManyOptions<SysRoleEntity>): Promise<SysRoleEntity[]>;
    getPermissionsByRoleIds(roleIds: number[]): Promise<import("../menu/entities/menu.entity").SysMenuEntity[] | {
        perms: string;
    }[]>;
    findRoleWithDeptIds(roleId: number): Promise<number[]>;
    export(res: Response, body: ListRoleDto): Promise<void>;
}
