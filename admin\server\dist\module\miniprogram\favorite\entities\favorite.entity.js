"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FavoriteEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
const user_entity_1 = require("../../user/entities/user.entity");
const product_entity_1 = require("../../product/entities/product.entity");
let FavoriteEntity = class FavoriteEntity extends base_1.BaseEntity {
};
exports.FavoriteEntity = FavoriteEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '收藏ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'favorite_id', comment: '收藏ID' }),
    __metadata("design:type", Number)
], FavoriteEntity.prototype, "favoriteId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '用户ID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'user_id', comment: '用户ID' }),
    (0, typeorm_1.Index)('idx_user_id'),
    __metadata("design:type", String)
], FavoriteEntity.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, description: '商品ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'product_id', comment: '商品ID' }),
    (0, typeorm_1.Index)('idx_product_id'),
    __metadata("design:type", Number)
], FavoriteEntity.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date, description: '收藏时间' }),
    (0, typeorm_1.Column)({ type: 'datetime', name: 'favorite_time', default: () => 'CURRENT_TIMESTAMP', comment: '收藏时间' }),
    __metadata("design:type", Date)
], FavoriteEntity.prototype, "favoriteTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.MiniprogramUser, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.MiniprogramUser)
], FavoriteEntity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => product_entity_1.ProductEntity, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id' }),
    __metadata("design:type", product_entity_1.ProductEntity)
], FavoriteEntity.prototype, "product", void 0);
exports.FavoriteEntity = FavoriteEntity = __decorate([
    (0, typeorm_1.Entity)('user_favorites', { comment: '用户收藏商品表' })
], FavoriteEntity);
//# sourceMappingURL=favorite.entity.js.map