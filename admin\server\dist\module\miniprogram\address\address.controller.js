"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const address_service_1 = require("./address.service");
const create_address_dto_1 = require("./dto/create-address.dto");
const update_address_dto_1 = require("./dto/update-address.dto");
const address_entity_1 = require("./entities/address.entity");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("../../system/user/user.decorator");
let AddressController = class AddressController {
    constructor(addressService) {
        this.addressService = addressService;
    }
    async create(userId, createAddressDto) {
        try {
            console.log(`用户 ${userId} 创建地址接口开始，请求数据:`, JSON.stringify(createAddressDto));
            const result = await this.addressService.create(userId, createAddressDto);
            console.log('地址创建成功，返回数据:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '地址创建成功');
        }
        catch (error) {
            console.error('创建地址接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async findByUserId(userId, queryDto) {
        try {
            console.log(`获取用户 ${userId} 地址列表接口开始，查询条件:`, JSON.stringify(queryDto));
            const result = await this.addressService.findByUserId(userId, queryDto);
            console.log(`地址列表获取成功，共 ${result.length} 条`);
            return result_1.ResultData.ok(result, '获取成功');
        }
        catch (error) {
            console.error('获取地址列表接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async findById(userId, addressId) {
        try {
            console.log(`获取地址详情接口开始，用户ID: ${userId}，地址ID: ${addressId}`);
            const result = await this.addressService.findById(addressId, userId);
            console.log('地址详情获取成功:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '获取成功');
        }
        catch (error) {
            console.error('获取地址详情接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async update(userId, addressId, updateAddressDto) {
        try {
            console.log(`更新地址接口开始，用户ID: ${userId}，地址ID: ${addressId}，更新数据:`, JSON.stringify(updateAddressDto));
            const result = await this.addressService.update(addressId, userId, updateAddressDto);
            console.log('地址更新成功:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '更新成功');
        }
        catch (error) {
            console.error('更新地址接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async setDefault(userId, addressId) {
        try {
            console.log(`设置默认地址接口开始，用户ID: ${userId}，地址ID: ${addressId}`);
            const result = await this.addressService.setDefault(addressId, userId);
            console.log('默认地址设置成功:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '设置成功');
        }
        catch (error) {
            console.error('设置默认地址接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async remove(userId, addressId) {
        try {
            console.log(`删除地址接口开始，用户ID: ${userId}，地址ID: ${addressId}`);
            await this.addressService.remove(addressId, userId);
            console.log('地址删除成功');
            return result_1.ResultData.ok(null, '删除成功');
        }
        catch (error) {
            console.error('删除地址接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async getDefaultAddress(userId) {
        try {
            console.log(`获取默认地址接口开始，用户ID: ${userId}`);
            const result = await this.addressService.getDefaultAddress(userId);
            console.log('默认地址获取成功:', JSON.stringify(result));
            return result_1.ResultData.ok(result, result ? '获取成功' : '暂无默认地址');
        }
        catch (error) {
            console.error('获取默认地址接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async getAddressStats(userId) {
        try {
            console.log(`获取地址统计接口开始，用户ID: ${userId}`);
            const result = await this.addressService.getAddressStats(userId);
            console.log('地址统计获取成功:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '获取成功');
        }
        catch (error) {
            console.error('获取地址统计接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async checkDeliveryRange(deliveryRangeDto) {
        try {
            console.log('检查配送范围接口开始，请求数据:', JSON.stringify(deliveryRangeDto));
            const result = await this.addressService.checkDeliveryRange(deliveryRangeDto);
            console.log('配送范围检查结果:', JSON.stringify(result));
            return result_1.ResultData.ok(result, '检查成功');
        }
        catch (error) {
            console.error('检查配送范围接口错误:', error.message, error.stack);
            throw error;
        }
    }
    async adminList(pageNum = 1, pageSize = 10, userId, receiverName, receiverPhone, addressName) {
        try {
            console.log('管理员获取地址列表接口开始，查询条件:', { pageNum, pageSize, userId, receiverName, receiverPhone, addressName });
            const [list, total] = await this.addressService.findAll({
                pageNum,
                pageSize,
                userId,
                receiverName,
                receiverPhone,
                addressName,
            });
            console.log(`地址列表获取成功，共 ${total} 条`);
            return result_1.ResultData.ok({
                rows: list,
                total,
            }, '获取成功');
        }
        catch (error) {
            console.error('管理员获取地址列表接口错误:', error.message, error.stack);
            throw error;
        }
    }
};
exports.AddressController = AddressController;
__decorate([
    (0, common_1.Post)(':userId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: '创建地址',
        description: '用户创建新的收货地址，支持设置为默认地址',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '地址创建成功',
        type: address_entity_1.UserAddress,
        schema: {
            example: {
                code: 200,
                msg: '地址创建成功',
                data: {
                    addressId: 1,
                    userId: 1,
                    receiverName: '张三',
                    receiverPhone: '13812345678',
                    addressName: '中关村软件园',
                    detailAddress: '科技园南区科苑路XX号',
                    isDefault: 1,
                    label: '家',
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_address_dto_1.CreateAddressDto]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':userId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户地址列表',
        description: '获取用户的所有收货地址，支持按标签和默认地址筛选',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'label', description: '地址标签', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'isDefault', description: '是否默认地址', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: [address_entity_1.UserAddress],
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_address_dto_1.AddressQueryDto]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)(':userId/:addressId'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取地址详情',
        description: '根据地址ID获取具体的地址信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiParam)({ name: 'addressId', description: '地址ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: address_entity_1.UserAddress,
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('addressId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "findById", null);
__decorate([
    (0, common_1.Put)(':userId/:addressId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '更新地址',
        description: '更新指定地址的信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiParam)({ name: 'addressId', description: '地址ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        type: address_entity_1.UserAddress,
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('addressId', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, update_address_dto_1.UpdateAddressDto]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':userId/:addressId/default'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '设置默认地址',
        description: '将指定地址设置为默认地址，其他地址自动取消默认状态',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiParam)({ name: 'addressId', description: '地址ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '设置成功',
        type: address_entity_1.UserAddress,
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('addressId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "setDefault", null);
__decorate([
    (0, common_1.Delete)(':userId/:addressId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '删除地址',
        description: '删除指定的地址（软删除）',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiParam)({ name: 'addressId', description: '地址ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('addressId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':userId/default'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户默认地址',
        description: '获取用户设置的默认收货地址',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: address_entity_1.UserAddress,
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "getDefaultAddress", null);
__decorate([
    (0, common_1.Get)(':userId/stats'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取地址统计信息',
        description: '获取用户地址的统计信息，包括总数量、是否有默认地址等',
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: {
                    total: 3,
                    hasDefault: true,
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "getAddressStats", null);
__decorate([
    (0, common_1.Post)('check-delivery'),
    (0, user_decorator_1.NotRequireAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: '检查配送范围',
        description: '检查指定地址是否在配送范围内',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '检查成功',
        schema: {
            example: {
                code: 200,
                msg: '检查成功',
                data: {
                    inRange: true,
                    distance: 5.2,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_address_dto_1.DeliveryRangeDto]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "checkDeliveryRange", null);
__decorate([
    (0, common_1.Get)('admin/list'),
    (0, swagger_1.ApiOperation)({
        summary: '管理员获取地址列表',
        description: '管理员获取所有用户的地址列表，支持分页和条件筛选',
    }),
    (0, swagger_1.ApiQuery)({ name: 'pageNum', description: '页码', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '每页条数', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'receiverName', description: '收货人姓名', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'receiverPhone', description: '收货人电话', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'addressName', description: '地址名称', required: false, type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: {
                    rows: [
                        {
                            addressId: 1,
                            userId: 1,
                            receiverName: '张三',
                            receiverPhone: '13812345678',
                            addressName: '中关村软件园',
                            detailAddress: '科技园南区科苑路XX号',
                            isDefault: 1,
                            label: '家',
                            createTime: '2023-01-01 12:00:00',
                        },
                    ],
                    total: 1,
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('pageNum')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Query)('receiverName')),
    __param(4, (0, common_1.Query)('receiverPhone')),
    __param(5, (0, common_1.Query)('addressName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, String, String, String]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "adminList", null);
exports.AddressController = AddressController = __decorate([
    (0, swagger_1.ApiTags)('小程序地址管理'),
    (0, common_1.Controller)('miniprogram/address'),
    __metadata("design:paramtypes", [address_service_1.AddressService])
], AddressController);
//# sourceMappingURL=address.controller.js.map