"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysPostEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
let SysPostEntity = class SysPostEntity extends base_1.BaseEntity {
};
exports.SysPostEntity = SysPostEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '岗位ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'post_id', comment: '岗位ID' }),
    __metadata("design:type", Number)
], SysPostEntity.prototype, "postId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'post_code', length: 64, comment: '岗位编码' }),
    __metadata("design:type", String)
], SysPostEntity.prototype, "postCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'post_name', length: 50, comment: '岗位名称' }),
    __metadata("design:type", String)
], SysPostEntity.prototype, "postName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'post_sort', default: 0, comment: '显示顺序' }),
    __metadata("design:type", Number)
], SysPostEntity.prototype, "postSort", void 0);
exports.SysPostEntity = SysPostEntity = __decorate([
    (0, typeorm_1.Entity)('sys_post', {
        comment: '岗位信息表',
    })
], SysPostEntity);
//# sourceMappingURL=post.entity.js.map