
import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/miniprogram/order/admin/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细信息
export function getOrder(id) {
  return request({
    url: '/miniprogram/order/detail/' + id,
    method: 'get'
  })
}

// 订单发货
export function deliverOrder(data) {
  return request({
    url: '/miniprogram/order/deliver',
    method: 'post',
    data: data
  })
}

// 订单退款
export function refundOrder(data) {
  return request({
    url: '/miniprogram/order/refund',
    method: 'post',
    data: data
  })
}

// 导出订单数据
export function exportOrder(query) {
  return request({
    url: '/miniprogram/order/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取订单统计信息
export function getOrderStats(query) {
  return request({
    url: '/miniprogram/order/admin/stats',
    method: 'get',
    params: query
  })
}

// 批量取消订单
export function cancelOrders(orderIds) {
  return request({
    url: '/miniprogram/order/batch/cancel',
    method: 'post',
    data: { orderIds }
  })
}

// 修改订单状态
export function updateOrderStatus(data) {
  return request({
    url: '/miniprogram/order/status/update',
    method: 'put',
    data: data
  })
}

// ==================== 退款申请相关接口 ====================

// 配送员确认取货（已完成订单退款流程）
export function confirmRefundPickup(data) {
  return request({
    url: '/miniprogram/order/admin/refund/confirm-pickup',
    method: 'post',
    data: data
  })
}