"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PASSWORD_MAX_LENGTH = exports.PASSWORD_MIN_LENGTH = exports.USERNAME_MAX_LENGTH = exports.USERNAME_MIN_LENGTH = exports.NOT_UNIQUE = exports.UNIQUE = exports.INNER_LINK = exports.PARENT_VIEW = exports.LAYOUT = exports.TYPE_BUTTON = exports.TYPE_MENU = exports.TYPE_DIR = exports.NO_FRAME = exports.YES_FRAME = exports.YES = exports.DICT_NORMAL = exports.DEPT_DISABLE = exports.DEPT_NORMAL = exports.ROLE_DISABLE = exports.USER_DISABLE = exports.EXCEPTION = exports.NORMAL = exports.SYS_USER = void 0;
exports.SYS_USER = 'SYS_USER';
exports.NORMAL = '0';
exports.EXCEPTION = '1';
exports.USER_DISABLE = '1';
exports.ROLE_DISABLE = '1';
exports.DEPT_NORMAL = '0';
exports.DEPT_DISABLE = '1';
exports.DICT_NORMAL = '0';
exports.YES = 'Y';
exports.YES_FRAME = '0';
exports.NO_FRAME = '1';
exports.TYPE_DIR = 'M';
exports.TYPE_MENU = 'C';
exports.TYPE_BUTTON = 'F';
exports.LAYOUT = 'Layout';
exports.PARENT_VIEW = 'ParentView';
exports.INNER_LINK = 'InnerLink';
exports.UNIQUE = true;
exports.NOT_UNIQUE = false;
exports.USERNAME_MIN_LENGTH = 2;
exports.USERNAME_MAX_LENGTH = 20;
exports.PASSWORD_MIN_LENGTH = 5;
exports.PASSWORD_MAX_LENGTH = 20;
//# sourceMappingURL=user.constant.js.map