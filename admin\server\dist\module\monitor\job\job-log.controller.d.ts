import { JobLogService } from './job-log.service';
import { ListJobLogDto } from './dto/create-job.dto';
import { Response } from 'express';
export declare class JobLogController {
    private readonly jobLogService;
    constructor(jobLogService: JobLogService);
    list(query: ListJobLogDto): Promise<import("../../../common/utils/result").ResultData>;
    clean(): Promise<import("../../../common/utils/result").ResultData>;
    export(res: Response, body: ListJobLogDto): Promise<void>;
}
