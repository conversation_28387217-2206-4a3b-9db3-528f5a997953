"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderItemEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const order_entity_1 = require("./order.entity");
let OrderItemEntity = class OrderItemEntity {
};
exports.OrderItemEntity = OrderItemEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单商品ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'item_id', comment: '订单商品ID主键' }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "itemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'order_id', length: 32, comment: '订单ID外键' }),
    (0, typeorm_1.Index)('idx_order_id'),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'product_id', comment: '商品ID外键' }),
    (0, typeorm_1.Index)('idx_product_id'),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'product_name', length: 255, nullable: true, comment: '商品名称，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品主图' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'product_image', length: 255, nullable: true, comment: '商品主图ID，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "productImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品图片列表' }),
    (0, typeorm_1.Column)({ type: 'text', name: 'product_images', nullable: true, comment: '商品图片ID列表，逗号分隔，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "productImages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品分类ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'category_id', nullable: true, comment: '商品分类ID，冗余存储' }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品分类名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'category_name', length: 100, nullable: true, comment: '商品分类名称，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'spec_id', default: 0, comment: '商品规格ID，0表示默认规格' }),
    (0, typeorm_1.Index)('idx_spec_id'),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'spec_name', length: 100, nullable: true, comment: '规格名称，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "specName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格值' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'spec_value', length: 255, nullable: true, comment: '规格值，冗余存储' }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "specValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品原价' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'original_price',
        precision: 10,
        scale: 2,
        nullable: true,
        comment: '商品原价，冗余存储',
    }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "originalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品团购价' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'group_buy_price',
        precision: 10,
        scale: 2,
        nullable: true,
        comment: '商品团购价，冗余存储',
    }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "groupBuyPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品数量' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'quantity', comment: '商品数量' }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品单价' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'price',
        precision: 10,
        scale: 2,
        comment: '商品单价',
    }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品小计' }),
    (0, typeorm_1.Column)({
        type: 'decimal',
        name: 'total_price',
        precision: 10,
        scale: 2,
        comment: '商品小计',
    }),
    __metadata("design:type", Number)
], OrderItemEntity.prototype, "totalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价状态', example: '0' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'review_status',
        length: 1,
        default: '0',
        comment: '评价状态：0未评价1已评价',
    }),
    __metadata("design:type", String)
], OrderItemEntity.prototype, "reviewStatus", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => order_entity_1.OrderEntity, (order) => order.orderItems, { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'order_id', referencedColumnName: 'orderId' }),
    __metadata("design:type", order_entity_1.OrderEntity)
], OrderItemEntity.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('ProductEntity', { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'product_id', referencedColumnName: 'productId' }),
    __metadata("design:type", Object)
], OrderItemEntity.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('ProductSpecEntity', { createForeignKeyConstraints: false }),
    (0, typeorm_1.JoinColumn)({ name: 'spec_id', referencedColumnName: 'specId' }),
    __metadata("design:type", Object)
], OrderItemEntity.prototype, "spec", void 0);
exports.OrderItemEntity = OrderItemEntity = __decorate([
    (0, typeorm_1.Entity)('order_items', { comment: '订单商品表' }),
    (0, typeorm_1.Index)('idx_order_id', ['orderId']),
    (0, typeorm_1.Index)('idx_product_id', ['productId'])
], OrderItemEntity);
//# sourceMappingURL=order-item.entity.js.map