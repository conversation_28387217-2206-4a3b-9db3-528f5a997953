"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginlogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const loginlog_service_1 = require("./loginlog.service");
const index_1 = require("./dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
let LoginlogController = class LoginlogController {
    constructor(loginlogService) {
        this.loginlogService = loginlogService;
    }
    findAll(query) {
        return this.loginlogService.findAll(query);
    }
    removeAll() {
        return this.loginlogService.removeAll();
    }
    remove(ids) {
        const infoIds = ids.split(',').map((id) => id);
        return this.loginlogService.remove(infoIds);
    }
    async export(res, body) {
        return this.loginlogService.export(res, body);
    }
};
exports.LoginlogController = LoginlogController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '登录日志-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ListLoginlogDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:logininfor:list'),
    (0, common_1.Get)('/list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListLoginlogDto]),
    __metadata("design:returntype", void 0)
], LoginlogController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '登录日志-清除全部日志',
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:logininfor:remove'),
    (0, common_1.Delete)('/clean'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoginlogController.prototype, "removeAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '登录日志-删除日志',
    }),
    (0, require_premission_decorator_1.RequirePermission)('monitor:logininfor:remove'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LoginlogController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出登录日志为xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:config:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListLoginlogDto]),
    __metadata("design:returntype", Promise)
], LoginlogController.prototype, "export", null);
exports.LoginlogController = LoginlogController = __decorate([
    (0, swagger_1.ApiTags)('登录日志'),
    (0, common_1.Controller)('monitor/logininfor'),
    __metadata("design:paramtypes", [loginlog_service_1.LoginlogService])
], LoginlogController);
//# sourceMappingURL=loginlog.controller.js.map