import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare class CreateDictTypeDto {
    dictName: string;
    dictType: string;
    remark?: string;
    status?: string;
}
export declare class UpdateDictTypeDto extends CreateDictTypeDto {
    dictId: number;
}
export declare class ListDictType extends PagingDto {
    dictName?: string;
    dictType?: string;
    status?: string;
}
export declare class CreateDictDataDto {
    dictType: string;
    dictLabel: string;
    dictValue: string;
    listClass: string;
    cssClass: string;
    dictSort?: number;
    remark?: string;
    status?: string;
}
export declare class UpdateDictDataDto extends CreateDictDataDto {
    dictCode: number;
}
export declare class ListDictData extends PagingDto {
    dictLabel?: string;
    dictType?: string;
    status?: string;
}
