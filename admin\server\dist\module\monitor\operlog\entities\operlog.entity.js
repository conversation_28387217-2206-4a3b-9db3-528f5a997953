"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysOperlogEntity = void 0;
const typeorm_1 = require("typeorm");
let SysOperlogEntity = class SysOperlogEntity {
};
exports.SysOperlogEntity = SysOperlogEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'oper_id', comment: '日志主键' }),
    __metadata("design:type", Number)
], SysOperlogEntity.prototype, "operId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'title', length: 50, default: '', comment: '模块标题' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'business_type', default: 0, comment: '业务类型' }),
    __metadata("design:type", Number)
], SysOperlogEntity.prototype, "businessType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'method', length: 100, default: '', comment: '方法名称' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "method", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'request_method', length: 10, default: '', comment: '请求方式' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "requestMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'operator_type', default: 0, comment: '操作类别' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operatorType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'oper_name', length: 50, default: '', comment: '操作人员' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'dept_name', length: 50, default: '', comment: '部门名称' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "deptName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'oper_url', length: 255, default: '', comment: '请求URL' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'oper_ip', length: 255, default: '', comment: '主机地址' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'oper_location', length: 255, default: '', comment: '操作地点' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operLocation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'oper_param', length: 2000, default: '', comment: '请求参数' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "operParam", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'json_result', length: 2000, default: '', comment: '返回参数' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "jsonResult", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp', name: 'oper_time', comment: '操作时间' }),
    __metadata("design:type", Date)
], SysOperlogEntity.prototype, "operTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'status', length: 1, default: '0', comment: '登录状态' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'error_msg', length: 2000, default: '', comment: '错误消息' }),
    __metadata("design:type", String)
], SysOperlogEntity.prototype, "errorMsg", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'cost_time', default: 0, comment: '消耗时间' }),
    __metadata("design:type", Number)
], SysOperlogEntity.prototype, "costTime", void 0);
exports.SysOperlogEntity = SysOperlogEntity = __decorate([
    (0, typeorm_1.Entity)('sys_oper_log', {
        comment: '操作日志记录',
    })
], SysOperlogEntity);
//# sourceMappingURL=operlog.entity.js.map