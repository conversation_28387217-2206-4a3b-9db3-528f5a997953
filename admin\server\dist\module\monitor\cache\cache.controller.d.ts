import { CacheService } from './cache.service';
export declare class CacheController {
    private readonly cacheService;
    constructor(cacheService: CacheService);
    getInfo(): Promise<import("../../../common/utils/result").ResultData>;
    getNames(): Promise<import("../../../common/utils/result").ResultData>;
    getKeys(id: string): Promise<import("../../../common/utils/result").ResultData>;
    getValue(params: string[]): Promise<import("../../../common/utils/result").ResultData>;
    clearCacheName(cacheName: string): Promise<import("../../../common/utils/result").ResultData>;
    clearCacheKey(cacheKey: string): Promise<import("../../../common/utils/result").ResultData>;
    clearCacheAll(): Promise<import("../../../common/utils/result").ResultData>;
}
