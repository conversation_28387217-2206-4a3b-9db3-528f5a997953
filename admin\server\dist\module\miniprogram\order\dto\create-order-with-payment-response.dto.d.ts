export declare class OrderWithPaymentResponseDto {
    orderId: string;
    totalAmount: number;
    discountAmount: number;
    finalAmount: number;
    orderStatus: string;
    status?: string;
    paymentId: number;
    paymentStatus: string;
    paymentAmount: number;
    paymentMethod: string;
    paymentParams?: {
        appId: string;
        timeStamp: string;
        nonceStr: string;
        package: string;
        signType: string;
        paySign: string;
    };
    paymentInfo?: any;
    transactionId?: string;
    paymentTime?: Date;
    groupBuyId?: string;
    isInitiator?: boolean;
    createTime: Date;
}
