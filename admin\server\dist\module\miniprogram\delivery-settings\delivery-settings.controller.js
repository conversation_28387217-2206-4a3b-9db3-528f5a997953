"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DeliverySettingsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliverySettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const delivery_settings_service_1 = require("./delivery-settings.service");
const dto_1 = require("./dto");
const delivery_settings_entity_1 = require("./entities/delivery-settings.entity");
const delivery_time_slot_entity_1 = require("./entities/delivery-time-slot.entity");
let DeliverySettingsController = DeliverySettingsController_1 = class DeliverySettingsController {
    constructor(deliverySettingsService) {
        this.deliverySettingsService = deliverySettingsService;
        this.logger = new common_1.Logger(DeliverySettingsController_1.name);
    }
    async create(createDeliverySettingsDto) {
        this.logger.log(`创建配送设置接口开始，请求数据: ${JSON.stringify(createDeliverySettingsDto)}`);
        return await this.deliverySettingsService.create(createDeliverySettingsDto);
    }
    async findAll(queryDto) {
        this.logger.log(`获取配送设置列表接口开始，查询条件: ${JSON.stringify(queryDto)}`);
        return await this.deliverySettingsService.findAll(queryDto);
    }
    async update(id, updateDeliverySettingsDto) {
        this.logger.log(`更新配送设置接口开始，ID: ${id}，更新数据: ${JSON.stringify(updateDeliverySettingsDto)}`);
        return await this.deliverySettingsService.update(id, updateDeliverySettingsDto);
    }
    async remove(id) {
        this.logger.log(`删除配送设置接口开始，ID: ${id}`);
        return await this.deliverySettingsService.remove(id);
    }
    async findOne(id) {
        this.logger.log(`获取配送设置详情接口开始，ID: ${id}`);
        return await this.deliverySettingsService.findOne(id);
    }
    async getUniqueSettings() {
        this.logger.log('获取唯一配送设置接口开始');
        return await this.deliverySettingsService.getUniqueSettings();
    }
    async checkDeliveryRange(body) {
        this.logger.log(`检查配送范围接口开始，坐标: ${JSON.stringify(body)}`);
        return await this.deliverySettingsService.checkDeliveryRange(body.latitude, body.longitude);
    }
    async createTimeSlot(createDto) {
        this.logger.log(`创建配送时间段接口开始，请求数据: ${JSON.stringify(createDto)}`);
        return await this.deliverySettingsService.createTimeSlot(createDto);
    }
    async findAllTimeSlots(queryDto) {
        this.logger.log(`获取配送时间段列表接口开始，查询条件: ${JSON.stringify(queryDto)}`);
        return await this.deliverySettingsService.findAllTimeSlots(queryDto);
    }
    async findOneTimeSlot(id) {
        this.logger.log(`获取配送时间段详情接口开始，ID: ${id}`);
        return await this.deliverySettingsService.findOneTimeSlot(id);
    }
    async updateTimeSlot(id, updateDto) {
        this.logger.log(`更新配送时间段接口开始，ID: ${id}，更新数据: ${JSON.stringify(updateDto)}`);
        return await this.deliverySettingsService.updateTimeSlot(id, updateDto);
    }
    async removeTimeSlot(id) {
        this.logger.log(`删除配送时间段接口开始，ID: ${id}`);
        return await this.deliverySettingsService.removeTimeSlot(id);
    }
    async getActiveTimeSlots() {
        this.logger.log('获取启用配送时间段列表接口开始（小程序端）');
        return await this.deliverySettingsService.getActiveTimeSlots();
    }
};
exports.DeliverySettingsController = DeliverySettingsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: '创建配送设置',
        description: '创建新的配送设置，包括超市名称、经纬度、配送半径等信息',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '配送设置创建成功',
        type: delivery_settings_entity_1.DeliverySettings,
        schema: {
            example: {
                code: 200,
                msg: '配送设置创建成功',
                data: {
                    id: 1,
                    storeName: '初鲜果味超市',
                    latitude: 22.5329,
                    longitude: 114.0544,
                    deliveryRadius: 5,
                    isActive: 1,
                    createTime: '2023-01-01 12:00:00',
                    updateTime: '2023-01-01 12:00:00',
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateDeliverySettingsDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取配送设置列表',
        description: '获取配送设置列表，支持分页和条件筛选',
    }),
    (0, swagger_1.ApiQuery)({ name: 'pageNum', description: '页码', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '每页条数', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'storeName', description: '超市名称', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'isActive', description: '是否启用', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: {
                    rows: [
                        {
                            id: 1,
                            storeName: '初鲜果味超市',
                            latitude: 22.5329,
                            longitude: 114.0544,
                            deliveryRadius: 5,
                            isActive: 1,
                            createTime: '2023-01-01 12:00:00',
                            updateTime: '2023-01-01 12:00:00',
                        },
                    ],
                    total: 1,
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.DeliverySettingsQueryDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '更新配送设置',
        description: '更新指定配送设置的信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送设置ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        type: delivery_settings_entity_1.DeliverySettings,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateDeliverySettingsDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '删除配送设置',
        description: '删除指定的配送设置',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送设置ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取配送设置详情',
        description: '根据ID获取具体的配送设置信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送设置ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: delivery_settings_entity_1.DeliverySettings,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('settings/unique'),
    (0, swagger_1.ApiOperation)({
        summary: '获取唯一的配送设置',
        description: '获取系统中唯一的配送设置',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: delivery_settings_entity_1.DeliverySettings,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "getUniqueSettings", null);
__decorate([
    (0, common_1.Post)('check-range'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '检查配送范围',
        description: '检查指定坐标是否在配送范围内',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '检查成功',
        schema: {
            example: {
                code: 200,
                msg: '检查成功',
                data: {
                    inRange: true,
                    distance: 3.2,
                    storeName: '初鲜果味超市',
                    maxRadius: 5,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "checkDeliveryRange", null);
__decorate([
    (0, common_1.Post)('time-slots'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: '创建配送时间段',
        description: '创建新的配送时间段',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '配送时间段创建成功',
        type: delivery_time_slot_entity_1.DeliveryTimeSlot,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateDeliveryTimeSlotDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "createTimeSlot", null);
__decorate([
    (0, common_1.Post)('time-slots/page'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取配送时间段列表',
        description: '获取配送时间段列表，支持分页和条件筛选',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: {
                    rows: [
                        {
                            id: 1,
                            label: '上午9:00-12:00',
                            value: '10:00:00',
                            sortOrder: 1,
                            isActive: 1,
                            createTime: '2023-01-01 12:00:00',
                            updateTime: '2023-01-01 12:00:00',
                        },
                    ],
                    total: 1,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.DeliveryTimeSlotQueryDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "findAllTimeSlots", null);
__decorate([
    (0, common_1.Get)('time-slots/:id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取配送时间段详情',
        description: '根据ID获取具体的配送时间段信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送时间段ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: delivery_time_slot_entity_1.DeliveryTimeSlot,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "findOneTimeSlot", null);
__decorate([
    (0, common_1.Put)('time-slots/:id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '更新配送时间段',
        description: '更新指定配送时间段的信息',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送时间段ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        type: delivery_time_slot_entity_1.DeliveryTimeSlot,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateDeliveryTimeSlotDto]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "updateTimeSlot", null);
__decorate([
    (0, common_1.Delete)('time-slots/:id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '删除配送时间段',
        description: '删除指定的配送时间段',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配送时间段ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "removeTimeSlot", null);
__decorate([
    (0, common_1.Get)('time-slots'),
    (0, swagger_1.ApiOperation)({
        summary: '获取启用的配送时间段列表',
        description: '获取所有启用的配送时间段，按排序顺序返回（小程序端使用）',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                msg: '获取成功',
                data: [
                    {
                        id: 1,
                        label: '上午9:00-12:00',
                        value: '10:00:00',
                        sortOrder: 1,
                        isActive: 1,
                    },
                    {
                        id: 2,
                        label: '下午13:00-18:00',
                        value: '15:00:00',
                        sortOrder: 2,
                        isActive: 1,
                    },
                ],
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DeliverySettingsController.prototype, "getActiveTimeSlots", null);
exports.DeliverySettingsController = DeliverySettingsController = DeliverySettingsController_1 = __decorate([
    (0, swagger_1.ApiTags)('配送设置管理'),
    (0, common_1.Controller)('admin/delivery-settings'),
    __metadata("design:paramtypes", [delivery_settings_service_1.DeliverySettingsService])
], DeliverySettingsController);
//# sourceMappingURL=delivery-settings.controller.js.map