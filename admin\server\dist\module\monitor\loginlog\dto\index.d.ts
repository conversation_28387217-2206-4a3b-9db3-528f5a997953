import { PagingDto } from 'src/common/dto/index';
export declare enum StatusEnum {
    STATIC = "0",
    DYNAMIC = "1"
}
export declare class CreateLoginlogDto {
    ipaddr?: string;
    userName?: string;
    loginLocation?: string;
    browser?: string;
    os?: string;
    msg?: string;
    status?: string;
}
export declare class UpdateLoginlogDto extends CreateLoginlogDto {
    infoId: number;
}
export declare class ListLoginlogDto extends PagingDto {
    ipaddr?: string;
    userName?: string;
    status?: string;
}
