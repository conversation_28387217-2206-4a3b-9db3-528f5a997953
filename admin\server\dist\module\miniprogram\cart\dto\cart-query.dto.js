"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const index_1 = require("../../../../common/dto/index");
class CartQueryDto extends index_1.PagingDto {
}
exports.CartQueryDto = CartQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '用户ID必须是整数' }),
    (0, class_validator_1.IsPositive)({ message: '用户ID必须大于0' }),
    __metadata("design:type", Number)
], CartQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称搜索', required: false, example: '苹果' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CartQueryDto.prototype, "productName", void 0);
//# sourceMappingURL=cart-query.dto.js.map