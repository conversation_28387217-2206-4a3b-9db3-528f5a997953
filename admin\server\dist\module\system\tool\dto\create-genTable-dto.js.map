{"version": 3, "file": "create-genTable-dto.js", "sourceRoot": "", "sources": ["../../../../../src/module/system/tool/dto/create-genTable-dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAiJ;AACjJ,6CAA8C;AAC9C,wDAAiD;AAEjD,MAAa,iBAAiB;CA2B7B;AA3BD,8CA2BC;AAzBQ;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;;oDACzB;AAGlB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;;uDACtB;AAGrB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;;oDAC3B;AAGlB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;;sDACzB;AAGpB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;;qDAC1B;AAGnB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;;uDACxB;AAGrB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;;uDACxB;AAGrB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC;;yDACvB;AAGvB;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;;mDAC1B;AAG1B,MAAa,iBAAkB,SAAQ,iBAAiB;CAGvD;AAHD,8CAGC;AADQ;IADN,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;;kDAC1B;AAEzB,MAAa,cAAe,SAAQ,iBAAS;CAO5C;AAPD,wCAOC;AAJC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;iDACM;AAGnB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;oDACS;AAGxB,MAAa,SAAS;CAMrB;AAND,8BAMC;AADC;IAJC,IAAA,qBAAW,EAAC;QACX,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;;6CACQ;AAErB,MAAa,OAAO;CAMnB;AAND,0BAMC;AADC;IAJC,IAAA,qBAAW,EAAC;QACX,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;;yCACM;AAGnB,MAAa,YAAa,SAAQ,iBAAS;CAO1C;AAPD,oCAOC;AAJC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;gDACO;AAGpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACW;AAGxB,MAAa,cAAc;CA2C1B;AA3CD,wCA2CC;AAtCC;IAJC,IAAA,qBAAW,EAAC;QACX,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;;+CACG;AAGhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACQ;AAGnB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACW;AAGtB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACQ;AAGnB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACa;AAGxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACK;AAGhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACU;AAGrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACU;AAGrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACS;AAGpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACW;AAGtB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACW;AAGtB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACM;AAEjB;IADC,IAAA,4BAAU,GAAE;;+CACoB;AAEjC;IADC,IAAA,0BAAQ,GAAE;;kDACS"}