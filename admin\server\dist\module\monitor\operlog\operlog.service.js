"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperlogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const operlog_entity_1 = require("./entities/operlog.entity");
const core_1 = require("@nestjs/core");
const result_1 = require("../../../common/utils/result");
const axios_service_1 = require("../../common/axios/axios.service");
let OperlogService = class OperlogService {
    constructor(request, sysOperlogEntityRep, axiosService) {
        this.request = request;
        this.sysOperlogEntityRep = sysOperlogEntityRep;
        this.axiosService = axiosService;
    }
    create(createOperlogDto) {
        return 'This action adds a new operlog';
    }
    async findAll(query) {
        const entity = this.sysOperlogEntityRep.createQueryBuilder('entity');
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const orderMap = {
            descending: 'DESC',
            ascending: 'ASC',
        };
        if (query.orderByColumn && query.isAsc) {
            entity.orderBy(`entity.${query.orderByColumn}`, orderMap[query.isAsc]);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async removeAll() {
        await this.sysOperlogEntityRep.delete({ operId: (0, typeorm_1.Not)((0, typeorm_1.IsNull)()) });
        return result_1.ResultData.ok();
    }
    findOne(id) {
        return `This action returns a #${id} operlog`;
    }
    update(id, updateOperlogDto) {
        return `This action updates a #${id} operlog`;
    }
    remove(id) {
        return `This action removes a #${id} operlog`;
    }
    async logAction({ resultData, costTime, title, handlerName, errorMsg, businessType, }) {
        const { originalUrl, method, ip, body, query } = this.request;
        const { user } = this.request.user;
        const operLocation = await this.axiosService.getIpAddress(ip);
        const params = {
            title,
            method: handlerName,
            operName: user.nickName,
            deptName: user.deptName,
            operUrl: originalUrl,
            requestMethod: method.toUpperCase(),
            operIp: ip,
            costTime: costTime,
            operLocation: operLocation,
            operParam: JSON.stringify({ ...body, ...query }),
            jsonResult: JSON.stringify(resultData),
            errorMsg,
            businessType,
            operatorType: '1',
            operTime: new Date(),
            status: errorMsg ? '1' : '0',
        };
        await this.sysOperlogEntityRep.save(params);
    }
};
exports.OperlogService = OperlogService;
exports.OperlogService = OperlogService = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __param(0, (0, common_1.Inject)(core_1.REQUEST)),
    __param(1, (0, typeorm_2.InjectRepository)(operlog_entity_1.SysOperlogEntity)),
    __metadata("design:paramtypes", [Object, typeorm_1.Repository,
        axios_service_1.AxiosService])
], OperlogService);
//# sourceMappingURL=operlog.service.js.map