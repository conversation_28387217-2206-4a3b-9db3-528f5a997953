"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSpecResponseDto = exports.UpdateProductSpecDto = exports.CreateProductSpecDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class CreateProductSpecDto {
}
exports.CreateProductSpecDto = CreateProductSpecDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '规格ID必须是数字' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格名称', example: '规格1' }),
    (0, class_validator_1.IsNotEmpty)({ message: '规格名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '规格名称必须是字符串' }),
    __metadata("design:type", String)
], CreateProductSpecDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格价格', example: 15.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '规格价格不能为空' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '规格价格必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '规格价格不能为负数' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '团购价格', required: false, example: 12.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '团购价格必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '团购价格不能为负数' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "groupBuyPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格值', example: '500g/袋' }),
    (0, class_validator_1.IsNotEmpty)({ message: '规格值不能为空' }),
    (0, class_validator_1.IsString)({ message: '规格值必须是字符串' }),
    __metadata("design:type", String)
], CreateProductSpecDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '库存数量', example: 100 }),
    (0, class_validator_1.IsNotEmpty)({ message: '库存数量不能为空' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '库存数量必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '库存数量不能为负数' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "stock", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格图片', required: false, example: 'upload_id_1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '规格图片必须是字符串' }),
    __metadata("design:type", String)
], CreateProductSpecDto.prototype, "image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否默认规格：1是，0否', required: false, example: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '默认规格值必须是0或1' }),
    __metadata("design:type", Number)
], CreateProductSpecDto.prototype, "isDefault", void 0);
class UpdateProductSpecDto {
}
exports.UpdateProductSpecDto = UpdateProductSpecDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格名称', required: false, example: '规格1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '规格名称必须是字符串' }),
    __metadata("design:type", String)
], UpdateProductSpecDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格价格', required: false, example: 15.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '规格价格必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '规格价格不能为负数' }),
    __metadata("design:type", Number)
], UpdateProductSpecDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '团购价格', required: false, example: 12.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '团购价格必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '团购价格不能为负数' }),
    __metadata("design:type", Number)
], UpdateProductSpecDto.prototype, "groupBuyPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格值', required: false, example: '500g/袋' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '规格值必须是字符串' }),
    __metadata("design:type", String)
], UpdateProductSpecDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '库存数量', required: false, example: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '库存数量必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '库存数量不能为负数' }),
    __metadata("design:type", Number)
], UpdateProductSpecDto.prototype, "stock", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格图片', required: false, example: 'upload_id_1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '规格图片必须是字符串' }),
    __metadata("design:type", String)
], UpdateProductSpecDto.prototype, "image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], UpdateProductSpecDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否默认规格：1是，0否', required: false, example: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)([0, 1], { message: '默认规格值必须是0或1' }),
    __metadata("design:type", Number)
], UpdateProductSpecDto.prototype, "isDefault", void 0);
class ProductSpecResponseDto {
}
exports.ProductSpecResponseDto = ProductSpecResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格ID', example: 1 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID', example: 1 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格名称', example: '规格1' }),
    __metadata("design:type", String)
], ProductSpecResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格价格', example: 15.99 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '团购价格', example: 12.99, nullable: true }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "groupBuyPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格值', example: '500g/袋' }),
    __metadata("design:type", String)
], ProductSpecResponseDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '库存数量', example: 100 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "stock", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '规格图片', example: 'upload_id_1' }),
    __metadata("design:type", String)
], ProductSpecResponseDto.prototype, "image", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用', example: 1 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否默认规格：1是，0否', example: 0 }),
    __metadata("design:type", Number)
], ProductSpecResponseDto.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2023-01-01 12:00:00' }),
    __metadata("design:type", Date)
], ProductSpecResponseDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2023-01-01 12:00:00' }),
    __metadata("design:type", Date)
], ProductSpecResponseDto.prototype, "updateTime", void 0);
//# sourceMappingURL=product-spec.dto.js.map