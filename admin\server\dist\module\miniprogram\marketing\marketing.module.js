"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const coupon_service_1 = require("./coupon.service");
const coupon_controller_1 = require("./coupon.controller");
const coupon_entity_1 = require("./entities/coupon.entity");
const user_coupon_entity_1 = require("./entities/user-coupon.entity");
const user_entity_1 = require("../user/entities/user.entity");
const schedule_1 = require("@nestjs/schedule");
const coupon_scheduler_1 = require("./coupon.scheduler");
let MarketingModule = class MarketingModule {
};
exports.MarketingModule = MarketingModule;
exports.MarketingModule = MarketingModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([coupon_entity_1.Coupon, user_coupon_entity_1.UserCoupon, user_entity_1.MiniprogramUser]), schedule_1.ScheduleModule.forRoot()],
        providers: [coupon_service_1.CouponService, coupon_scheduler_1.CouponScheduler],
        controllers: [coupon_controller_1.CouponController],
        exports: [coupon_service_1.CouponService],
    })
], MarketingModule);
//# sourceMappingURL=marketing.module.js.map