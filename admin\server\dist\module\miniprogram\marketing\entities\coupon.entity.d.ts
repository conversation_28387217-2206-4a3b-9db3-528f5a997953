import { BaseEntity } from '../../../../common/entities/base';
import { UserCoupon } from './user-coupon.entity';
export declare class Coupon extends BaseEntity {
    id: number;
    name: string;
    type: string;
    status: string;
    description: string;
    conditionAmount: number;
    discountAmount: number;
    discountRate: number;
    goodsType: string;
    goodsIds: string;
    categoryIds: string;
    startTime: Date;
    endTime: Date;
    totalCount: number;
    usedCount: number;
    perUserLimit: number;
    distributeType: string;
    userCoupons: UserCoupon[];
}
