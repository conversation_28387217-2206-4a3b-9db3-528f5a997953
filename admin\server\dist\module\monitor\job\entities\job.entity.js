"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Job = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let Job = class Job {
};
exports.Job = Job;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'job_id', comment: '任务ID' }),
    __metadata("design:type", Number)
], Job.prototype, "jobId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务名称' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'job_name', length: 64, comment: '任务名称' }),
    __metadata("design:type", String)
], Job.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '任务组名' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'job_group', length: 64, comment: '任务组名', default: 'DEFAULT' }),
    __metadata("design:type", String)
], Job.prototype, "jobGroup", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '调用目标字符串' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'invoke_target', length: 500, comment: '调用目标字符串' }),
    __metadata("design:type", String)
], Job.prototype, "invokeTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'cron执行表达式' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'cron_expression', length: 255, comment: 'cron执行表达式', nullable: true }),
    __metadata("design:type", String)
], Job.prototype, "cronExpression", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '计划执行错误策略（1立即执行 2执行一次 3放弃执行）' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        name: 'misfire_policy',
        length: 20,
        comment: '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
        default: '3',
        nullable: true,
    }),
    __metadata("design:type", String)
], Job.prototype, "misfirePolicy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否并发执行（0允许 1禁止）' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'concurrent',
        length: 1,
        comment: '是否并发执行（0允许 1禁止）',
        default: '1',
        nullable: true,
    }),
    __metadata("design:type", String)
], Job.prototype, "concurrent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态（0正常 1暂停）' }),
    (0, typeorm_1.Column)({
        type: 'char',
        name: 'status',
        length: 1,
        comment: '状态（0正常 1暂停）',
        default: '0',
        nullable: true,
    }),
    __metadata("design:type", String)
], Job.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '创建者' }),
    (0, typeorm_1.Column)({ type: 'varchar', name: 'create_by', length: 64, default: '', comment: '创建者' }),
    __metadata("design:type", String)
], Job.prototype, "createBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'datetime', name: 'create_time', default: null, comment: '创建时间' }),
    __metadata("design:type", Date)
], Job.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'update_by', length: 64, default: '', comment: '更新者' }),
    __metadata("design:type", String)
], Job.prototype, "updateBy", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'datetime', name: 'update_time', default: null, comment: '更新时间' }),
    __metadata("design:type", Date)
], Job.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'remark', length: 500, default: null, comment: '备注' }),
    __metadata("design:type", String)
], Job.prototype, "remark", void 0);
exports.Job = Job = __decorate([
    (0, typeorm_1.Entity)('sys_job', {
        comment: '定时任务表',
    })
], Job);
//# sourceMappingURL=job.entity.js.map