"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTool = exports.NotRequireAuth = exports.User = void 0;
const common_1 = require("@nestjs/common");
exports.User = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
});
const NotRequireAuth = () => (0, common_1.SetMetadata)('notRequireAuth', true);
exports.NotRequireAuth = NotRequireAuth;
exports.UserTool = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const userName = request.user?.user?.userName;
    const injectCreate = (data) => {
        if (data.createBy) {
            return;
        }
        data.createBy = userName;
        return injectUpdate(data);
    };
    const injectUpdate = (data) => {
        if (data.updateBy) {
            return;
        }
        data.updateBy = userName;
        return data;
    };
    return { injectCreate, injectUpdate };
});
//# sourceMappingURL=user.decorator.js.map