"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BannerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const banner_service_1 = require("./banner.service");
const create_banner_dto_1 = require("./dto/create-banner.dto");
const update_banner_dto_1 = require("./dto/update-banner.dto");
const query_banner_dto_1 = require("./dto/query-banner.dto");
const result_1 = require("../../../common/utils/result");
const user_decorator_1 = require("../../../module/system/user/user.decorator");
const user_decorator_2 = require("../../../module/system/user/user.decorator");
let BannerController = class BannerController {
    constructor(bannerService) {
        this.bannerService = bannerService;
    }
    async create(createBannerDto, user) {
        const data = await this.bannerService.create(createBannerDto, user.userName);
        return result_1.ResultData.ok(data, '创建成功');
    }
    async findAll(queryParams) {
        const data = await this.bannerService.findAll(queryParams);
        return result_1.ResultData.ok(data);
    }
    async findOne(id) {
        const data = await this.bannerService.findOne(+id);
        if (!data) {
            return result_1.ResultData.fail(404, '轮播图不存在');
        }
        return result_1.ResultData.ok(data);
    }
    async update(id, updateBannerDto, user) {
        const data = await this.bannerService.update(+id, updateBannerDto, user.userName);
        if (!data) {
            return result_1.ResultData.fail(404, '轮播图不存在');
        }
        return result_1.ResultData.ok(data, '更新成功');
    }
    async remove(id, user) {
        const success = await this.bannerService.remove(+id, user.userName);
        if (!success) {
            return result_1.ResultData.fail(404, '轮播图不存在');
        }
        return result_1.ResultData.ok(null, '删除成功');
    }
    async getMiniBanners() {
        const data = await this.bannerService.getMiniBanners();
        return result_1.ResultData.ok(data);
    }
};
exports.BannerController = BannerController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建轮播图' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_banner_dto_1.CreateBannerDto, Object]),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取轮播图列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, common_1.Get)('list'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_banner_dto_1.QueryBannerDto]),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取轮播图详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '轮播图ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新轮播图' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '轮播图ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_banner_dto_1.UpdateBannerDto, Object]),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除轮播图' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '轮播图ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取小程序端轮播图列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '操作成功', type: result_1.ResultData }),
    (0, user_decorator_2.NotRequireAuth)(),
    (0, common_1.Get)('mini/list'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BannerController.prototype, "getMiniBanners", null);
exports.BannerController = BannerController = __decorate([
    (0, swagger_1.ApiTags)('小程序-轮播图管理'),
    (0, common_1.Controller)('miniprogram/banner'),
    __metadata("design:paramtypes", [banner_service_1.BannerService])
], BannerController);
//# sourceMappingURL=banner.controller.js.map