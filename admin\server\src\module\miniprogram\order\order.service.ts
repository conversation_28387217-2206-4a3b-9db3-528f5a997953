import { Injectable, Logger, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { OrderEntity } from './entities/order.entity';
import { OrderItemEntity } from './entities/order-item.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderWithPaymentResponseDto } from './dto/create-order-with-payment-response.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatisticsQueryDto, OrderStatisticsResponseDto, SalesStatisticsItem, HotProductItem } from './dto/order-statistics.dto';
import { ResultData } from '../../../common/utils/result';
import { OrderStatus, OrderType, DeliveryType } from './enum/order-status.enum';
import { SysUploadEntity } from '../../upload/entities/upload.entity';
import { MoreThanOrEqual } from 'typeorm';
import { GenerateUUID } from '../../../common/utils/index';
import { calc } from 'a-calc'; // 引入 a-calc 库
import { PaymentService } from '../payment/payment.service';
import { PaymentEntity } from '../payment/entities/payment.entity';
import { MiniprogramUser } from '../user/entities/user.entity';
import { NotificationGateway } from '../../notification/notification.gateway';
import { RefundRequestEntity } from './entities/refund-request.entity';
import { CreateRefundRequestDto, ProcessRefundRequestDto, RefundRequestStatus, RefundRequestStatusText } from './dto/refund-request.dto';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(PaymentEntity)
    private readonly paymentRepository: Repository<PaymentEntity>,
    @InjectRepository(MiniprogramUser)
    private readonly userRepository: Repository<MiniprogramUser>,
    @InjectRepository(RefundRequestEntity)
    private readonly refundRequestRepository: Repository<RefundRequestEntity>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => PaymentService))
    private readonly paymentService: PaymentService,
    private readonly notificationGateway: NotificationGateway,
  ) {}

  /**
   * 生成订单ID
   * 格式：ORD + 年月日 + 6位随机数
   */
  private generateOrderId(): string {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
    const randomStr = Math.random().toString().slice(2, 8);
    return `ORD${dateStr}${randomStr}`;
  }

  /**
   * 创建订单
   */
  async createOrder(userId: number, createOrderDto: CreateOrderDto): Promise<ResultData> {
    this.logger.log(`[DEBUG] 创建订单: UserId=${userId}, Data=${JSON.stringify(createOrderDto)}`);

    return await this.dataSource.transaction(async (manager) => {
      try {
        // 1. 验证地址信息
        this.logger.log(`[DEBUG] 验证地址信息: AddressId=${createOrderDto.addressId}`);
        const address = await manager.query('SELECT * FROM miniprogram_user_address WHERE address_id = ? AND user_id = ? AND del_flag = "0"', [createOrderDto.addressId, userId]);

        if (!address || address.length === 0) {
          throw new HttpException('收货地址不存在', HttpStatus.BAD_REQUEST);
        }

        // 2. 验证商品信息和计算总金额
        this.logger.log(`[DEBUG] 验证商品信息: ItemsCount=${createOrderDto.items.length}`);
        const orderItems = [];
        let totalAmount = 0;

        // 判断订单类型和团购信息
        let orderType = OrderType.NORMAL;
        let userGroupBuyId = null;
        let isGroupInitiator = 0;

        // 处理团购订单
        if (createOrderDto.isUserInitiatedGroupBuy) {
          // 用户发起的团购
          orderType = OrderType.GROUP_BUY;
          userGroupBuyId = this.generateGroupBuyId();
          isGroupInitiator = 1;
          this.logger.log(`[DEBUG] 用户发起的团购，生成团购ID: ${userGroupBuyId}, 设置为团购发起人`);
        } else if (createOrderDto.userGroupBuyId) {
          // 用户参与的团购
          orderType = OrderType.GROUP_BUY;
          userGroupBuyId = createOrderDto.userGroupBuyId;
          this.logger.log(`[DEBUG] 用户参与团购，团购ID: ${userGroupBuyId}`);

          // 验证团购ID是否存在
          const existingGroupBuy = await manager.query('SELECT * FROM orders WHERE user_group_buy_id = ? AND order_type = ? AND status != ? AND del_flag = "0"', [
            userGroupBuyId,
            OrderType.GROUP_BUY,
            OrderStatus.CANCELLED,
          ]);

          if (!existingGroupBuy || existingGroupBuy.length === 0) {
            throw new HttpException('参与的团购不存在或已结束', HttpStatus.BAD_REQUEST);
          }
        }

        // 是否是团购订单
        const isGroupBuy = orderType === OrderType.GROUP_BUY;

        for (const item of createOrderDto.items) {
          // 1. 先检查商品是否存在
          const product = await manager.query('SELECT product_id, name, price, status, images, category_id FROM products WHERE product_id = ? AND status = 1', [item.productId]);

          if (!product || product.length === 0) {
            throw new HttpException(`商品不存在或已下架: ID=${item.productId}`, HttpStatus.BAD_REQUEST);
          }

          // 2. 获取商品价格，默认使用商品表中的价格
          let price = parseFloat(product[0].price);

          // 3. 获取规格ID
          const specId = item.specId || 0;

          // 初始化规格名称和规格值
          let specName = '默认规格';
          let specValue = '默认';

          // 4. 检查商品规格信息
          if (specId > 0) {
            // 如果提供了规格ID，则先检查规格是否存在
            this.logger.log(`[DEBUG] 检查商品规格信息: ProductId=${item.productId}, SpecId=${specId}`);
            const specQuery = 'SELECT spec_id, name, value, price, stock, status, group_buy_price FROM product_specs WHERE spec_id = ? AND product_id = ?';
            const specs = await manager.query(specQuery, [specId, item.productId]);

            if (!specs || specs.length === 0) {
              throw new HttpException(`商品规格不存在: 商品ID=${item.productId}, 规格ID=${specId}`, HttpStatus.BAD_REQUEST);
            }

            const specInfo = specs[0];

            if (specInfo.status === 0) {
              throw new HttpException(`商品规格已禁用: 商品ID=${item.productId}, 规格=${specInfo.name}`, HttpStatus.BAD_REQUEST);
            }

            if (specInfo.stock < item.quantity) {
              throw new HttpException(`商品规格库存不足: ${product[0].name} - ${specInfo.name}, 当前库存: ${specInfo.stock}, 需要: ${item.quantity}`, HttpStatus.BAD_REQUEST);
            }

            // 使用规格价格，如果是团购则使用团购价格
            if (isGroupBuy && specInfo.group_buy_price) {
              price = parseFloat(specInfo.group_buy_price);
              this.logger.log(`[DEBUG] 使用规格团购价格: ${price}`);
            } else {
              price = parseFloat(specInfo.price);
              this.logger.log(`[DEBUG] 使用规格原价: ${price}`);
            }

            // 保存规格名称和规格值
            specName = specInfo.name || '默认规格';
            specValue = specInfo.value || '默认';
            this.logger.log(`[DEBUG] 保存规格信息: 规格名称=${specName}, 规格值=${specValue}`);

            // 更新规格库存
            await manager.query('UPDATE product_specs SET stock = stock - ?, sales_count = sales_count + ? WHERE spec_id = ?', [item.quantity, item.quantity, specId]);
          } else {
            // 对于无规格商品，应该有一个默认规格
            const defaultSpecQuery = 'SELECT spec_id, name, value, price, stock, status, group_buy_price FROM product_specs WHERE product_id = ? AND is_default = 1';
            const defaultSpecs = await manager.query(defaultSpecQuery, [item.productId]);

            if (defaultSpecs && defaultSpecs.length > 0) {
              const specInfo = defaultSpecs[0];
              if (specInfo.stock < item.quantity) {
                throw new HttpException(`商品默认规格库存不足: ${product[0].name}, 当前库存: ${specInfo.stock}, 需要: ${item.quantity}`, HttpStatus.BAD_REQUEST);
              }

              // 如果是团购则使用团购价格
              if (isGroupBuy && specInfo.group_buy_price) {
                price = parseFloat(specInfo.group_buy_price);
                this.logger.log(`[DEBUG] 使用默认规格团购价格: ${price}`);
              }

              // 保存默认规格名称和规格值
              specName = specInfo.name || '默认规格';
              specValue = specInfo.value || '默认';
              this.logger.log(`[DEBUG] 保存默认规格信息: 规格名称=${specName}, 规格值=${specValue}`);

              // 更新默认规格库存
              await manager.query('UPDATE product_specs SET stock = stock - ?, sales_count = sales_count + ? WHERE spec_id = ?', [item.quantity, item.quantity, specInfo.spec_id]);
            }
          }

          // 计算总价格和订单金额 - 使用 a-calc 进行精确计算
          const totalPrice = parseFloat(calc(`${price} * ${item.quantity} | =2`));
          totalAmount = parseFloat(calc(`${totalAmount} + ${totalPrice} | =2`));

          this.logger.log(`[DEBUG] 商品单价: ${price}元，数量: ${item.quantity}，小计: ${totalPrice}元`);

          // 获取原始价格和团购价格
          let originalPrice = parseFloat(product[0].price); // 默认使用商品原价
          let groupBuyPrice = null;

          // 如果有规格信息，则使用规格的价格
          if (specId > 0) {
            const specQuery = 'SELECT price, group_buy_price FROM product_specs WHERE spec_id = ? AND product_id = ?';
            const specPriceInfo = await manager.query(specQuery, [specId, item.productId]);
            if (specPriceInfo && specPriceInfo.length > 0) {
              originalPrice = parseFloat(specPriceInfo[0].price);
              if (specPriceInfo[0].group_buy_price) {
                groupBuyPrice = parseFloat(specPriceInfo[0].group_buy_price);
              }
            }
          }

          // 获取商品名称、图片和分类信息
          const productName = product[0].name;
          const productImages = product[0].images || '';
          const productImage = productImages ? productImages.split(',')[0] : '';
          const categoryId = product[0].category_id || null;

          // 获取分类名称
          let categoryName = '';
          if (categoryId) {
            const categoryQuery = 'SELECT name FROM product_categories WHERE category_id = ?';
            const categoryInfo = await manager.query(categoryQuery, [categoryId]);
            if (categoryInfo && categoryInfo.length > 0) {
              categoryName = categoryInfo[0].name;
            }
          }

          orderItems.push({
            productId: item.productId,
            productName: productName,
            productImage: productImage,
            productImages: productImages,
            categoryId: categoryId,
            categoryName: categoryName,
            specId: specId,
            specName: specName,
            specValue: specValue,
            quantity: item.quantity,
            price: price,
            totalPrice: totalPrice,
            originalPrice: originalPrice,
            groupBuyPrice: groupBuyPrice,
          });

          // 更新商品销量
          await manager.query('UPDATE products SET sales_count = sales_count + ? WHERE product_id = ?', [item.quantity, item.productId]);
        }

        // 4. 处理优惠券
        let discountAmount = 0;
        let userCouponId = null;

        if (createOrderDto.couponId) {
          this.logger.log(`[DEBUG] 处理优惠券: CouponId=${createOrderDto.couponId}`);

          try {
            // 查询用户优惠券信息
            const userCouponQuery = `
              SELECT uc.id, uc.user_id, uc.coupon_id, uc.status, 
                     c.type, c.condition_amount, c.discount_amount, c.discount_rate, 
                     c.goods_type, c.goods_ids, c.category_ids, c.start_time, c.end_time
              FROM t_user_coupon uc
              JOIN t_coupon c ON uc.coupon_id = c.id
              WHERE uc.user_id = ? AND uc.coupon_id = ? 
                AND uc.status = '0' AND uc.del_flag = '0'
                AND c.status = '1' AND c.del_flag = '0'
                AND c.start_time <= NOW() AND c.end_time >= NOW()
            `;

            const userCoupon = await manager.query(userCouponQuery, [userId, createOrderDto.couponId]);

            if (!userCoupon || userCoupon.length === 0) {
              throw new HttpException('优惠券不存在或已使用', HttpStatus.BAD_REQUEST);
            }

            const coupon = userCoupon[0];
            userCouponId = coupon.id;

            // 验证优惠券是否满足使用条件
            const conditionAmountInYuan = parseFloat(calc(`${coupon.condition_amount} / 100 | =2`));
            if (coupon.condition_amount > 0 && totalAmount < conditionAmountInYuan) {
              throw new HttpException(`订单金额未满足优惠券使用条件，需满${conditionAmountInYuan}元`, HttpStatus.BAD_REQUEST);
            }

            // 验证商品范围限制
            if (coupon.goods_type !== '1') {
              // 不是全部商品可用
              if (coupon.goods_type === '2' && coupon.goods_ids) {
                // 指定商品可用
                const goodsIds = coupon.goods_ids.split(',').map((id) => parseInt(id.trim()));
                const hasValidProduct = orderItems.some((item) => goodsIds.includes(item.productId));

                if (!hasValidProduct) {
                  throw new HttpException('当前订单商品不适用于此优惠券', HttpStatus.BAD_REQUEST);
                }
              } else if (coupon.goods_type === '3' && coupon.category_ids) {
                // 指定分类可用
                const categoryIds = coupon.category_ids.split(',').map((id) => parseInt(id.trim()));
                const hasValidCategory = orderItems.some((item) => item.categoryId && categoryIds.includes(item.categoryId));

                if (!hasValidCategory) {
                  throw new HttpException('当前订单商品分类不适用于此优惠券', HttpStatus.BAD_REQUEST);
                }
              }
            }

            // 计算优惠金额 - 使用 a-calc 进行精确计算
            if (coupon.type === '1') {
              // 满减券
              discountAmount = parseFloat(calc(`${coupon.discount_amount} / 100 | =2`)); // 转换为元
              this.logger.log(`[DEBUG] 使用满减券，优惠金额: ${discountAmount}元`);
            } else if (coupon.type === '2') {
              // 折扣券
              const discountRate = parseFloat(calc(`${coupon.discount_rate} / 100 | =2`)); // 转换为小数，如80/100=0.8表示8折
              discountAmount = parseFloat(calc(`${totalAmount} * (1 - ${discountRate}) | =2`)); // 保留两位小数
              this.logger.log(`[DEBUG] 使用折扣券，折扣率: ${discountRate}，优惠金额: ${discountAmount}元`);
            } else if (coupon.type === '3') {
              // 无门槛券
              discountAmount = parseFloat(calc(`${coupon.discount_amount} / 100 | =2`)); // 转换为元
              this.logger.log(`[DEBUG] 使用无门槛券，优惠金额: ${discountAmount}元`);
            }

            // 确保优惠金额不超过订单总金额
            if (discountAmount > totalAmount) {
              discountAmount = totalAmount;
              this.logger.log(`[DEBUG] 优惠金额超过订单总金额，调整为: ${discountAmount}元`);
            }

            // 添加日志记录最终价格计算
            this.logger.log(`[DEBUG] 订单总金额: ${totalAmount}元，优惠金额: ${discountAmount}元，最终金额: ${calc(`${totalAmount} - ${discountAmount} | =2`)}元`);
          } catch (error) {
            if (error instanceof HttpException) {
              throw error;
            }
            this.logger.error(`[ERROR] 处理优惠券失败: ${error.message}`, error.stack);
            throw new HttpException('处理优惠券失败', HttpStatus.INTERNAL_SERVER_ERROR);
          }
        }

        const finalAmount = parseFloat(calc(`${totalAmount} - ${discountAmount} | =2`));

        // 5. 创建订单
        const orderId = this.generateOrderId();
        this.logger.log(`[DEBUG] 生成订单ID: ${orderId}`);

        const order = new OrderEntity();
        order.orderId = orderId;
        order.userId = userId;
        order.addressId = createOrderDto.addressId;
        order.orderType = orderType;
        order.userGroupBuyId = userGroupBuyId;
        order.isGroupInitiator = isGroupInitiator;
        order.totalAmount = totalAmount;
        order.discountAmount = discountAmount;
        order.finalAmount = finalAmount;
        order.deliveryType = createOrderDto.deliveryType;
        order.deliveryTime = createOrderDto.deliveryTime ? new Date(createOrderDto.deliveryTime) : null;
        order.status = OrderStatus.PENDING_PAYMENT;
        order.createTime = new Date();
        order.updateTime = new Date();
        order.remark = createOrderDto.remark || null;

        // 设置收货人信息
        order.receiverName = address[0].receiver_name;
        order.receiverPhone = address[0].receiver_phone;
        order.receiverAddress = this.formatAddress(address[0]);

        order.createBy = userId.toString();
        order.updateBy = userId.toString();

        const savedOrder = await manager.save(OrderEntity, order);

        // 6. 创建订单商品
        this.logger.log(`[DEBUG] 创建订单商品: Count=${orderItems.length}`);
        for (const itemData of orderItems) {
          const orderItem = new OrderItemEntity();
          orderItem.orderId = orderId;
          orderItem.productId = itemData.productId;
          orderItem.productName = itemData.productName;
          orderItem.productImage = itemData.productImage;
          orderItem.productImages = itemData.productImages;
          orderItem.categoryId = itemData.categoryId;
          orderItem.categoryName = itemData.categoryName;
          orderItem.specId = itemData.specId;
          orderItem.specName = itemData.specName;
          orderItem.specValue = itemData.specValue;
          orderItem.quantity = itemData.quantity;
          orderItem.price = itemData.price;
          orderItem.totalPrice = itemData.totalPrice;
          orderItem.originalPrice = itemData.originalPrice;
          orderItem.groupBuyPrice = itemData.groupBuyPrice;

          await manager.save(OrderItemEntity, orderItem);
        }

        // 7. 处理优惠券使用
        if (userCouponId) {
          this.logger.log(`[DEBUG] 更新优惠券使用状态: UserCouponId=${userCouponId}, OrderId=${orderId}`);

          // 更新优惠券状态为已使用
          await manager.query(
            `
            UPDATE t_user_coupon 
            SET status = '1', use_time = NOW(), order_id = ?, update_time = NOW(), update_by = ? 
            WHERE id = ? AND user_id = ? AND status = '0'
          `,
            [orderId, userId.toString(), userCouponId, userId],
          );
        }

        // 8. 清空购物车中的相关商品
        // 收集所有购物车项ID
        const cartIds = createOrderDto.items.filter((item) => item.cartId).map((item) => item.cartId);

        if (cartIds.length > 0) {
          this.logger.log(`[DEBUG] 清空购物车中的相关商品，购物车项ID: ${cartIds.join(',')}`);
          try {
            await manager.query(
              `UPDATE shopping_cart SET del_flag = '1', update_time = NOW(), update_by = ? 
               WHERE user_id = ? AND cart_id IN (${cartIds.map(() => '?').join(',')})`,
              [userId.toString(), userId, ...cartIds],
            );
          } catch (error) {
            this.logger.error(`[DEBUG] 更新购物车记录失败：${error.message}`);
            // 如果更新失败，尝试直接删除
            try {
              await manager.query(`DELETE FROM shopping_cart WHERE user_id = ? AND cart_id IN (${cartIds.map(() => '?').join(',')})`, [userId, ...cartIds]);
            } catch (deleteError) {
              this.logger.error(`[DEBUG] 删除购物车记录也失败：${deleteError.message}`);
              // 继续订单创建流程，不因购物车清理失败而回滚整个事务
            }
          }
        } else {
          // 如果没有提供购物车项ID，则尝试通过商品ID和规格ID清理
          this.logger.log(`[DEBUG] 没有提供购物车项ID，尝试通过商品ID和规格ID清理购物车`);
          const productSpecs = createOrderDto.items.map((item) => ({
            productId: item.productId,
            specId: item.specId || 0,
          }));

          for (const { productId, specId } of productSpecs) {
            try {
              await manager.query(
                `UPDATE shopping_cart SET del_flag = '1', update_time = NOW(), update_by = ? 
                 WHERE user_id = ? AND product_id = ? AND spec_id = ?`,
                [userId.toString(), userId, productId, specId],
              );
            } catch (error) {
              this.logger.error(`[DEBUG] 更新购物车记录失败：${error.message}`);
              // 继续处理下一项，不中断流程
            }
          }
        }

        // 构建返回数据
        const responseData: Partial<OrderWithPaymentResponseDto> = {
          orderId: orderId,
          totalAmount: totalAmount,
          finalAmount: finalAmount,
          orderStatus: OrderStatus.PENDING_PAYMENT,
          createTime: new Date(),
        };

        // 添加团购相关信息到返回数据
        if (isGroupBuy) {
          responseData.groupBuyId = userGroupBuyId;
          responseData.isInitiator = isGroupInitiator === 1;
        }


        return ResultData.ok(responseData, '订单创建成功');
      } catch (error) {
        this.logger.error(`[ERROR] 创建订单失败: ${error.message}`, error.stack);
        if (error instanceof HttpException) {
          // 将HttpException转换为ResultData返回
          return ResultData.fail(error.getStatus(), error.message);
        }
        throw new HttpException('创建订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });
  }

  /**
   * 获取用户订单列表
   */
  async getUserOrders(queryDto: OrderQueryDto): Promise<ResultData> {
    this.logger.log(`[DEBUG] 获取用户订单列表: Query=${JSON.stringify(queryDto)}`);
    try {
      const queryBuilder = this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.orderItems', 'orderItems')
        .leftJoinAndSelect('orderItems.product', 'product') // 联查商品信息
        .leftJoinAndSelect('order.user', 'user') // 添加用户信息关联
        .leftJoinAndSelect('order.address', 'address') // 添加地址信息关联以获取经纬度
        .leftJoin('refund_requests', 'refundRequest', 'refundRequest.order_id = order.orderId') // 左连接退款申请表
        .addSelect([
          'refundRequest.status AS refund_status',
          'refundRequest.refund_reason AS refund_reason',
          'refundRequest.request_time AS refund_request_time'
        ])
        .where('order.delFlag = :delFlag', { delFlag: '0' });

      // 用户ID筛选
      if (queryDto.userId) {
        queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
      }

      // 配送方式筛选
      if (queryDto.deliveryType) {
        queryBuilder.andWhere('order.deliveryType = :deliveryType', { deliveryType: queryDto.deliveryType });
      }

      // 订单状态筛选
      if (queryDto.status) {
        // -1查询所有取消状态
        if (queryDto.status == '-1') {
          queryBuilder.andWhere('order.status IN (:...statuses)', { statuses: [5, 6] });
        } else {
          queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
        }
      }

      // 订单类型筛选
      if (queryDto.orderType) {
        queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
      }

      // 排序：按预约配送时间排序
      if (queryDto.orderByDeliveryTime) {
        queryBuilder.orderBy('order.deliveryTime', queryDto.orderByDeliveryTime);
      } else {
        // 默认按创建时间倒序
        queryBuilder.orderBy('order.createTime', 'DESC');
      }

      // 分页
      const pageNum = queryDto.pageNum || 1;
      const pageSize = queryDto.pageSize || 10;
      queryBuilder.skip((pageNum - 1) * pageSize).take(pageSize);

      const [orders, total] = await queryBuilder.getManyAndCount();

      // 提取团购订单ID并获取参与者信息
      const groupBuyOrderIds = this.extractGroupBuyIds(orders);
      const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);

      // 批量查询支付信息
      const orderIds = orders.map(order => order.orderId);
      let paymentInfoMap = new Map();
      
      if (orderIds.length > 0) {
        this.logger.log(`[DEBUG] 批量查询支付信息: 订单数=${orderIds.length}, OrderIds=${orderIds.join(',')}`);
        
        const paymentResults = await this.dataSource.query(
          `SELECT order_id, payment_method, payment_time, payment_amount 
           FROM payments 
           WHERE order_id IN (${orderIds.map(() => '?').join(',')}) AND payment_status = '2'
           ORDER BY create_time DESC`,
          orderIds
        );
        
        this.logger.log(`[DEBUG] 支付信息查询结果: 找到${paymentResults.length}条记录`);
        this.logger.log(`[DEBUG] 支付记录详情: ${JSON.stringify(paymentResults)}`);
        
        // 为每个订单保留最新的支付记录
        const processedOrderIds = new Set();
        paymentResults.forEach(payment => {
          if (!processedOrderIds.has(payment.order_id)) {
            paymentInfoMap.set(payment.order_id, payment);
            processedOrderIds.add(payment.order_id);
            this.logger.log(`[DEBUG] 为订单 ${payment.order_id} 设置支付方式: ${payment.payment_method}`);
          }
        });
        
        this.logger.log(`[DEBUG] 支付信息映射完成: 映射了${paymentInfoMap.size}个订单的支付信息`);
      }

      // 处理订单数据，扁平化商品信息
      const processedOrders = orders.map((order: any) => {
        const paymentInfo = paymentInfoMap.get(order.orderId);
        this.logger.log(`[DEBUG] 处理订单 ${order.orderId}: 支付信息=${JSON.stringify(paymentInfo)}, 支付方式=${paymentInfo?.payment_method}`);
        const orderData = {
          orderId: order.orderId,
          userId: order.userId,
          orderType: order.orderType,
          totalAmount: order.totalAmount,
          discountAmount: order.discountAmount,
          finalAmount: order.finalAmount,
          deliveryType: order.deliveryType,
          deliveryTime: order.deliveryTime,
          status: order.status,
          receiverName: order.receiverName,
          receiverPhone: order.receiverPhone,
          receiverAddress: order.receiverAddress,
          receiverLatitude: order.address?.latitude || null, // 添加纬度信息
          receiverLongitude: order.address?.longitude || null, // 添加经度信息
          createTime: order.createTime,
          paymentTime: order.paymentTime,
          shipmentTime: order.shipmentTime,
          completionTime: order.completionTime,
          remark: order.remark,
          items: [],
          userGroupBuyId: order.userGroupBuyId,
          isGroupInitiator: order.isGroupInitiator === 1,
          hasReviewed: order.reviewStatus === '1', // AURA-X: Add - 判断用户是否已评价. Approval: 寸止.
          paymentMethod: paymentInfo?.payment_method || null, // 从查询结果获取支付方式
          paymentAmount: paymentInfo?.payment_amount || null, // 从查询结果获取支付金额
          refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
          // 添加退款申请相关字段
          refundStatus: order.refund_status || null, // 退款申请状态
          refundReason: order.refund_reason || null, // 退款原因
          refundRequestTime: order.refund_request_time || null, // 退款申请时间
        };

        // 处理订单商品项
        if (order.orderItems && order.orderItems.length > 0) {
          orderData.items = order.orderItems.map((item) => {
            // 处理商品图片 - 直接使用processProductImages方法，传入空Map
            const { productImage, imageList } = this.processProductImages(item.product, new Map());

            return {
              itemId: item.itemId,
              productId: item.productId,
              productName: item.product ? item.product.name : '未知商品',
              productImage: productImage,
              imageList: imageList,
              price: item.price,
              quantity: item.quantity,
              totalPrice: item.totalPrice,
              specId: item.specId,
              specName: item.specName || '默认规格',
              specValue: item.specValue || '默认',
              originalPrice: item.originalPrice,
              groupBuyPrice: item.groupBuyPrice,
              categoryId: item.categoryId,
              categoryName: item.categoryName,
            };
          });
        }

        // 处理用户信息
        if (order.user) {
          orderData['user'] = {
            userId: order.user.userId,
            nickname: order.user.nickname,
            avatar: order.user.avatar,
            gender: order.user.gender,
          };
        }

        // 添加团购参与者信息
        if (order.orderType === OrderType.GROUP_BUY && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
          orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
          this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
        }

        return orderData;
      });

      this.logger.log(`[DEBUG] 获取用户订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
      return ResultData.ok({
        list: processedOrders,
        total,
        pageNum,
        pageSize,
      });
    } catch (error) {
      this.logger.error(`[ERROR] 获取用户订单列表失败: ${error.message}`, error.stack);
      throw new HttpException('获取订单列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取订单详情
   */
  async getOrderDetail(orderId: string, userId?: number): Promise<ResultData> {
    this.logger.log(`[DEBUG] 获取订单详情: OrderId=${orderId}, UserId=${userId}`);

    try {
      // 获取订单基本信息和关联数据
      const order = await this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.orderItems', 'orderItems')
        .leftJoinAndSelect('orderItems.product', 'product')
        .leftJoinAndSelect('order.user', 'user')
        .where('order.orderId = :orderId', { orderId })
        .andWhere('order.delFlag = :delFlag', { delFlag: '0' })
        .andWhere(userId ? 'order.userId = :userId' : '1=1', userId ? { userId } : {})
        .getOne();

      if (!order) {
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }

      // 单独查询支付信息
      this.logger.log(`[DEBUG] 查询支付信息 SQL: SELECT payment_method, payment_time, payment_amount FROM payments WHERE order_id = '${orderId}' AND payment_status = "2" ORDER BY create_time DESC LIMIT 1`);
      
      const paymentResult = await this.dataSource.query(
        'SELECT payment_method, payment_time, payment_amount FROM payments WHERE order_id = ? AND payment_status = "2" ORDER BY create_time DESC LIMIT 1',
        [orderId]
      );
      
      this.logger.log(`[DEBUG] 支付信息查询结果: ${JSON.stringify(paymentResult)}`);
      
      const paymentInfo = paymentResult && paymentResult.length > 0 ? paymentResult[0] : null;
      this.logger.log(`[DEBUG] 处理后的支付信息: ${JSON.stringify(paymentInfo)}`);
      this.logger.log(`[DEBUG] 最终支付方式: ${paymentInfo?.payment_method}`);

      // 处理订单数据，扁平化商品信息
      const orderDetail = {
        orderId: order.orderId,
        userId: order.userId,
        orderType: order.orderType,
        totalAmount: order.totalAmount,
        discountAmount: order.discountAmount,
        finalAmount: order.finalAmount,
        deliveryType: order.deliveryType,
        deliveryTime: order.deliveryTime,
        status: order.status,
        receiverName: order.receiverName,
        receiverPhone: order.receiverPhone,
        receiverAddress: order.receiverAddress,
        createTime: order.createTime,
        paymentTime: order.paymentTime,
        shipmentTime: order.shipmentTime,
        completionTime: order.completionTime,
        cancelReason: order.cancelReason,
        remark: order.remark,
        userGroupBuyId: order.userGroupBuyId,
        isGroupInitiator: order.isGroupInitiator === 1,
        items: [],
        paymentMethod: paymentInfo?.payment_method || null, // 从查询结果获取支付方式
        paymentAmount: paymentInfo?.payment_amount || null, // 从查询结果获取支付金额
        refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
      };

      // 处理用户信息
      if (order.user) {
        orderDetail['user'] = {
          userId: order.user.userId,
          nickname: order.user.nickname,
          phone: order.user.phone,
          avatar: order.user.avatar,
        };
      }

      // 处理订单商品项
      if (order.orderItems && order.orderItems.length > 0) {
        orderDetail.items = order.orderItems.map((item) => {
          // 处理商品图片 - 直接使用processProductImages方法，传入空Map
          const { productImage, imageList } = this.processProductImages(item.product, new Map());

          return {
            itemId: item.itemId,
            productId: item.productId,
            productName: item.product ? item.product.name : '未知商品',
            productImage: productImage,
            imageList: imageList,
            price: item.price,
            quantity: item.quantity,
            totalPrice: item.totalPrice,
            specId: item.specId,
            specName: item.specName || '默认规格',
            specValue: item.specValue || '默认',
            originalPrice: item.originalPrice,
            groupBuyPrice: item.groupBuyPrice,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
          };
        });
      }

      // 查询团购订单参与者信息
      if (order.orderType === OrderType.GROUP_BUY && order.userGroupBuyId) {
        const groupBuyParticipantsMap = await this.getGroupBuyParticipants([order.userGroupBuyId]);
        if (groupBuyParticipantsMap.has(order.userGroupBuyId)) {
          const participants = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
          orderDetail['groupBuyParticipants'] = participants;
          this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${orderId}, ParticipantsCount=${participants.length}`);
        }
      }

      this.logger.log(`[DEBUG] 获取订单详情成功: OrderId=${orderId}`);
      return ResultData.ok(orderDetail);
    } catch (error) {
      this.logger.error(`[ERROR] 获取订单详情失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('获取订单详情失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId: string, updateOrderDto: UpdateOrderDto, operatorId?: number): Promise<ResultData> {
    this.logger.log(`[DEBUG] 更新订单状态: OrderId=${orderId}, Data=${JSON.stringify(updateOrderDto)}, OperatorId=${operatorId}`);

    try {
      const order = await this.orderRepository.findOne({
        where: { orderId, delFlag: '0' },
      });

      if (!order) {
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }

      // 状态流转验证
      const currentStatus = order.status;
      const newStatus = updateOrderDto.status;

      if (!this.isValidStatusTransition(currentStatus, newStatus)) {
        throw new HttpException(`订单状态不能从 ${currentStatus} 变更为 ${newStatus}`, HttpStatus.BAD_REQUEST);
      }

      // 更新订单状态
      order.status = newStatus;
      order.updateTime = new Date();
      order.updateBy = operatorId?.toString() || order.updateBy;

      // 设置相关时间字段
      const now = new Date();
      switch (newStatus) {
        case OrderStatus.PENDING_SHIPMENT:
          order.paymentTime = now;
          break;
        case OrderStatus.SHIPPING:
          order.shipmentTime = now;
          break;
        case OrderStatus.COMPLETED:
          order.completionTime = now;
          break;
        case OrderStatus.CANCELLED:
        case OrderStatus.GROUP_BUY_FAILED:
          order.cancelReason = updateOrderDto.cancelReason || '系统取消';
          break;
      }

      const updatedOrder = await this.orderRepository.save(order);

      // 当订单状态从待支付变更为待发货时（支付成功），发送新订单通知给管理员
      if (currentStatus === OrderStatus.PENDING_PAYMENT && newStatus === OrderStatus.PENDING_SHIPMENT) {
        try {
          this.notificationGateway.sendNewOrderNotification({
            orderId: updatedOrder.orderId,
            totalAmount: updatedOrder.totalAmount,
            finalAmount: updatedOrder.finalAmount,
            orderType: updatedOrder.orderType === OrderType.GROUP_BUY ? '团购订单' : '普通订单',
            createTime: updatedOrder.createTime,
            paymentTime: updatedOrder.paymentTime,
            userInfo: {
              userId: updatedOrder.userId
            }
          });
          this.logger.log(`[DEBUG] 支付成功通知发送成功: OrderId=${orderId}`);
        } catch (error) {
          this.logger.error(`[ERROR] 发送支付成功通知失败: ${error.message}`, error.stack);
          // 通知发送失败不影响订单状态更新，继续执行
        }
      }

      this.logger.log(`[DEBUG] 订单状态更新成功: OrderId=${orderId}, Status=${newStatus}`);
      return ResultData.ok(updatedOrder, '订单状态更新成功');
    } catch (error) {
      this.logger.error(`[ERROR] 更新订单状态失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('更新订单状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新订单评价状态
   * @param orderId 订单ID
   * @param userId 操作用户ID
   * @param status 评价状态 (0未评价, 1已评价)
   * @returns 更新结果
   */
  async updateOrderEvaluationStatus(orderId: string, userId: number, status: string): Promise<ResultData> {
    this.logger.log(`[DEBUG] 更新订单评价状态: OrderId=${orderId}, UserId=${userId}, Status=${status}`);
    try {
      const order = await this.orderRepository.findOne({
        where: { orderId, userId, delFlag: '0' },
      });

      if (!order) {
        throw new HttpException('订单不存在或无权操作', HttpStatus.NOT_FOUND);
      }

      order.reviewStatus = status;
      order.updateTime = new Date();
      order.updateBy = userId.toString();

      await this.orderRepository.save(order);

      this.logger.log(`[DEBUG] 订单评价状态更新成功: OrderId=${orderId}, ReviewStatus=${status}`);
      return ResultData.ok(null, '订单评价状态更新成功');
    } catch (error) {
      this.logger.error(`[ERROR] 更新订单评价状态失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('更新订单评价状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string, userId: number, reason?: string): Promise<ResultData> {
    this.logger.log(`[DEBUG] 取消订单: OrderId=${orderId}, UserId=${userId}, Reason=${reason}`);

    try {
      const order = await this.orderRepository.findOne({
        where: { orderId, userId, delFlag: '0' },
      });

      if (!order) {
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }

      // 只有待支付状态的订单可以取消
      if (order.status !== OrderStatus.PENDING_PAYMENT) {
        throw new HttpException('只有待支付的订单可以取消', HttpStatus.BAD_REQUEST);
      }

      const updateOrderDto = new UpdateOrderDto();
      updateOrderDto.status = OrderStatus.CANCELLED;
      updateOrderDto.cancelReason = reason || '用户主动取消';

      return await this.updateOrderStatus(orderId, updateOrderDto, userId);
    } catch (error) {
      this.logger.error(`[ERROR] 取消订单失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('取消订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 管理员查询订单列表
   */
  async findAll(queryDto: OrderQueryDto): Promise<ResultData> {
    this.logger.log(`[DEBUG] 管理员查询订单列表: ${JSON.stringify(queryDto)}`);

    try {
      const queryBuilder = this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.orderItems', 'orderItems')
        .leftJoinAndSelect('orderItems.product', 'product')
        .leftJoinAndSelect('order.user', 'user')
        .where('order.delFlag = :delFlag', { delFlag: '0' });

      // 用户ID筛选
      if (queryDto.userId) {
        queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
      }

      // 订单ID搜索
      if (queryDto.orderId) {
        queryBuilder.andWhere('order.orderId LIKE :orderId', { orderId: `%${queryDto.orderId}%` });
      }

      // 订单状态筛选
      if (queryDto.status) {
        queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
      }

      // 订单类型筛选
      if (queryDto.orderType) {
        queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
      }

      // 收货人姓名搜索
      if (queryDto.receiverName) {
        queryBuilder.andWhere('order.receiverName LIKE :receiverName', {
          receiverName: `%${queryDto.receiverName}%`,
        });
      }

      // 收货人电话搜索
      if (queryDto.receiverPhone) {
        queryBuilder.andWhere('order.receiverPhone LIKE :receiverPhone', {
          receiverPhone: `%${queryDto.receiverPhone}%`,
        });
      }

      // 时间范围筛选
      if (queryDto.startTime) {
        queryBuilder.andWhere('order.createTime >= :startTime', { startTime: new Date(queryDto.startTime) });
      }
      if (queryDto.endTime) {
        queryBuilder.andWhere('order.createTime <= :endTime', { endTime: new Date(queryDto.endTime + ' 23:59:59') });
      }

      // 分页
      const pageNum = queryDto.pageNum || 1;
      const pageSize = queryDto.pageSize || 10;
      queryBuilder
        .orderBy('order.createTime', 'DESC')
        .skip((pageNum - 1) * pageSize)
        .take(pageSize);

      const [orders, total] = await queryBuilder.getManyAndCount();

      // 提取团购订单ID并获取参与者信息
      const groupBuyOrderIds = this.extractGroupBuyIds(orders);
      const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);

      // 处理订单数据，扁平化处理商品信息
      const processedOrders = orders.map((order) => {
        const orderData = {
          orderId: order.orderId,
          userId: order.userId,
          orderType: order.orderType,
          totalAmount: order.totalAmount,
          discountAmount: order.discountAmount,
          finalAmount: order.finalAmount,
          deliveryType: order.deliveryType,
          deliveryTime: order.deliveryTime,
          status: order.status,
          receiverName: order.receiverName,
          receiverPhone: order.receiverPhone,
          receiverAddress: order.receiverAddress,
          createTime: order.createTime,
          paymentTime: order.paymentTime,
          shipmentTime: order.shipmentTime,
          completionTime: order.completionTime,
          cancelReason: order.cancelReason,
          remark: order.remark,
          items: [],
          userGroupBuyId: order.userGroupBuyId,
          isGroupInitiator: order.isGroupInitiator === 1,
          paymentMethod: null, // 支付方式待查询
          paymentAmount: null, // 支付金额待查询
          refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
        };

        // 处理用户信息
        if (order.user) {
          orderData['user'] = {
            userId: order.user.userId,
            nickname: order.user.nickname,
            phone: order.user.phone,
            avatar: order.user.avatar,
          };
        }

        // 处理订单商品项
        if (order.orderItems && order.orderItems.length > 0) {
          orderData.items = order.orderItems.map((item) => {
            // 处理商品图片 - 直接使用processProductImages方法，传入空Map
            const { productImage, imageList } = this.processProductImages(item.product, new Map());

            return {
              itemId: item.itemId,
              productId: item.productId,
              productName: item.product ? item.product.name : '未知商品',
              productImage: productImage,
              imageList: imageList,
              price: item.price,
              quantity: item.quantity,
              totalPrice: item.totalPrice,
              specId: item.specId,
              specName: item.specName || '默认规格',
              specValue: item.specValue || '默认',
              originalPrice: item.originalPrice,
              groupBuyPrice: item.groupBuyPrice,
              categoryId: item.categoryId,
              categoryName: item.categoryName,
            };
          });
        }

        // 添加团购参与者信息
        if (order.orderType === OrderType.GROUP_BUY && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
          orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
          this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
        }

        return orderData;
      });

      this.logger.log(`[DEBUG] 管理员查询订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
      return ResultData.ok({
        list: processedOrders,
        total,
        pageNum,
        pageSize,
      });
    } catch (error) {
      this.logger.error(`[ERROR] 管理员查询订单列表失败: ${error.message}`, error.stack);
      throw new HttpException('查询订单列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 管理员查询自提订单列表
   */
  async findSelfPickupOrders(queryDto: OrderQueryDto): Promise<ResultData> {
    this.logger.log(`[DEBUG] 管理员查询自提订单列表: ${JSON.stringify(queryDto)}`);

    try {
      const queryBuilder = this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.orderItems', 'orderItems')
        .leftJoinAndSelect('orderItems.product', 'product')
        .leftJoinAndSelect('order.user', 'user')
        .leftJoin('payments', 'payment', 'payment.order_id = order.orderId AND payment.payment_status = :paymentStatus', { paymentStatus: '2' })
        .addSelect(['payment.payment_method', 'payment.payment_time', 'payment.payment_amount'])
        .where('order.delFlag = :delFlag', { delFlag: '0' })
        .andWhere('order.deliveryType = :deliveryType', { deliveryType: '2' }); // 自提类型订单

      // 用户ID筛选
      if (queryDto.userId) {
        queryBuilder.andWhere('order.userId = :userId', { userId: queryDto.userId });
      }

      // 订单ID搜索
      if (queryDto.orderId) {
        queryBuilder.andWhere('order.orderId LIKE :orderId', { orderId: `%${queryDto.orderId}%` });
      }

      // 订单状态筛选
      if (queryDto.status) {
        queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
      }

      // 订单类型筛选
      if (queryDto.orderType) {
        queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
      }

      // 收货人姓名搜索
      if (queryDto.receiverName) {
        queryBuilder.andWhere('order.receiverName LIKE :receiverName', {
          receiverName: `%${queryDto.receiverName}%`,
        });
      }

      // 收货人电话搜索
      if (queryDto.receiverPhone) {
        queryBuilder.andWhere('order.receiverPhone LIKE :receiverPhone', {
          receiverPhone: `%${queryDto.receiverPhone}%`,
        });
      }

      // 手机号尾号后四位搜索 - 使用用户表中的手机号
      if (queryDto.phoneLastFour) {
        queryBuilder.andWhere('user.phone LIKE :phoneLastFour', {
          phoneLastFour: `%${queryDto.phoneLastFour}`,
        });
        this.logger.log(`[DEBUG] 根据用户手机号尾号搜索: ${queryDto.phoneLastFour}`);
      }

      // 时间范围筛选
      if (queryDto.startTime) {
        queryBuilder.andWhere('order.createTime >= :startTime', { startTime: new Date(queryDto.startTime) });
      }
      if (queryDto.endTime) {
        queryBuilder.andWhere('order.createTime <= :endTime', { endTime: new Date(queryDto.endTime + ' 23:59:59') });
      }

      // 分页
      const pageNum = queryDto.pageNum || 1;
      const pageSize = queryDto.pageSize || 10;
      queryBuilder
        .orderBy('order.createTime', 'DESC')
        .skip((pageNum - 1) * pageSize)
        .take(pageSize);

      const [orders, total] = await queryBuilder.getManyAndCount();

      // 提取团购订单ID并获取参与者信息
      const groupBuyOrderIds = this.extractGroupBuyIds(orders);
      const groupBuyParticipantsMap = await this.getGroupBuyParticipants(groupBuyOrderIds);

      // 处理订单数据，扁平化处理商品信息
      const processedOrders = orders.map((order) => {
        const orderData = {
          orderId: order.orderId,
          userId: order.userId,
          orderType: order.orderType,
          totalAmount: order.totalAmount,
          discountAmount: order.discountAmount,
          finalAmount: order.finalAmount,
          deliveryType: order.deliveryType,
          deliveryTime: order.deliveryTime,
          status: order.status,
          receiverName: order.receiverName,
          receiverPhone: order.receiverPhone,
          receiverAddress: order.receiverAddress,
          createTime: order.createTime,
          paymentTime: order.paymentTime,
          shipmentTime: order.shipmentTime,
          completionTime: order.completionTime,
          cancelReason: order.cancelReason,
          remark: order.remark,
          items: [],
          userGroupBuyId: order.userGroupBuyId,
          isGroupInitiator: order.isGroupInitiator === 1,
          paymentMethod: null, // 支付方式待查询
          paymentAmount: null, // 支付金额待查询
          refundType: order.refundType || null, // 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
        };

        // 处理用户信息
        if (order.user) {
          orderData['user'] = {
            userId: order.user.userId,
            nickname: order.user.nickname,
            phone: order.user.phone,
            avatar: order.user.avatar,
          };
        }

        // 处理订单商品项
        if (order.orderItems && order.orderItems.length > 0) {
          orderData.items = order.orderItems.map((item) => {
            // 处理商品图片 - 直接使用processProductImages方法，传入空Map
            const { productImage, imageList } = this.processProductImages(item.product, new Map());

            return {
              itemId: item.itemId,
              productId: item.productId,
              productName: item.product ? item.product.name : '未知商品',
              productImage: productImage,
              imageList: imageList,
              price: item.price,
              quantity: item.quantity,
              totalPrice: item.totalPrice,
              specId: item.specId,
              specName: item.specName || '默认规格',
              specValue: item.specValue || '默认',
              originalPrice: item.originalPrice,
              groupBuyPrice: item.groupBuyPrice,
              categoryId: item.categoryId,
              categoryName: item.categoryName,
            };
          });
        }

        // 添加团购参与者信息
        if (order.orderType === '2' && order.userGroupBuyId && groupBuyParticipantsMap.has(order.userGroupBuyId)) {
          orderData['groupBuyParticipants'] = groupBuyParticipantsMap.get(order.userGroupBuyId) || [];
          this.logger.log(`[DEBUG] 添加团购参与者信息: OrderId=${order.orderId}, ParticipantsCount=${orderData['groupBuyParticipants'].length}`);
        }

        return orderData;
      });

      this.logger.log(`[DEBUG] 管理员查询自提订单列表成功: Total=${total}, PageNum=${pageNum}, PageSize=${pageSize}`);
      return ResultData.ok({
        rows: processedOrders,
        total,
        pageNum: Number(pageNum),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / pageSize),
      });
    } catch (error) {
      this.logger.error(`[ERROR] 管理员查询自提订单列表失败: ${error.message}`, error.stack);
      throw new HttpException('查询自提订单列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取订单统计（管理端）
   */
  async getOrderStats(): Promise<ResultData> {
    this.logger.log(`[DEBUG] 获取订单统计`);

    try {
      // 1. 统计订单总数
      const totalCount = await this.orderRepository.count({ where: { delFlag: '0' } });

      // 2. 统计各状态订单数量
      const statusCounts = await this.dataSource.query(`
        SELECT status, COUNT(*) as count
        FROM orders
        WHERE del_flag = '0'
        GROUP BY status
      `);

      // 3. 统计今日订单数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayCount = await this.orderRepository.count({
        where: {
          delFlag: '0',
          createTime: MoreThanOrEqual(today),
        },
      });

      // 4. 统计本月订单数
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const monthCount = await this.orderRepository.count({
        where: {
          delFlag: '0',
          createTime: MoreThanOrEqual(firstDayOfMonth),
        },
      });

      // 5. 统计总销售额
      const totalSalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
      `);
      const totalSales = totalSalesResult[0]?.total || 0;

      // 6. 统计今日销售额
      const todaySalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
        AND DATE(create_time) = CURDATE()
      `);
      const todaySales = todaySalesResult[0]?.total || 0;

      // 7. 统计本月销售额
      const monthSalesResult = await this.dataSource.query(`
        SELECT SUM(final_amount) as total
        FROM orders
        WHERE del_flag = '0' AND status != '5'
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
      `);
      const monthSales = monthSalesResult[0]?.total || 0;

      // 构建状态统计对象
      const statusMap = {};
      statusCounts.forEach((item) => {
        statusMap[item.status] = item.count;
      });

      return ResultData.ok({
        totalCount,
        todayCount,
        monthCount,
        totalSales,
        todaySales,
        monthSales,
        statusCounts: statusMap,
      });
    } catch (error) {
      this.logger.error(`[ERROR] 获取订单统计失败: ${error.message}`, error.stack);
      throw new HttpException('获取订单统计失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取详细的订单数据统计（管理端仪表板使用）
   */
  async getDetailedOrderStatistics(query: OrderStatisticsQueryDto): Promise<ResultData> {
    this.logger.log(`[DEBUG] 获取详细订单统计: ${JSON.stringify(query)}`);

    try {
      const { period = 'day', range = 7, topCount = 10 } = query;

      // 1. 生成销售趋势数据
      const salesTrend = await this.getSalesTrend(period, range);

      // 2. 获取热销商品数据
      const hotProducts = await this.getHotProducts(topCount);

      // 3. 获取汇总统计数据
      const summary = await this.getSummaryStatistics();

      const result: OrderStatisticsResponseDto = {
        salesTrend,
        hotProducts,
        summary,
      };

      return ResultData.ok(result);
    } catch (error) {
      this.logger.error(`[ERROR] 获取详细订单统计失败: ${error.message}`, error.stack);
      throw new HttpException('获取详细订单统计失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取销售趋势数据
   */
  private async getSalesTrend(period: string, range: number): Promise<SalesStatisticsItem[]> {
    try {
      let dateFormat: string;
      let dateFunction: string;

      switch (period) {
        case 'day':
          dateFormat = '%Y-%m-%d';
          dateFunction = 'DATE';
          break;
        case 'week':
          dateFormat = '%Y-第%u周';
          dateFunction = 'YEARWEEK';
          break;
        case 'month':
          dateFormat = '%Y-%m';
          dateFunction = 'DATE_FORMAT';
          break;
        default:
          dateFormat = '%Y-%m-%d';
          dateFunction = 'DATE';
      }

      let dateCondition: string;
      let selectDate: string;
      let groupBy: string;

      switch (period) {
        case 'day':
          // 当天每小时的数据
          dateCondition = 'DATE(create_time) = CURDATE()';
          selectDate = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
          groupBy = 'DATE(create_time), HOUR(create_time)';
          break;
        case 'week':
          // 当周每天的数据
          dateCondition = 'YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)';
          selectDate = 'DATE(create_time)';
          groupBy = 'DATE(create_time)';
          break;
        case 'month':
          // 当月每天的数据
          dateCondition = 'YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW())';
          selectDate = 'DATE(create_time)';
          groupBy = 'DATE(create_time)';
          break;
        default:
          // 默认当天每小时
          dateCondition = 'DATE(create_time) = CURDATE()';
          selectDate = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
          groupBy = 'DATE(create_time), HOUR(create_time)';
      }

      const sql = `
        SELECT 
          ${selectDate} as date,
          0 as sales,
          COUNT(*) as orderCount
        FROM orders 
        WHERE del_flag = '0' 
          AND status = '4'
          AND ${dateCondition}
        GROUP BY ${groupBy}
        ORDER BY ${groupBy}
      `;

      this.logger.log(`[DEBUG] 销售趋势SQL: ${sql}`);
      const result = await this.dataSource.query(sql);
      this.logger.log(`[DEBUG] 销售趋势查询结果: ${JSON.stringify(result)}`);

      const processedResult = result.map((item) => {
        let dateStr = item.date;

        // 处理Date对象和时区问题
        if (typeof item.date === 'object' && item.date instanceof Date) {
          // 如果是Date对象，需要正确处理时区
          // MySQL DATE()函数返回的应该是本地日期，但被解析为UTC
          // 我们需要获取本地日期部分
          const year = item.date.getFullYear();
          const month = String(item.date.getMonth() + 1).padStart(2, '0');
          const day = String(item.date.getDate()).padStart(2, '0');
          dateStr = `${year}-${month}-${day}`;
        } else if (typeof item.date === 'string' && item.date.includes('T')) {
          // 如果是ISO格式字符串，只取日期部分
          dateStr = item.date.split('T')[0];
        }

        return {
          date: dateStr,
          sales: parseFloat(item.sales) || 0,
          orderCount: parseInt(item.orderCount) || 0,
        };
      });

      this.logger.log(`[DEBUG] 处理后的销售趋势: ${JSON.stringify(processedResult)}`);
      return processedResult;
    } catch (error) {
      this.logger.error(`[ERROR] 获取销售趋势失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 获取热销商品数据
   */
  private async getHotProducts(topCount: number): Promise<HotProductItem[]> {
    try {
      const sql = `
        SELECT 
          oi.product_id as productId,
          oi.product_name as productName,
          oi.category_name as categoryName,
          SUM(oi.total_price) as totalSales,
          SUM(oi.quantity) as totalQuantity,
          COUNT(DISTINCT oi.order_id) as orderCount,
          MAX(oi.product_image) as productImage
        FROM order_items oi
        INNER JOIN orders o ON oi.order_id = o.order_id
        WHERE o.del_flag = '0' 
          AND o.status IN ('2', '3', '4')
        GROUP BY oi.product_id, oi.product_name, oi.category_name
        ORDER BY totalSales DESC, totalQuantity DESC
        LIMIT ?
      `;

      this.logger.log(`[DEBUG] 热销商品SQL: ${sql}`);
      const result = await this.dataSource.query(sql, [topCount]);
      this.logger.log(`[DEBUG] 热销商品查询结果: ${JSON.stringify(result)}`);

      const processedResult = result.map((item) => ({
        productId: parseInt(item.productId),
        productName: item.productName || '未知商品',
        categoryName: item.categoryName || '未分类',
        totalSales: parseFloat(item.totalSales) || 0,
        totalQuantity: parseInt(item.totalQuantity) || 0,
        orderCount: parseInt(item.orderCount) || 0,
        productImage: item.productImage || '',
      }));

      this.logger.log(`[DEBUG] 处理后的热销商品: ${JSON.stringify(processedResult)}`);
      return processedResult;
    } catch (error) {
      this.logger.error(`[ERROR] 获取热销商品失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 获取汇总统计数据
   */
  private async getSummaryStatistics() {
    try {
      // 当前期间数据
      const currentSalesResult = await this.dataSource.query(`
        SELECT COALESCE(SUM(final_amount), 0) as total
        FROM orders
        WHERE del_flag = '0' AND status IN ('2', '3', '4')
      `);

      const currentOrdersResult = await this.dataSource.query(`
        SELECT 
          COUNT(*) as totalOrders,
          SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as completedOrders,
          SUM(CASE WHEN status IN ('1', '2', '3') THEN 1 ELSE 0 END) as pendingOrders,
          SUM(CASE WHEN status IN ('5', '6') THEN 1 ELSE 0 END) as cancelledOrders
        FROM orders
        WHERE del_flag = '0'
      `);

      // 上个月数据用于对比
      const lastMonthSalesResult = await this.dataSource.query(`
        SELECT COALESCE(SUM(final_amount), 0) as total
        FROM orders
        WHERE del_flag = '0' AND status IN ('2', '3', '4')
          AND create_time >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 MONTH)
          AND create_time < DATE_SUB(NOW(), INTERVAL 1 MONTH)
      `);

      const lastMonthOrdersResult = await this.dataSource.query(`
        SELECT COUNT(*) as totalOrders
        FROM orders
        WHERE del_flag = '0' 
          AND create_time >= DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 MONTH), INTERVAL 1 MONTH)
          AND create_time < DATE_SUB(NOW(), INTERVAL 1 MONTH)
      `);

      this.logger.log(`[DEBUG] 当前销售额: ${JSON.stringify(currentSalesResult)}`);
      this.logger.log(`[DEBUG] 上月销售额: ${JSON.stringify(lastMonthSalesResult)}`);

      const totalSales = parseFloat(currentSalesResult[0]?.total) || 0;
      const orderCounts = currentOrdersResult[0];
      const totalOrders = parseInt(orderCounts?.totalOrders) || 0;

      // 计算增长率
      const lastMonthSales = parseFloat(lastMonthSalesResult[0]?.total) || 0;
      const lastMonthOrders = parseInt(lastMonthOrdersResult[0]?.totalOrders) || 0;

      const salesGrowth = lastMonthSales > 0 ? Math.round(((totalSales - lastMonthSales) / lastMonthSales) * 100 * 10) / 10 : 0;
      const ordersGrowth = lastMonthOrders > 0 ? Math.round(((totalOrders - lastMonthOrders) / lastMonthOrders) * 100 * 10) / 10 : 0;

      const result = {
        totalSales,
        totalOrders,
        completedOrders: parseInt(orderCounts?.completedOrders) || 0,
        pendingOrders: parseInt(orderCounts?.pendingOrders) || 0,
        cancelledOrders: parseInt(orderCounts?.cancelledOrders) || 0,
        // 新增增长率字段
        salesGrowth,
        ordersGrowth,
        completedGrowth: 0, // 暂时设为0，可以后续添加逻辑
      };

      this.logger.log(`[DEBUG] 汇总统计结果: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`[ERROR] 获取汇总统计失败: ${error.message}`, error.stack);
      return {
        totalSales: 0,
        totalOrders: 0,
        completedOrders: 0,
        pendingOrders: 0,
        cancelledOrders: 0,
        salesGrowth: 0,
        ordersGrowth: 0,
        completedGrowth: 0,
      };
    }
  }

  /**
   * 调试：检查数据库表和数据
   */
  async debugCheckTables() {
    try {
      // 1. 检查订单表是否存在
      const tableExistsResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = 'orders'
      `);

      // 2. 检查订单表数据量
      const orderCountResult = await this.dataSource.query(`
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN del_flag = '0' THEN 1 END) as active_count,
               COUNT(CASE WHEN del_flag = '0' AND status IN ('2', '3', '4') THEN 1 END) as paid_count
        FROM orders
      `);

      // 3. 检查最近10条订单数据
      const recentOrdersResult = await this.dataSource.query(`
        SELECT order_id, user_id, total_amount, final_amount, status, del_flag, create_time
        FROM orders 
        ORDER BY create_time DESC 
        LIMIT 10
      `);

      // 4. 检查订单项表
      const orderItemCountResult = await this.dataSource.query(`
        SELECT COUNT(*) as count FROM order_items
      `);

      return {
        tableExists: tableExistsResult[0]?.count > 0,
        orderCounts: orderCountResult[0],
        recentOrders: recentOrdersResult,
        orderItemCount: orderItemCountResult[0]?.count || 0,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`[ERROR] 调试检查失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取用户订单统计
   * @param userId 用户ID
   */
  async getUserOrderStats(userId: number): Promise<ResultData> {
    this.logger.log(`[DEBUG] 获取用户订单统计: UserId=${userId}`);

    try {
      // 用户总订单数
      const totalOrders = await this.orderRepository.count({
        where: {
          userId,
          delFlag: '0',
        },
      });

      // 各状态订单数
      const statusStats = await this.orderRepository
        .createQueryBuilder('order')
        .select('order.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('order.delFlag = :delFlag', { delFlag: '0' })
        .andWhere('order.userId = :userId', { userId })
        .groupBy('order.status')
        .getRawMany();

      // 订单状态映射，确保结果中包含所有状态（即使数量为0）
      const allStatuses = {
        [OrderStatus.PENDING_PAYMENT]: 0, // 待支付
        [OrderStatus.PENDING_SHIPMENT]: 0, // 待发货
        [OrderStatus.SHIPPING]: 0, // 配送中
        [OrderStatus.COMPLETED]: 0, // 已完成
        [OrderStatus.CANCELLED]: 0, // 已取消
        [OrderStatus.GROUP_BUY_FAILED]: 0, // 团购失败已退款
      };

      // 填充实际数据
      statusStats.forEach((item) => {
        allStatuses[item.status] = parseInt(item.count);
      });

      // 用户累计消费金额
      const totalConsumption = await this.orderRepository
        .createQueryBuilder('order')
        .select('SUM(order.finalAmount)', 'total')
        .where('order.delFlag = :delFlag', { delFlag: '0' })
        .andWhere('order.userId = :userId', { userId })
        .andWhere('order.status = :status', { status: OrderStatus.COMPLETED })
        .getRawOne();

      const stats = Object.entries(allStatuses).map(([status, count]) => ({
        status,
        count,
        statusText: this.getStatusText(status),
      }));

      this.logger.log(`[DEBUG] 获取用户订单统计成功: ${JSON.stringify(stats)}`);
      return ResultData.ok(stats);
    } catch (error) {
      this.logger.error(`[ERROR] 获取用户订单统计失败: ${error.message}`, error.stack);
      throw new HttpException('获取用户订单统计失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 验证订单状态流转是否合法
   */
  private isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions = {
      [OrderStatus.PENDING_PAYMENT]: [OrderStatus.PENDING_SHIPMENT, OrderStatus.CANCELLED, OrderStatus.GROUP_BUY_FAILED],
      [OrderStatus.PENDING_SHIPMENT]: [OrderStatus.SHIPPING, OrderStatus.CANCELLED, OrderStatus.COMPLETED], // 允许待发货直接变为已完成
      [OrderStatus.SHIPPING]: [OrderStatus.COMPLETED],
      [OrderStatus.COMPLETED]: [], // 已完成的订单不能再变更
      [OrderStatus.CANCELLED]: [], // 已取消的订单不能再变更
      [OrderStatus.GROUP_BUY_FAILED]: [], // 团购失败的订单不能再变更
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  // 格式化地址
  private formatAddress(address: any): string {
    const addressName = address.address_name || '';
    const detailAddress = address.detail_address || '';
    return `${addressName}${detailAddress}`;
  }

  /**
   * 调试接口：检查团购活动状态
   * @param activityId 团购活动ID
   * @returns 团购活动状态信息
   */
  async checkGroupBuyStatus(activityId: number): Promise<ResultData> {
    this.logger.log(`[DEBUG] 检查团购活动状态: ActivityId=${activityId}`);
    try {
      // 查询团购活动相关订单
      const orders = await this.orderRepository.find({
        where: {
          userGroupBuyId: activityId.toString(),
          orderType: OrderType.GROUP_BUY,
          delFlag: '0',
        },
      });

      if (!orders || orders.length === 0) {
        return ResultData.fail(404, '团购活动不存在');
      }

      // 获取团购参与者信息
      const participantsMap = await this.getGroupBuyParticipants([activityId.toString()]);
      const participants = participantsMap.get(activityId.toString()) || [];

      return ResultData.ok({
        activityId: activityId,
        status: 'active',
        participants: participants,
        participantCount: participants.length,
      });
    } catch (error) {
      this.logger.error(`[ERROR] 检查团购活动状态失败: ${error.message}`, error.stack);
      return ResultData.fail(500, '检查团购活动状态失败');
    }
  }

  /**
   * 生成团购ID
   * 使用UUID作为团购ID，确保全局唯一
   */
  private generateGroupBuyId(): string {
    try {
      // 使用GenerateUUID函数生成唯一ID
      const uuid = GenerateUUID();
      this.logger.log(`[DEBUG] 生成新的团购ID(UUID): ${uuid}`);
      return uuid;
    } catch (error) {
      this.logger.error(`[ERROR] 生成团购ID失败: ${error.message}`, error.stack);
      throw new HttpException('生成团购ID失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 处理商品图片
   * @param product 商品对象
   * @param imageMap 图片映射（不再使用）
   * @returns 处理后的商品图片信息
   */
  private processProductImages(product: any, imageMap: Map<string, any>): { productImage: string; imageList: string[] } {
    let productImage = '';
    let imageList = [];

    if (product && product.images) {
      // 直接使用逗号分隔的URL字符串
      const imageUrls = product.images.split(',').filter((url) => url.trim());

      if (imageUrls.length > 0) {
        // 第一张图片作为主图
        productImage = imageUrls[0];

        // 所有图片列表
        imageList = imageUrls;
      }
    }

    return { productImage, imageList };
  }

  /**
   * 获取团购订单参与者信息
   * @param groupBuyIds 团购ID数组
   * @returns 团购ID到参与者列表的映射
   */
  private async getGroupBuyParticipants(groupBuyIds: string[]): Promise<Map<string, any[]>> {
    this.logger.log(`[DEBUG] 查询团购订单参与者信息: GroupBuyIds=${groupBuyIds.join(',')}`);

    const groupBuyParticipantsMap = new Map<string, any[]>();

    if (!groupBuyIds.length) {
      return groupBuyParticipantsMap;
    }

    try {
      // 查询所有具有相同userGroupBuyId的订单
      const participantOrders = await this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.user', 'user')
        .where('order.userGroupBuyId IN (:...groupBuyIds)', { groupBuyIds })
        .andWhere('order.orderType = :orderType', { orderType: OrderType.GROUP_BUY })
        .andWhere('order.delFlag = :delFlag', { delFlag: '0' })
        .getMany();

      // 按团购ID分组参与者信息
      participantOrders.forEach((order) => {
        if (!order.userGroupBuyId) return;

        if (!groupBuyParticipantsMap.has(order.userGroupBuyId)) {
          groupBuyParticipantsMap.set(order.userGroupBuyId, []);
        }

        const participantInfo = {
          userId: order.userId,
          orderId: order.orderId,
          isInitiator: order.isGroupInitiator === 1,
          joinTime: order.createTime,
          // 用户信息
          nickname: order.user?.nickname || '未知用户',
          avatar: order.user?.avatar || '',
          gender: order.user?.gender || '0',
        };

        groupBuyParticipantsMap.get(order.userGroupBuyId)?.push(participantInfo);
      });

      return groupBuyParticipantsMap;
    } catch (error) {
      this.logger.error(`[ERROR] 获取团购订单参与者信息失败: ${error.message}`, error.stack);
      return new Map<string, any[]>();
    }
  }

  /**
   * 从订单列表中提取团购订单ID
   * @param orders 订单列表
   * @returns 团购订单ID数组
   */
  private extractGroupBuyIds(orders: OrderEntity[]): string[] {
    const groupBuyOrderIds: string[] = [];

    orders.forEach((order) => {
      if (order.orderType === OrderType.GROUP_BUY && order.userGroupBuyId) {
        groupBuyOrderIds.push(order.userGroupBuyId);
      }
    });

    return [...new Set(groupBuyOrderIds)]; // 去重
  }

  /**
   * 订单发货（管理端）
   * @param orderId 订单ID
   */
  async deliverOrder(orderId: string): Promise<ResultData> {
    try {
      this.logger.log(`[DEBUG] 订单发货: OrderId=${orderId}`);

      // 查找订单
      const order = await this.orderRepository.findOne({
        where: { orderId: orderId },
      });

      if (!order) {
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }

      // 检查订单状态
      if (order.status !== '2') {
        // 待发货
        throw new HttpException('只有待发货状态的订单才能发货', HttpStatus.BAD_REQUEST);
      }

      // 更新订单状态为配送中
      order.status = '3';
      order.shipmentTime = new Date();
      order.updateBy = 'admin';
      order.updateTime = new Date();

      await this.orderRepository.save(order);

      this.logger.log(`[DEBUG] 订单发货成功: OrderId=${orderId}`);
      return ResultData.ok(null, '订单发货成功');
    } catch (error) {
      this.logger.error(`[ERROR] 订单发货失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('订单发货失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 订单退款（管理端）
   * @param orderId 订单ID
   * @param refundReason 退款原因
   * @param refundType 退款类型：1用户申请-待发货 2用户申请-已完成 3管理员手动 4系统自动
   */
  async refundOrder(orderId: string, refundReason?: string, refundType: string = '3'): Promise<ResultData> {
    try {
      this.logger.log(`[DEBUG] 订单退款: OrderId=${orderId}, Reason=${refundReason}, RefundType=${refundType}`);

      // 查找订单
      const order = await this.orderRepository.findOne({
        where: { orderId: orderId },
      });

      if (!order) {
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }

      // 检查订单状态（支持配送员确认取货场景）
      if (refundType === '2') {
        // 配送员确认取货场景：只允许退款中状态
        if (order.status !== '7') {
          throw new HttpException('配送员确认取货只能处理退款中的订单', HttpStatus.BAD_REQUEST);
        }
      } else {
        // 管理员退款场景：只允许待发货或配送中状态
        if (!['2', '3'].includes(order.status)) {
          throw new HttpException('只有待发货或配送中的订单才能退款', HttpStatus.BAD_REQUEST);
        }
      }

      // 查找支付记录，确定支付方式
      const payment = await this.paymentRepository.findOne({
        where: {
          orderId: orderId,
          paymentStatus: '2', // 支付成功
        },
        order: { createTime: 'DESC' },
      });

      if (!payment) {
        throw new HttpException('未找到有效的支付记录', HttpStatus.NOT_FOUND);
      }

      // 统一调用PaymentService处理退款
      this.logger.log(`[DEBUG] 处理退款: PaymentId=${payment.paymentId}, Method=${payment.paymentMethod}`);
      const refundResult = await this.processRefund(payment, order.userId, refundReason);

      if (!refundResult.success) {
        throw new HttpException(refundResult.message || '退款处理失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // 更新订单状态为已取消
      order.status = '5';
      order.cancelReason = refundReason || '管理员退款';
      order.refundReason = refundReason;
      order.refundType = refundType;
      order.refundTime = new Date();
      order.updateBy = 'admin';
      order.updateTime = new Date();

      await this.orderRepository.save(order);

      this.logger.log(`[DEBUG] 订单退款成功: OrderId=${orderId}`);
      return ResultData.ok(refundResult, '订单退款成功');
    } catch (error) {
      this.logger.error(`[ERROR] 订单退款失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('订单退款失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 处理退款（统一调用PaymentService）
   * @param payment 支付记录
   * @param userId 用户ID
   * @param refundReason 退款原因
   */
  private async processRefund(payment: PaymentEntity, userId: number, refundReason?: string): Promise<any> {
    try {
      // 构建退款请求参数
      const refundRequest = {
        paymentId: payment.paymentId,
        refundAmount: Number(payment.paymentAmount),
        refundReason: refundReason || '管理员退款',
      };

      // 统一调用支付服务的退款接口
      const refundResult = await this.paymentService.requestRefund(refundRequest, userId);

      if (refundResult.code === 200) {
        return { success: true, data: refundResult.data };
      } else {
        return { success: false, message: refundResult.msg || '退款失败' };
      }
    } catch (error) {
      this.logger.error(`[ERROR] 退款失败: ${error.message}`, error.stack);
      return { success: false, message: `退款失败: ${error.message}` };
    }
  }

  /**
   * 导出订单数据（管理端）
   * @param queryDto 查询条件
   */
  async exportOrders(queryDto: OrderQueryDto): Promise<ResultData> {
    try {
      this.logger.log(`[DEBUG] 导出订单数据: ${JSON.stringify(queryDto)}`);

      const queryBuilder = this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.user', 'user')
        .leftJoinAndSelect('order.orderItems', 'orderItems')
        .where('order.delFlag = :delFlag', { delFlag: '0' });

      // 添加查询条件
      if (queryDto.orderId) {
        queryBuilder.andWhere('order.orderId LIKE :orderId', {
          orderId: `%${queryDto.orderId}%`,
        });
      }

      if (queryDto.status) {
        queryBuilder.andWhere('order.status = :status', { status: queryDto.status });
      }

      if (queryDto.orderType) {
        queryBuilder.andWhere('order.orderType = :orderType', { orderType: queryDto.orderType });
      }

      // 不分页，导出所有符合条件的数据
      const orders = await queryBuilder.orderBy('order.createTime', 'DESC').getMany();

      // 转换为导出格式
      const exportData = orders.map((order) => ({
        订单号: order.orderId,
        用户昵称: order.user?.nickname || '-',
        订单类型: order.orderType === '1' ? '普通订单' : '团购订单',
        订单总额: order.totalAmount,
        优惠金额: order.discountAmount,
        实付金额: order.finalAmount,
        订单状态: this.getStatusText(order.status),
        配送方式: order.deliveryType === '1' ? '配送' : '自提',
        收货人: order.receiverName,
        收货电话: order.receiverPhone,
        收货地址: order.receiverAddress,
        下单时间: order.createTime,
        支付时间: order.paymentTime || '-',
        发货时间: order.shipmentTime || '-',
        完成时间: order.completionTime || '-',
        取消原因: order.cancelReason || '-',
      }));

      return ResultData.ok(exportData, `成功导出 ${exportData.length} 条订单数据`);
    } catch (error) {
      this.logger.error(`[ERROR] 导出订单数据失败: ${error.message}`, error.stack);
      throw new HttpException('导出订单数据失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 批量取消订单（管理端）
   * @param orderIds 订单ID数组
   */
  async batchCancelOrders(orderIds: string[]): Promise<ResultData> {
    try {
      this.logger.log(`[DEBUG] 批量取消订单: OrderIds=${orderIds.join(',')}`);

      if (!orderIds || orderIds.length === 0) {
        throw new HttpException('订单ID不能为空', HttpStatus.BAD_REQUEST);
      }

      // 查找所有订单
      const orders = await this.orderRepository.find({
        where: {
          orderId: In(orderIds),
          delFlag: '0',
        },
      });

      if (orders.length !== orderIds.length) {
        throw new HttpException('部分订单不存在', HttpStatus.BAD_REQUEST);
      }

      // 检查订单状态
      const invalidOrders = orders.filter((order) => !['1', '2'].includes(order.status));
      if (invalidOrders.length > 0) {
        const invalidOrderIds = invalidOrders.map((order) => order.orderId);
        throw new HttpException(`订单 ${invalidOrderIds.join(', ')} 状态不允许取消`, HttpStatus.BAD_REQUEST);
      }

      // 批量更新订单状态
      const updateResult = await this.orderRepository.update(
        { orderId: In(orderIds) },
        {
          status: '5', // 已取消
          cancelReason: '管理员批量取消',
          updateBy: 'admin',
          updateTime: new Date(),
        },
      );

      this.logger.log(`[DEBUG] 批量取消订单成功: 影响行数=${updateResult.affected}`);
      return ResultData.ok(null, `成功取消 ${updateResult.affected} 个订单`);
    } catch (error) {
      this.logger.error(`[ERROR] 批量取消订单失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('批量取消订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取状态文本
   * @param status 状态值
   * @returns 状态文本
   */
  private getStatusText(status: string): string {
    const statusMap = {
      '1': '待支付',
      '2': '待发货',
      '3': '配送中',
      '4': '已完成',
      '5': '已取消',
      '6': '团购失败已退款',
    };
    return statusMap[status] || status;
  }

  // ==================== 退款申请相关方法 ====================

  /**
   * 创建退款申请（小程序端）
   * @param userId 用户ID
   * @param orderId 订单ID
   * @param createRefundRequestDto 创建退款申请DTO
   */
  async createRefundRequest(userId: number, orderId: string, createRefundRequestDto: CreateRefundRequestDto): Promise<ResultData> {
    return await this.dataSource.transaction(async (manager) => {
      try {
        this.logger.log(`[DEBUG] 创建退款申请: UserId=${userId}, OrderId=${orderId}, Data=${JSON.stringify(createRefundRequestDto)}`);

        // 1. 验证订单是否存在且属于该用户
        const order = await manager.findOne(OrderEntity, {
          where: { orderId, userId, delFlag: '0' },
        });

        if (!order) {
          throw new HttpException('订单不存在或无权限', HttpStatus.NOT_FOUND);
        }

        // 2. 验证订单状态和退款规则
        if (order.status === OrderStatus.SHIPPING) {
          throw new HttpException('配送中的订单无法申请退款', HttpStatus.BAD_REQUEST);
        }

        if (![OrderStatus.PENDING_SHIPMENT, OrderStatus.COMPLETED].includes(order.status as OrderStatus)) {
          throw new HttpException('只有待发货或已完成的订单才能申请退款', HttpStatus.BAD_REQUEST);
        }

        // 3. 检查是否已有退款申请或已经在退款流程中
        if (order.status === OrderStatus.REFUNDING || order.status === OrderStatus.CANCELLED) {
          throw new HttpException('该订单已在退款流程中或已取消', HttpStatus.BAD_REQUEST);
        }

        // 4. 验证退款金额不能超过订单实付金额
        if (createRefundRequestDto.refundAmount > Number(order.finalAmount)) {
          throw new HttpException('退款金额不能超过订单实付金额', HttpStatus.BAD_REQUEST);
        }

        // 5. 根据订单状态执行不同的退款逻辑
        if (order.status === OrderStatus.PENDING_SHIPMENT) {
          // 待发货订单：立即执行退款，状态改为已取消
          this.logger.log(`[DEBUG] 待发货订单申请退款，立即执行退款: OrderId=${orderId}`);
          
          // 执行退款操作
          const refundResult = await this.refundOrder(orderId, createRefundRequestDto.refundReason, '1');
          if (refundResult.code !== 200) {
            throw new HttpException('退款处理失败', HttpStatus.INTERNAL_SERVER_ERROR);
          }

          // 更新订单状态和退款信息
          order.status = OrderStatus.CANCELLED;
          order.refundReason = createRefundRequestDto.refundReason;
          order.refundType = '1'; // 用户申请-待发货
          order.refundTime = new Date();
          await manager.save(OrderEntity, order);

          // 创建退款申请记录（已退款状态）
          const refundRequest = manager.create(RefundRequestEntity, {
            orderId,
            userId,
            refundReason: createRefundRequestDto.refundReason,
            refundAmount: createRefundRequestDto.refundAmount,
            status: RefundRequestStatus.REFUNDED,
            requestTime: new Date(),
            processTime: new Date(),
          });
          await manager.save(RefundRequestEntity, refundRequest);

          this.logger.log(`[DEBUG] 待发货订单退款成功: OrderId=${orderId}`);
          return ResultData.ok(refundRequest, '退款申请已提交并自动处理完成');

        } else if (order.status === OrderStatus.COMPLETED) {
          // 已完成订单：状态改为退款中，等配送员取货
          this.logger.log(`[DEBUG] 已完成订单申请退款，等待配送员取货: OrderId=${orderId}`);

          // 更新订单状态和退款信息
          order.status = OrderStatus.REFUNDING;
          order.refundReason = createRefundRequestDto.refundReason;
          order.refundType = '2'; // 用户申请-已完成
          await manager.save(OrderEntity, order);

          // 创建退款申请记录（退款中状态）
          const refundRequest = manager.create(RefundRequestEntity, {
            orderId,
            userId,
            refundReason: createRefundRequestDto.refundReason,
            refundAmount: createRefundRequestDto.refundAmount,
            status: RefundRequestStatus.PENDING, // 这里用PENDING表示退款中
            requestTime: new Date(),
          });
          await manager.save(RefundRequestEntity, refundRequest);

          this.logger.log(`[DEBUG] 已完成订单退款申请创建成功: OrderId=${orderId}`);
          return ResultData.ok(refundRequest, '退款申请已提交，等待配送员上门取货后完成退款');
        }

      } catch (error) {
        this.logger.error(`[ERROR] 创建退款申请失败: ${error.message}`, error.stack);
        if (error instanceof HttpException) {
          throw error;
        }
        throw new HttpException('创建退款申请失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });
  }

  /**
   * 配送员确认取货（已完成订单退款流程）
   * @param orderId 订单ID
   * @param deliveryStaffId 配送员ID（可选）
   */
  async confirmRefundPickup(orderId: string, deliveryStaffId?: number): Promise<ResultData> {
    return await this.dataSource.transaction(async (manager) => {
      try {
        this.logger.log(`[DEBUG] 配送员确认取货: OrderId=${orderId}, DeliveryStaffId=${deliveryStaffId}`);

        // 1. 验证订单是否存在且状态为退款中
        const order = await manager.findOne(OrderEntity, {
          where: { orderId, status: OrderStatus.REFUNDING, delFlag: '0' },
        });

        if (!order) {
          throw new HttpException('订单不存在或状态不正确', HttpStatus.NOT_FOUND);
        }

        // 2. 查找对应的退款申请记录
        const refundRequest = await manager.findOne(RefundRequestEntity, {
          where: { orderId, status: RefundRequestStatus.PENDING },
        });

        if (!refundRequest) {
          throw new HttpException('未找到对应的退款申请记录', HttpStatus.NOT_FOUND);
        }

        // 3. 执行退款操作
        const refundResult = await this.refundOrder(orderId, order.refundReason, '2');
        if (refundResult.code !== 200) {
          throw new HttpException('退款处理失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // 4. 更新订单状态为已取消
        order.status = OrderStatus.CANCELLED;
        order.refundTime = new Date();
        order.updateBy = deliveryStaffId ? `delivery_${deliveryStaffId}` : 'delivery_staff';
        order.updateTime = new Date();
        await manager.save(OrderEntity, order);

        // 5. 更新退款申请记录状态
        refundRequest.status = RefundRequestStatus.REFUNDED;
        refundRequest.processTime = new Date();
        await manager.save(RefundRequestEntity, refundRequest);

        this.logger.log(`[DEBUG] 配送员确认取货成功，退款完成: OrderId=${orderId}`);
        return ResultData.ok(refundRequest, '取货确认成功，退款已完成');

      } catch (error) {
        this.logger.error(`[ERROR] 配送员确认取货失败: ${error.message}`, error.stack);
        if (error instanceof HttpException) {
          throw error;
        }
        throw new HttpException('确认取货失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });
  }

  // 注意：getUserRefundRequests 方法已删除
  // 退款信息已整合到订单中，用户可通过订单列表查看退款状态

  // 注意：getRefundRequestsForAdmin 方法已删除
  // 管理员可通过订单管理页面的状态筛选功能查看退款记录

  // 注意：processRefundRequest 方法已被移除
  // 新的退款流程：
  // 1. 待发货订单退款：用户申请后自动处理
  // 2. 已完成订单退款：用户申请 -> 配送员取货确认 -> 自动退款
  
  /**
   * 处理退款申请（管理端）- 已废弃
   * @deprecated 此方法已废弃，退款现已自动化处理
   */
  async processRefundRequest_DEPRECATED(processDto: ProcessRefundRequestDto): Promise<ResultData> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`[DEBUG] 处理退款申请: ${JSON.stringify(processDto)}`);

      // 1. 查找退款申请
      const refundRequest = await queryRunner.manager.findOne(RefundRequestEntity, {
        where: { id: processDto.requestId },
        relations: ['order'],
      });

      if (!refundRequest) {
        throw new HttpException('退款申请不存在', HttpStatus.NOT_FOUND);
      }

      // 2. 验证申请状态
      if (refundRequest.status !== RefundRequestStatus.PENDING) {
        throw new HttpException('该退款申请已被处理', HttpStatus.BAD_REQUEST);
      }

      const now = new Date();

      if (processDto.action === 'approve') {
        // 批准退款：直接调用现有退款逻辑
        this.logger.log(`[DEBUG] 批准退款申请: RequestId=${processDto.requestId}, OrderId=${refundRequest.orderId}`);

        // 调用现有的退款方法
        const refundResult = await this.refundOrder(refundRequest.orderId, processDto.adminComment || '管理员批准退款申请');

        if (refundResult.code === 200) {
          // 更新退款申请状态为已退款
          await queryRunner.manager.update(RefundRequestEntity, 
            { id: processDto.requestId },
            {
              status: RefundRequestStatus.REFUNDED,
              adminComment: processDto.adminComment || '退款申请已批准',
              processTime: now,
            }
          );

          await queryRunner.commitTransaction();
          this.logger.log(`[DEBUG] 退款申请批准成功: RequestId=${processDto.requestId}`);
          return ResultData.ok(null, '退款申请批准成功，资金已退回');
        } else {
          throw new HttpException(refundResult.msg || '退款处理失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }
      } else if (processDto.action === 'reject') {
        // 拒绝退款
        this.logger.log(`[DEBUG] 拒绝退款申请: RequestId=${processDto.requestId}`);

        await queryRunner.manager.update(RefundRequestEntity,
          { id: processDto.requestId },
          {
            status: RefundRequestStatus.REJECTED,
            adminComment: processDto.adminComment || '退款申请被拒绝',
            processTime: now,
          }
        );

        await queryRunner.commitTransaction();
        this.logger.log(`[DEBUG] 退款申请拒绝成功: RequestId=${processDto.requestId}`);
        return ResultData.ok(null, '退款申请已拒绝');
      }

      throw new HttpException('无效的处理动作', HttpStatus.BAD_REQUEST);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`[ERROR] 处理退款申请失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('处理退款申请失败', HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      await queryRunner.release();
    }
  }
}
