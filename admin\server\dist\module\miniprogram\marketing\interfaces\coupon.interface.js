"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponDistributeType = exports.UserCouponStatus = exports.CouponStatus = exports.CouponType = void 0;
var CouponType;
(function (CouponType) {
    CouponType["AMOUNT"] = "1";
    CouponType["DISCOUNT"] = "2";
    CouponType["NO_THRESHOLD"] = "3";
})(CouponType || (exports.CouponType = CouponType = {}));
var CouponStatus;
(function (CouponStatus) {
    CouponStatus["INACTIVE"] = "0";
    CouponStatus["ACTIVE"] = "1";
})(CouponStatus || (exports.CouponStatus = CouponStatus = {}));
var UserCouponStatus;
(function (UserCouponStatus) {
    UserCouponStatus["UNUSED"] = "0";
    UserCouponStatus["USED"] = "1";
    UserCouponStatus["EXPIRED"] = "2";
})(UserCouponStatus || (exports.UserCouponStatus = UserCouponStatus = {}));
var CouponDistributeType;
(function (CouponDistributeType) {
    CouponDistributeType["PUBLIC"] = "1";
    CouponDistributeType["PRIVATE"] = "2";
})(CouponDistributeType || (exports.CouponDistributeType = CouponDistributeType = {}));
//# sourceMappingURL=coupon.interface.js.map