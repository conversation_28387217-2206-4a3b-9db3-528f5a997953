import { Repository } from 'typeorm';
import { CreateOperlogDto } from './dto/create-operlog.dto';
import { UpdateOperlogDto } from './dto/update-operlog.dto';
import { SysOperlogEntity } from './entities/operlog.entity';
import { Request } from 'express';
import { ResultData } from 'src/common/utils/result';
import { AxiosService } from 'src/module/common/axios/axios.service';
export declare class OperlogService {
    private readonly request;
    private readonly sysOperlogEntityRep;
    private readonly axiosService;
    constructor(request: Request & {
        user: any;
    }, sysOperlogEntityRep: Repository<SysOperlogEntity>, axiosService: AxiosService);
    create(createOperlogDto: CreateOperlogDto): string;
    findAll(query: any): Promise<ResultData>;
    removeAll(): Promise<ResultData>;
    findOne(id: number): string;
    update(id: number, updateOperlogDto: UpdateOperlogDto): string;
    remove(id: number): string;
    logAction({ resultData, costTime, title, handlerName, errorMsg, businessType, }: {
        resultData?: any;
        costTime: number;
        title: string;
        handlerName: string;
        errorMsg?: string;
        businessType: number;
    }): Promise<void>;
}
