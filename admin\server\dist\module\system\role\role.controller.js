"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const role_service_1 = require("./role.service");
const index_1 = require("./dto/index");
const index_2 = require("../user/dto/index");
const require_premission_decorator_1 = require("../../../common/decorators/require-premission.decorator");
const user_service_1 = require("../user/user.service");
const user_decorator_1 = require("../user/user.decorator");
let RoleController = class RoleController {
    constructor(roleService, userService) {
        this.roleService = roleService;
        this.userService = userService;
    }
    create(createRoleDto) {
        return this.roleService.create(createRoleDto);
    }
    findAll(query, user) {
        return this.roleService.findAll(query);
    }
    deptTree(id) {
        return this.roleService.deptTree(+id);
    }
    findOne(id) {
        return this.roleService.findOne(+id);
    }
    update(updateRoleDto) {
        return this.roleService.update(updateRoleDto);
    }
    dataScope(updateRoleDto) {
        return this.roleService.dataScope(updateRoleDto);
    }
    changeStatus(changeStatusDto) {
        return this.roleService.changeStatus(changeStatusDto);
    }
    remove(ids) {
        const menuIds = ids.split(',').map((id) => +id);
        return this.roleService.remove(menuIds);
    }
    authUserAllocatedList(query) {
        return this.userService.allocatedList(query);
    }
    authUserUnAllocatedList(query) {
        return this.userService.unallocatedList(query);
    }
    authUserCancel(body) {
        return this.userService.authUserCancel(body);
    }
    authUserCancelAll(body) {
        return this.userService.authUserCancelAll(body);
    }
    authUserSelectAll(body) {
        return this.userService.authUserSelectAll(body);
    }
    async export(res, body) {
        return this.roleService.export(res, body);
    }
};
exports.RoleController = RoleController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-创建',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.CreateRoleDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:add'),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.CreateRoleDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ListRoleDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:list'),
    (0, common_1.Get)('list'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ListRoleDto, Object]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-部门树',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Get)('deptTree/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "deptTree", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-详情',
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:query'),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-修改',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdateRoleDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateRoleDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-数据权限修改',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.UpdateRoleDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)('dataScope'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.UpdateRoleDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "dataScope", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-停用角色',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.ChangeStatusDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)('changeStatus'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.ChangeStatusDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "changeStatus", null);
__decorate([
    (0, require_premission_decorator_1.RequirePermission)('system:role:remove'),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-角色已分配用户列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_2.AllocatedListDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:query'),
    (0, common_1.Get)('authUser/allocatedList'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_2.AllocatedListDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "authUserAllocatedList", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-角色未分配用户列表',
    }),
    (0, swagger_1.ApiBody)({
        type: index_2.AllocatedListDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:query'),
    (0, common_1.Get)('authUser/unallocatedList'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_2.AllocatedListDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "authUserUnAllocatedList", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-解绑角色',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.AuthUserCancelDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)('authUser/cancel'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.AuthUserCancelDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "authUserCancel", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-批量解绑角色',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.AuthUserCancelAllDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)('authUser/cancelAll'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.AuthUserCancelAllDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "authUserCancelAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '角色管理-批量绑定角色',
    }),
    (0, swagger_1.ApiBody)({
        type: index_1.AuthUserSelectAllDto,
        required: true,
    }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:edit'),
    (0, common_1.Put)('authUser/selectAll'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [index_1.AuthUserSelectAllDto]),
    __metadata("design:returntype", void 0)
], RoleController.prototype, "authUserSelectAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '导出角色管理xlsx文件' }),
    (0, require_premission_decorator_1.RequirePermission)('system:role:export'),
    (0, common_1.Post)('/export'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, index_1.ListRoleDto]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "export", null);
exports.RoleController = RoleController = __decorate([
    (0, swagger_1.ApiTags)('角色管理'),
    (0, common_1.Controller)('system/role'),
    __metadata("design:paramtypes", [role_service_1.RoleService,
        user_service_1.UserService])
], RoleController);
//# sourceMappingURL=role.controller.js.map