"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DictService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const result_1 = require("../../../common/utils/result");
const index_1 = require("../../../common/enum/index");
const export_1 = require("../../../common/utils/export");
const dict_type_entity_1 = require("./entities/dict.type.entity");
const dict_data_entity_1 = require("./entities/dict.data.entity");
const redis_service_1 = require("../../common/redis/redis.service");
let DictService = class DictService {
    constructor(sysDictTypeEntityRep, sysDictDataEntityRep, redisService) {
        this.sysDictTypeEntityRep = sysDictTypeEntityRep;
        this.sysDictDataEntityRep = sysDictDataEntityRep;
        this.redisService = redisService;
    }
    async createType(CreateDictTypeDto) {
        await this.sysDictTypeEntityRep.save(CreateDictTypeDto);
        return result_1.ResultData.ok();
    }
    async deleteType(dictIds) {
        await this.sysDictTypeEntityRep.update({ dictId: (0, typeorm_2.In)(dictIds) }, { delFlag: '1' });
        return result_1.ResultData.ok();
    }
    async updateType(updateDictTypeDto) {
        await this.sysDictTypeEntityRep.update({ dictId: updateDictTypeDto.dictId }, updateDictTypeDto);
        return result_1.ResultData.ok();
    }
    async findAllType(query) {
        const entity = this.sysDictTypeEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.dictName) {
            entity.andWhere(`entity.dictName LIKE "%${query.dictName}%"`);
        }
        if (query.dictType) {
            entity.andWhere(`entity.dictType LIKE "%${query.dictType}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOneType(dictId) {
        const data = await this.sysDictTypeEntityRep.findOne({
            where: {
                dictId: dictId,
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(data);
    }
    async findOptionselect() {
        const data = await this.sysDictTypeEntityRep.find({
            where: {
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(data);
    }
    async createDictData(createDictDataDto) {
        await this.sysDictDataEntityRep.save(createDictDataDto);
        return result_1.ResultData.ok();
    }
    async deleteDictData(dictIds) {
        await this.sysDictDataEntityRep.update({ dictCode: (0, typeorm_2.In)(dictIds) }, { delFlag: '1' });
        return result_1.ResultData.ok();
    }
    async updateDictData(updateDictDataDto) {
        await this.sysDictDataEntityRep.update({ dictCode: updateDictDataDto.dictCode }, updateDictDataDto);
        return result_1.ResultData.ok();
    }
    async findAllData(query) {
        const entity = this.sysDictDataEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        if (query.dictLabel) {
            entity.andWhere(`entity.dictLabel LIKE "%${query.dictLabel}%"`);
        }
        if (query.dictType) {
            entity.andWhere(`entity.dictType LIKE "%${query.dictType}%"`);
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async findOneDataType(dictType) {
        let data = await this.redisService.get(`${index_1.CacheEnum.SYS_DICT_KEY}${dictType}`);
        if (data) {
            return result_1.ResultData.ok(data);
        }
        data = await this.sysDictDataEntityRep.find({
            where: {
                dictType: dictType,
                delFlag: '0',
            },
        });
        await this.redisService.set(`${index_1.CacheEnum.SYS_DICT_KEY}${dictType}`, data);
        return result_1.ResultData.ok(data);
    }
    async findOneDictData(dictCode) {
        const data = await this.sysDictDataEntityRep.findOne({
            where: {
                dictCode: dictCode,
                delFlag: '0',
            },
        });
        return result_1.ResultData.ok(data);
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAllType(body);
        const options = {
            sheetName: '字典数据',
            data: list.data.list,
            header: [
                { title: '字典主键', dataIndex: 'dictId' },
                { title: '字典名称', dataIndex: 'dictName' },
                { title: '字典类型', dataIndex: 'dictType' },
                { title: '状态', dataIndex: 'status' },
            ],
        };
        (0, export_1.ExportTable)(options, res);
    }
    async exportData(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.findAllData(body);
        const options = {
            sheetName: '字典数据',
            data: list.data.list,
            header: [
                { title: '字典主键', dataIndex: 'dictCode' },
                { title: '字典名称', dataIndex: 'dictLabel' },
                { title: '字典类型', dataIndex: 'dictValue' },
                { title: '备注', dataIndex: 'remark' },
            ],
        };
        (0, export_1.ExportTable)(options, res);
    }
    async resetDictCache() {
        await this.clearDictCache();
        await this.loadingDictCache();
        return result_1.ResultData.ok();
    }
    async clearDictCache() {
        const keys = await this.redisService.keys(`${index_1.CacheEnum.SYS_DICT_KEY}*`);
        if (keys && keys.length > 0) {
            await this.redisService.del(keys);
        }
    }
    async loadingDictCache() {
        const entity = this.sysDictTypeEntityRep.createQueryBuilder('entity');
        entity.where('entity.delFlag = :delFlag', { delFlag: '0' });
        entity.leftJoinAndMapMany('entity.dictTypeList', dict_data_entity_1.SysDictDataEntity, 'dictType', 'dictType.dictType = entity.dictType');
        const list = await entity.getMany();
        list.forEach((item) => {
            if (item.dictType) {
                this.redisService.set(`${index_1.CacheEnum.SYS_DICT_KEY}${item.dictType}`, item.dictTypeList);
            }
        });
    }
};
exports.DictService = DictService;
exports.DictService = DictService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(dict_type_entity_1.SysDictTypeEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(dict_data_entity_1.SysDictDataEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService])
], DictService);
//# sourceMappingURL=dict.service.js.map