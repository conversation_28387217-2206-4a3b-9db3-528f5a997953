import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { RedisService } from 'src/module/common/redis/redis.service';
import { Response } from 'express';
import { ResultData } from 'src/common/utils/result';
import { CreateUserDto, UpdateUserDto, ListUserDto, ChangeStatusDto, ResetPwdDto, AllocatedListDto, UpdateProfileDto, UpdatePwdDto } from './dto/index';
import { RegisterDto, LoginDto } from '../../main/dto/index';
import { AuthUserCancelDto, AuthUserCancelAllDto, AuthUserSelectAllDto } from '../role/dto/index';
import { UserEntity } from './entities/sys-user.entity';
import { SysUserWithPostEntity } from './entities/user-width-post.entity';
import { SysUserWithRoleEntity } from './entities/user-width-role.entity';
import { SysPostEntity } from '../post/entities/post.entity';
import { SysDeptEntity } from '../dept/entities/dept.entity';
import { RoleService } from '../role/role.service';
import { DeptService } from '../dept/dept.service';
import { ConfigService } from '../config/config.service';
import { SysRoleEntity } from '../role/entities/role.entity';
import { UserType } from './dto/user';
import { ClientInfoDto } from 'src/common/decorators/common.decorator';
export declare class UserService {
    private readonly userRepo;
    private readonly sysDeptEntityRep;
    private readonly sysPostEntityRep;
    private readonly sysUserWithPostEntityRep;
    private readonly sysUserWithRoleEntityRep;
    private readonly roleService;
    private readonly deptService;
    private readonly jwtService;
    private readonly redisService;
    private readonly configService;
    constructor(userRepo: Repository<UserEntity>, sysDeptEntityRep: Repository<SysDeptEntity>, sysPostEntityRep: Repository<SysPostEntity>, sysUserWithPostEntityRep: Repository<SysUserWithPostEntity>, sysUserWithRoleEntityRep: Repository<SysUserWithRoleEntity>, roleService: RoleService, deptService: DeptService, jwtService: JwtService, redisService: RedisService, configService: ConfigService);
    create(createUserDto: CreateUserDto): Promise<ResultData>;
    findAll(query: ListUserDto, user: UserType['user']): Promise<ResultData>;
    findPostAndRoleAll(): Promise<ResultData>;
    findOne(userId: number): Promise<ResultData>;
    update(updateUserDto: UpdateUserDto, userId: number): Promise<ResultData>;
    clearCacheByUserId(userId: number): number;
    login(user: LoginDto, clientInfo: ClientInfoDto): Promise<ResultData>;
    updateRedisUserRolesAndPermissions(uuid: string, userId: number): Promise<void>;
    updateRedisToken(token: string, metaData: Partial<UserType>): Promise<void>;
    getRoleIds(userIds: Array<number>): Promise<number[]>;
    getUserPermissions(userId: number): Promise<string[]>;
    getUserinfo(userId: number): Promise<{
        dept: SysDeptEntity;
        roles: Array<SysRoleEntity>;
        posts: Array<SysPostEntity>;
    } & UserEntity>;
    register(user: RegisterDto): Promise<ResultData>;
    createToken(payload: {
        uuid: string;
        userId: number;
    }): string;
    parseToken(token: string): any;
    resetPwd(body: ResetPwdDto): Promise<ResultData>;
    remove(ids: number[]): Promise<ResultData>;
    authRole(userId: number): Promise<ResultData>;
    updateAuthRole(query: any): Promise<ResultData>;
    changeStatus(changeStatusDto: ChangeStatusDto): Promise<ResultData>;
    deptTree(): Promise<ResultData>;
    allocatedList(query: AllocatedListDto): Promise<ResultData>;
    unallocatedList(query: AllocatedListDto): Promise<ResultData>;
    authUserCancel(data: AuthUserCancelDto): Promise<ResultData>;
    authUserCancelAll(data: AuthUserCancelAllDto): Promise<ResultData>;
    authUserSelectAll(data: AuthUserSelectAllDto): Promise<ResultData>;
    profile(user: any): Promise<ResultData>;
    updateProfile(user: UserType, updateProfileDto: UpdateProfileDto): Promise<ResultData>;
    updatePwd(user: UserType, updatePwdDto: UpdatePwdDto): Promise<ResultData>;
    export(res: Response, body: ListUserDto, user: UserType['user']): Promise<void>;
}
