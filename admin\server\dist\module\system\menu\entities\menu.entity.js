"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysMenuEntity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const base_1 = require("../../../../common/entities/base");
class NewBaseEntity extends base_1.BaseEntity {
    constructor() {
        super();
        delete this.delFlag;
    }
}
let SysMenuEntity = class SysMenuEntity extends NewBaseEntity {
};
exports.SysMenuEntity = SysMenuEntity;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '菜单ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'int', name: 'menu_id', comment: '菜单ID' }),
    __metadata("design:type", Number)
], SysMenuEntity.prototype, "menuId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'menu_name', length: 50, comment: '菜单名称' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "menuName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, description: '父菜单ID' }),
    (0, typeorm_1.Column)({ type: 'int', name: 'parent_id', comment: '父菜单ID' }),
    __metadata("design:type", Number)
], SysMenuEntity.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'order_num', default: 0, comment: '显示顺序' }),
    __metadata("design:type", Number)
], SysMenuEntity.prototype, "orderNum", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'path', length: 200, default: '', comment: '路由地址' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'component', length: 255, nullable: true, comment: '组件路径' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "component", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'query', length: 255, default: '', comment: '路由参数' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "query", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'is_frame', default: '1', comment: '是否为外链' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "isFrame", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'is_cache', default: '0', comment: '是否缓存' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "isCache", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'visible', default: '0', comment: '是否显示' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "visible", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'char', name: 'menu_type', length: 1, default: 'M', comment: '菜单类型' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "menuType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'perms', length: 100, default: '', comment: '权限标识' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "perms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'icon', length: 100, default: '', comment: '菜单图标' }),
    __metadata("design:type", String)
], SysMenuEntity.prototype, "icon", void 0);
exports.SysMenuEntity = SysMenuEntity = __decorate([
    (0, typeorm_1.Entity)('sys_menu', {
        comment: '菜单权限表',
    })
], SysMenuEntity);
//# sourceMappingURL=menu.entity.js.map