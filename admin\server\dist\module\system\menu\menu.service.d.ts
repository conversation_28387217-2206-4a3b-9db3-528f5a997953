import { Repository, FindManyOptions } from 'typeorm';
import { ResultData } from 'src/common/utils/result';
import { SysMenuEntity } from './entities/menu.entity';
import { SysRoleWithMenuEntity } from '../role/entities/role-width-menu.entity';
import { CreateMenuDto, UpdateMenuDto, ListDeptDto } from './dto/index';
import { UserService } from '../user/user.service';
export declare class MenuService {
    private readonly userService;
    private readonly sysMenuEntityRep;
    private readonly sysRoleWithMenuEntityRep;
    constructor(userService: UserService, sysMenuEntityRep: Repository<SysMenuEntity>, sysRoleWithMenuEntityRep: Repository<SysRoleWithMenuEntity>);
    create(createMenuDto: CreateMenuDto): Promise<ResultData>;
    findAll(query: ListDeptDto): Promise<ResultData>;
    treeSelect(): Promise<ResultData>;
    roleMenuTreeselect(roleId: number): Promise<any>;
    findOne(menuId: number): Promise<ResultData>;
    update(updateMenuDto: UpdateMenuDto): Promise<ResultData>;
    remove(menuId: number): Promise<ResultData>;
    findMany(where: FindManyOptions<SysMenuEntity>): Promise<SysMenuEntity[]>;
    getMenuListByUserId(userId: number): Promise<any[]>;
}
