"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginlogModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const loginlog_service_1 = require("./loginlog.service");
const loginlog_controller_1 = require("./loginlog.controller");
const loginlog_entity_1 = require("./entities/loginlog.entity");
let LoginlogModule = class LoginlogModule {
};
exports.LoginlogModule = LoginlogModule;
exports.LoginlogModule = LoginlogModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([loginlog_entity_1.MonitorLoginlogEntity])],
        controllers: [loginlog_controller_1.LoginlogController],
        providers: [loginlog_service_1.LoginlogService],
        exports: [loginlog_service_1.LoginlogService],
    })
], LoginlogModule);
//# sourceMappingURL=loginlog.module.js.map