import { Injectable, Logger, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Brackets } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { PaymentEntity } from './entities/payment.entity';
import { OrderEntity } from '../order/entities/order.entity';
import { MiniprogramUser } from '../user/entities/user.entity';
import { OrderService } from '../order/order.service';
import { UpdateOrderDto } from '../order/dto/update-order.dto';
import { PaymentRequestDto, PaymentNotifyDto, RefundRequestDto, UserBalanceRechargeDto, BalanceRechargeDto } from './dto/payment-request.dto';
import {
  PaymentResultDto,
  PaymentListDto,
  RefundResultDto,
  UserBalanceRechargeResultDto,
  BalanceRechargeResultDto,
  BalanceChangeListDto,
  BalanceChangeOrderInfoDto,
  BalanceChangeOrderItemDto,
} from './dto/payment-response.dto';
import { ResultData } from '../../../common/utils/result';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly wechatConfig: any;
  private readonly certSerialNo: string;

  constructor(
    @InjectRepository(PaymentEntity)
    private readonly paymentRepository: Repository<PaymentEntity>,
    @InjectRepository(OrderEntity)
    private readonly orderRepository: Repository<OrderEntity>,
    @InjectRepository(MiniprogramUser)
    private readonly userRepository: Repository<MiniprogramUser>,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => OrderService))
    private readonly orderService: OrderService,
  ) {
    this.wechatConfig = this.configService.get('wechat');
    this.logger.log(`微信支付配置加载成功: appid=${this.wechatConfig.appid}`);
    // 从配置中获取证书序列号
    this.certSerialNo = this.wechatConfig.pay.certSerialNo || '1234567890';
    this.logger.log(`微信支付证书序列号: ${this.certSerialNo}`);
  }

  /**
   * 创建支付订单
   * @param paymentRequest 支付请求参数
   * @param userId 用户ID
   * @returns 支付结果
   */
  async createPayment(paymentRequest: PaymentRequestDto, userId: number): Promise<ResultData> {
    try {
      this.logger.log(`用户 ${userId} 创建支付订单，订单ID: ${paymentRequest.orderId}`);
      // 1. 验证订单是否存在且属于当前用户
      const order = await this.orderRepository.findOne({
        where: { orderId: paymentRequest.orderId, userId: userId },
      });
      if (!order) {
        this.logger.error(`订单不存在或不属于当前用户: ${paymentRequest.orderId}, 用户ID: ${userId}`);
        throw new HttpException('订单不存在或无权限操作', HttpStatus.BAD_REQUEST);
      }
      // 2. 检查订单状态是否可支付
      if (order.status !== '1') {
        this.logger.error(`订单状态不允许支付: ${order.orderId}, 状态: ${order.status}`);
        throw new HttpException('订单状态不允许支付', HttpStatus.BAD_REQUEST);
      }
      // 3. 从订单表中获取支付金额
      const paymentAmount = Number(order.finalAmount);
      this.logger.log(`从订单获取支付金额: ${paymentAmount}`);
      // 4. 查询用户信息，获取openid
      const user = await this.userRepository.findOne({
        where: { userId: userId },
      });
      if (!user || !user.openid) {
        this.logger.error(`用户不存在或未绑定微信openid: ${userId}`);
        throw new HttpException('用户未绑定微信，无法使用微信支付', HttpStatus.BAD_REQUEST);
      }
      this.logger.log(`获取用户openid成功: ${user.openid.substring(0, 5)}***`);
      // 5. 检查是否已存在待支付记录
      const existingPayment = await this.paymentRepository.findOne({
        where: { orderId: paymentRequest.orderId, paymentStatus: '1' },
      });
      if (existingPayment) {
        this.logger.warn(`订单已存在待支付记录: ${paymentRequest.orderId}`);
        // 生成支付参数
        let paymentParams = null;
        if (paymentRequest.paymentMethod === '1') {
          // 微信支付，返回支付参数
          paymentParams = await this.generateWechatPayParams(existingPayment, user.openid);
        } else if (paymentRequest.paymentMethod === '2') {
          // 余额支付，这里需要实现余额支付逻辑
          // 余额支付，处理余额扣款和订单状态更新
          await this.handleBalancePayment(existingPayment, user);
          existingPayment.paymentStatus = '2'; // 余额支付成功
          existingPayment.paymentTime = new Date(); // 记录支付时间
        }
        // 返回已存在的支付记录，并附带支付参数
        const result: PaymentResultDto = {
          paymentId: existingPayment.paymentId,
          orderId: existingPayment.orderId,
          paymentStatus: existingPayment.paymentStatus,
          paymentAmount: Number(existingPayment.paymentAmount),
          transactionId: existingPayment.transactionId,
          paymentTime: existingPayment.paymentTime,
          paymentParams: paymentParams,
        };
        return ResultData.ok(result, '支付订单已存在');
      }
      // 5. 创建支付记录
      const payment = new PaymentEntity();
      payment.orderId = paymentRequest.orderId;
      payment.userId = userId;
      payment.paymentMethod = paymentRequest.paymentMethod;
      payment.paymentAmount = paymentAmount;
      payment.paymentStatus = '1'; // 待支付
      payment.createBy = userId.toString();
      const savedPayment = await this.paymentRepository.save(payment);
      this.logger.log(`支付记录创建成功: ID=${savedPayment.paymentId}`);

      // 6. 根据支付方式处理不同逻辑
      let paymentParams = null;
      if (paymentRequest.paymentMethod === '1') {
        // 微信支付，返回支付参数
        paymentParams = await this.generateWechatPayParams(savedPayment, user.openid);
      } else if (paymentRequest.paymentMethod === '2') {
        // 余额支付，处理余额扣款和订单状态更新
        await this.handleBalancePayment(savedPayment, user);
        savedPayment.paymentStatus = '2'; // 余额支付成功
        savedPayment.paymentTime = new Date(); // 记录支付时间
      }
      const result: PaymentResultDto = {
        paymentId: savedPayment.paymentId,
        orderId: savedPayment.orderId,
        paymentStatus: savedPayment.paymentStatus,
        paymentAmount: Number(savedPayment.paymentAmount),
        paymentParams: paymentParams,
      };
      return ResultData.ok(result, '支付订单创建成功');
    } catch (error) {
      this.logger.error(`创建支付订单失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('创建支付订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建余额充值订单
   * @param rechargeDto 充值请求参数
   * @param userId 用户ID
   * @returns 充值结果
   */
  async createBalanceRecharge(rechargeDto: BalanceRechargeDto, userId: number): Promise<ResultData> {
    try {
      this.logger.log(`用户 ${userId} 创建余额充值订单，金额: ${rechargeDto.amount}`);
      // 1. 查询用户信息，获取openid
      const user = await this.userRepository.findOne({
        where: { userId: userId },
      });
      if (!user || !user.openid) {
        this.logger.error(`用户不存在或未绑定微信openid: ${userId}`);
        throw new HttpException('用户未绑定微信，无法使用微信支付', HttpStatus.BAD_REQUEST);
      }
      this.logger.log(`获取用户openid成功: ${user.openid.substring(0, 5)}***`);
      // 2. 生成充值单号
      const rechargeId = `RECHARGE_${this.generateTimestamp()}_${this.generateRandomString(6)}`;
      this.logger.log(`生成充值单号: ${rechargeId}`);
      // 3. 创建支付记录
      const payment = new PaymentEntity();
      payment.orderId = rechargeId; // 使用充值单号作为orderId
      payment.userId = userId;
      payment.paymentMethod = '1'; // 余额充值目前只支持微信支付
      payment.paymentAmount = rechargeDto.amount;
      payment.paymentStatus = '1'; // 待支付
      payment.createBy = userId.toString();
      const savedPayment = await this.paymentRepository.save(payment);
      this.logger.log(`充值支付记录创建成功: ID=${savedPayment.paymentId}`);
      // 4. 根据支付方式处理不同逻辑 (目前只支持微信支付)
      const paymentParams = await this.generateWechatPayParams(savedPayment, user.openid, '余额充值');
      const result: BalanceRechargeResultDto = {
        paymentId: savedPayment.paymentId,
        rechargeId: rechargeId,
        paymentStatus: savedPayment.paymentStatus,
        amount: Number(savedPayment.paymentAmount),
        paymentParams: paymentParams,
      };
      return ResultData.ok(result, '充值订单创建成功');
    } catch (error) {
      this.logger.error(`创建充值订单失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('创建充值订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 为用户账户充值余额（管理员操作）
   * @param rechargeDto 充值请求参数
   * @param operatorId 操作人ID
   * @returns 充值结果
   */
  async rechargeUserBalance(rechargeDto: UserBalanceRechargeDto, operatorId: number): Promise<ResultData> {
    try {
      this.logger.log(`为用户 ${rechargeDto.userId} 充值余额，金额: ${rechargeDto.amount}`);
      // 1. 查询用户信息
      const user = await this.userRepository.findOne({
        where: { userId: rechargeDto.userId },
      });
      if (!user) {
        this.logger.error(`用户不存在: ${rechargeDto.userId}`);
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }
      // 2. 记录充值前的余额
      const beforeBalance = Number(user.balance) || 0;
      this.logger.log(`用户当前余额: ${beforeBalance}`);
      // 3. 更新用户余额
      user.balance = beforeBalance + rechargeDto.amount;
      user.updateBy = operatorId.toString();
      user.updateTime = new Date();
      // 4. 保存更新后的用户信息
      await this.userRepository.save(user);
      this.logger.log(`用户余额更新成功: ${user.userId}, 当前余额: ${user.balance}`);
      // 5. 构造并返回结果
      const result: UserBalanceRechargeResultDto = {
        userId: user.userId,
        beforeBalance: beforeBalance,
        amount: rechargeDto.amount,
        afterBalance: Number(user.balance),
        rechargeTime: new Date(),
        operator: operatorId.toString(),
        remark: rechargeDto.remark,
      };
      return ResultData.ok(result, '用户余额充值成功');
    } catch (error) {
      this.logger.error(`用户余额充值失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('用户余额充值失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 生成微信支付参数
   * @param payment 支付记录
   * @param openid 用户openid
   * @param description 支付描述
   * @returns 微信支付参数
   */
  async generateWechatPayParams(payment: PaymentEntity, openid: string, description?: string): Promise<any> {
    try {
      this.logger.log(`生成微信支付参数: ${payment.paymentId}, 使用商户号: ${this.wechatConfig.pay.mchId}`);
      this.logger.log(`用户openid: ${openid.substring(0, 5)}***`);
      // 1. 构建预支付订单所需参数
      const timestamp = Math.floor(Date.now() / 1000).toString();
      const nonceStr = this.generateNonceStr();
      const notifyUrl = this.wechatConfig.pay.notifyUrl.replace('{orderId}', payment.orderId);
      // 2. 构建预支付订单请求参数
      const prepayParams = {
        appid: this.wechatConfig.appid,
        mchid: this.wechatConfig.pay.mchId,
        description: description || `初鲜果味订单-${payment.orderId}`,
        out_trade_no: payment.orderId,
        notify_url: notifyUrl,
        amount: {
          total: Math.floor(Number(payment.paymentAmount) * 100), // 转换为分
          currency: 'CNY',
        },
        payer: {
          openid: openid, // 使用传入的用户openid
        },
      };
      this.logger.log(`预支付订单参数: ${JSON.stringify(prepayParams)}`);
      // 3. 调用微信支付API获取预支付订单ID
      // 获取私钥用于签名
      const rootDir = process.cwd();
      const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
      const privateKeyPath = path.join(rootDir, relativePath);
      this.logger.log(`私钥路径: ${privateKeyPath}`);
      if (!fs.existsSync(privateKeyPath)) {
        this.logger.error(`商户私钥文件不存在: ${privateKeyPath}`);
        throw new Error('商户私钥文件不存在，请配置正确的私钥路径');
      }
      const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
      this.logger.log(`私钥读取成功，长度: ${privateKey.length}`);
      // 构建请求签名
      const method = 'POST';
      const url = '/v3/pay/transactions/jsapi';
      const requestTimestamp = Math.floor(Date.now() / 1000).toString();
      const requestNonce = this.generateNonceStr();
      const requestBody = JSON.stringify(prepayParams);
      const message = `${method}\n${url}\n${requestTimestamp}\n${requestNonce}\n${requestBody}\n`;
      this.logger.log(`签名原文: ${message}`);
      try {
        const sign = crypto.createSign('RSA-SHA256');
        sign.update(message);
        const signature = sign.sign(privateKey, 'base64');
        this.logger.log(`签名生成成功，签名值: ${signature.substring(0, 10)}***`);
        // 构建认证头 - 修复格式问题
        // 正确格式: WECHATPAY2-SHA256-RSA2048 mchid="商户号",nonce_str="随机字符串",timestamp="时间戳",serial_no="证书序列号",signature="签名"
        const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.wechatConfig.pay.mchId}",nonce_str="${requestNonce}",timestamp="${requestTimestamp}",serial_no="${this.certSerialNo}",signature="${signature}"`;
        this.logger.log(`认证头: ${authHeader}`);
        // 调用微信支付API
        this.logger.log(`开始调用微信支付API...`);
        const response = await axios.post('https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi', prepayParams, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: authHeader,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
          },
        });
        this.logger.log(`微信支付API响应: ${JSON.stringify(response.data)}`);
        const prepayId = response.data.prepay_id;
        if (!prepayId) {
          throw new Error('获取预支付订单ID失败');
        }
        // 4. 生成支付签名
        // 签名内容: 应用ID\n时间戳\n随机字符串\n预支付交易会话ID\n
        const signContent = `${this.wechatConfig.appid}\n${timestamp}\n${nonceStr}\nprepay_id=${prepayId}\n`;
        const paySign = this.generatePaySign(signContent);
        // 5. 返回支付参数
        const payParams = {
          appId: this.wechatConfig.appid,
          timeStamp: timestamp,
          nonceStr: nonceStr,
          package: `prepay_id=${prepayId}`,
          signType: 'RSA',
          paySign: paySign,
        };
        this.logger.log(`生成支付参数成功: ${JSON.stringify(payParams)}`);
        return payParams;
      } catch (signError) {
        this.logger.error(`签名生成或API调用失败: ${signError.message}`);
        if (signError.response) {
          this.logger.error(`API错误详情: ${JSON.stringify(signError.response.data || {})}`);
        }
        throw signError;
      }
    } catch (error) {
      this.logger.error(`生成微信支付参数失败: ${error.message}`);
      if (error.response) {
        this.logger.error(`微信支付API错误详情: ${JSON.stringify(error.response.data || {})}`);
        throw new HttpException(`微信支付API错误: ${error.response.data?.message || error.message}`, HttpStatus.BAD_REQUEST);
      }
      throw new HttpException(`生成支付参数失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 生成随机字符串
   * @returns 随机字符串
   */
  private generateNonceStr(length = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成微信支付签名
   * @param signContent 签名内容
   * @returns 签名结果
   */
  private generatePaySign(signContent: string): string {
    try {
      // 获取项目根目录，无论是开发环境还是生产环境
      const rootDir = process.cwd();
      // 处理配置文件中的相对路径，移除开头的./或../
      const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
      // 使用path.join确保跨平台路径正确
      const privateKeyPath = path.join(rootDir, relativePath);
      this.logger.log(`读取商户私钥: ${privateKeyPath}`);
      // 检查私钥文件是否存在
      if (!fs.existsSync(privateKeyPath)) {
        this.logger.error(`商户私钥文件不存在: ${privateKeyPath}`);
        return '私钥文件不存在，请配置正确的私钥路径';
      }
      const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
      // 使用私钥对签名内容进行签名
      const sign = crypto.createSign('RSA-SHA256');
      sign.update(signContent);
      const signature = sign.sign(privateKey, 'base64');
      this.logger.log(`生成签名成功，签名内容长度: ${signContent.length}`);
      return signature;
    } catch (error) {
      this.logger.error(`生成签名失败: ${error.message}`);
      return '签名生成失败';
    }
  }

  /**
   * 处理支付成功回调
   * @param orderId 订单ID或充值单号
   * @param notifyData 回调数据
   * @returns 处理结果
   */
  async handlePaymentNotify(orderId: string, notifyData: PaymentNotifyDto): Promise<any> {
    try {
      this.logger.log(`接收到支付回调，订单ID: ${orderId}, 通知类型: ${notifyData.event_type}`);
      this.logger.log(`回调数据: ${JSON.stringify(notifyData)}`);
      // 1. 验证通知类型
      if (notifyData.event_type !== 'TRANSACTION.SUCCESS') {
        this.logger.warn(`非支付成功通知，忽略处理: ${notifyData.event_type}`);
        return { code: 'SUCCESS', message: '非支付成功通知' };
      }
      // 2. 验证加密数据
      if (!notifyData.resource || !notifyData.resource.ciphertext) {
        this.logger.error('回调数据缺少加密信息');
        throw new HttpException('回调数据格式错误', HttpStatus.BAD_REQUEST);
      }
      // 3. 解密回调数据
      const decryptedData = await this.decryptNotifyData(notifyData.resource);
      this.logger.log(`解密后的回调数据: ${JSON.stringify(decryptedData)}`);
      // 4. 查询支付记录
      const payment = await this.paymentRepository.findOne({
        where: { orderId: orderId },
      });
      if (!payment) {
        this.logger.error(`支付记录不存在: ${orderId}`);
        throw new HttpException('支付记录不存在', HttpStatus.NOT_FOUND);
      }

      // 5. 检查支付记录状态，避免重复处理
      if (payment.paymentStatus === '2') {
        this.logger.warn(`支付记录已处理，重复通知，订单ID: ${orderId}`);
        return { code: 'SUCCESS', message: '支付已成功，重复通知' };
      }

      // 6. 验证支付金额
      const notifyAmount = decryptedData.amount.total;
      const paymentAmount = Math.floor(Number(payment.paymentAmount) * 100); // 转换为分
      if (notifyAmount !== paymentAmount) {
        this.logger.error(`支付金额不匹配: 通知金额=${notifyAmount}, 订单金额=${paymentAmount}`);
        throw new HttpException('支付金额不匹配', HttpStatus.BAD_REQUEST);
      }
      // 5. 更新支付记录
      payment.paymentStatus = '2'; // 支付成功
      payment.transactionId = decryptedData.transaction_id;
      payment.paymentTime = new Date(decryptedData.success_time);
      payment.callbackData = JSON.stringify(decryptedData);
      payment.updateBy = 'system';
      payment.updateTime = new Date();
      await this.paymentRepository.save(payment);
      this.logger.log(`支付记录更新成功: ${payment.paymentId}, 状态: ${payment.paymentStatus}`);
      // 6. 处理业务逻辑
      if (orderId.startsWith('RECHARGE_')) {
        // 余额充值，更新用户余额
        await this.updateUserBalance(payment.userId, Number(payment.paymentAmount));
      } else {
        // 订单支付，更新订单状态
        await this.updateOrderStatus(orderId, '2'); // 更新为已支付状态
      }
      // 返回微信支付成功应答
      return { code: 'SUCCESS', message: '成功' };
    } catch (error) {
      this.logger.error(`处理支付回调失败: ${error.message}`);
      // 返回微信支付失败应答
      return { code: 'FAIL', message: error.message || '处理失败' };
    }
  }

  /**
   * 解密微信支付回调数据
   * @param resource 加密的资源数据
   * @returns 解密后的数据
   */
  private async decryptNotifyData(resource: any): Promise<any> {
    try {
      const { algorithm, ciphertext, associated_data, nonce } = resource;
      if (!algorithm || !ciphertext || !nonce) {
        throw new Error('缺少必要的解密参数');
      }

      this.logger.log(`开始解密回调数据: ${algorithm}`);
      this.logger.log(`解密参数: nonce=${nonce}, associated_data=${associated_data || 'undefined'}`);

      const apiKey = this.wechatConfig.pay.apiKey;
      if (!apiKey) {
        throw new Error('未配置 apiKey');
      }
      const key = Buffer.from(apiKey, 'utf8'); // 明确使用utf8编码
      this.logger.log(`加载的 apiKey 长度: ${key.length} 字节`);
      if (key.length !== 32) {
        throw new Error('无效的ApiKey，长度必须为32个字节');
      }

      // 解密
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, Buffer.from(nonce, 'utf8'));
      this.logger.log(`解密器创建成功`);

      // 设置 AAD（如果有）
      if (associated_data) {
        decipher.setAAD(Buffer.from(associated_data));
        this.logger.log(`已设置 AAD`);
      }

      // 解码密文
      const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
      this.logger.log(`密文长度: ${ciphertextBuffer.length} 字节`);

      // 从密文中提取 authTag（最后 16 字节）
      const authTag = ciphertextBuffer.slice(-16);
      const encryptedData = ciphertextBuffer.slice(0, -16);
      this.logger.log(`已提取 authTag，长度: ${authTag.length} 字节`);

      // 设置 authTag
      decipher.setAuthTag(authTag);
      this.logger.log(`已设置 authTag`);

      // 解密数据
      let decrypted = decipher.update(encryptedData);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      this.logger.log(`解密成功，解密后数据长度: ${decrypted.length} 字节`);

      // 解析 JSON
      const result = JSON.parse(decrypted.toString('utf8'));
      this.logger.log(`解析 JSON 成功: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`解密回调数据失败: ${error.message}`);
      if (error.message.includes('Unsupported state or unable to authenticate data')) {
        this.logger.error('可能是密钥不正确或数据被篡改');
      }
      throw new HttpException('解密回调数据失败', HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 更新用户余额
   * @param userId 用户ID
   * @param amount 充值金额
   */
  private async updateUserBalance(userId: number, amount: number): Promise<void> {
    try {
      this.logger.log(`更新用户余额: 用户ID=${userId}, 金额=${amount}`);
      // 查询用户
      const user = await this.userRepository.findOne({
        where: { userId: userId },
      });
      if (!user) {
        this.logger.error(`用户不存在: ${userId}`);
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }
      // 更新余额
      const currentBalance = Number(user.balance) || 0;
      user.balance = currentBalance + amount;
      user.updateBy = 'system';
      user.updateTime = new Date();
      await this.userRepository.save(user);
      this.logger.log(`用户余额更新成功: ${userId}, 当前余额: ${user.balance}`);
    } catch (error) {
      this.logger.error(`更新用户余额失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新订单状态
   * @param orderId 订单ID
   * @param status 订单状态
   */
  private async updateOrderStatus(orderId: string, status: string): Promise<void> {
    try {
      this.logger.log(`更新订单状态: 订单ID=${orderId}, 状态=${status}`);
      
      // 先查询订单获取用户ID
      const order = await this.orderRepository.findOne({
        where: { orderId: orderId },
      });
      
      if (!order) {
        this.logger.error(`订单不存在: ${orderId}`);
        throw new HttpException('订单不存在', HttpStatus.NOT_FOUND);
      }
      
      // 使用 OrderService 的 updateOrderStatus 方法，这样会触发通知逻辑
      const updateOrderDto: UpdateOrderDto = {
        userId: order.userId,
        orderId: orderId,
        status: status,
      };
      
      await this.orderService.updateOrderStatus(orderId, updateOrderDto, 0); // 使用系统用户ID 0
      this.logger.log(`订单状态更新成功: ${orderId}, 状态: ${status}`);
    } catch (error) {
      this.logger.error(`更新订单状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理余额支付逻辑
   * @param payment 支付记录
   * @param user 用户信息
   */
  private async handleBalancePayment(payment: PaymentEntity, user: MiniprogramUser): Promise<void> {
    try {
      this.logger.log(`处理余额支付: 支付ID=${payment.paymentId}, 用户ID=${user.userId}`);

      const paymentAmount = Number(payment.paymentAmount);
      const currentBalance = Number(user.balance) || 0;

      // 1. 检查用户余额是否充足
      if (currentBalance < paymentAmount) {
        this.logger.error(`用户余额不足: 用户ID=${user.userId}, 当前余额=${currentBalance}, 支付金额=${paymentAmount}`);
        throw new HttpException('用户余额不足', HttpStatus.BAD_REQUEST);
      }

      // 2. 扣除用户余额
      user.balance = currentBalance - paymentAmount;
      user.updateBy = user.userId.toString(); // 记录操作人
      user.updateTime = new Date();
      await this.userRepository.save(user);
      this.logger.log(`用户余额扣除成功: 用户ID=${user.userId}, 剩余余额=${user.balance}`);

      // 3. 更新支付记录状态为成功
      payment.paymentStatus = '2'; // 支付成功
      payment.transactionId = `BALANCE_${this.generateTimestamp()}_${this.generateRandomString(10)}`; // 生成虚拟交易号
      payment.paymentTime = new Date();
      payment.updateBy = user.userId.toString();
      payment.updateTime = new Date();
      await this.paymentRepository.save(payment);
      this.logger.log(`支付记录更新成功: ${payment.paymentId}, 状态: ${payment.paymentStatus}`);

      // 4. 更新订单状态为已支付
      await this.updateOrderStatus(payment.orderId, '2'); // 更新为已支付状态
      this.logger.log(`订单状态更新成功: ${payment.orderId}, 状态: 已支付`);
    } catch (error) {
      this.logger.error(`处理余额支付失败: ${error.message}`);
      throw error; // 抛出异常，由上层捕获处理
    }
  }

  /**
   * 查询支付状态
   * @param orderId 订单ID
   * @param userId 用户ID
   */
  async getPaymentStatus(orderId: string, userId: number): Promise<ResultData> {
    try {
      this.logger.log(`查询支付状态: 订单=${orderId}, 用户=${userId}`);
      const payment = await this.paymentRepository.findOne({
        where: { orderId: orderId, userId: userId },
        order: { createTime: 'DESC' },
      });
      if (!payment) {
        throw new HttpException('支付记录不存在', HttpStatus.NOT_FOUND);
      }
      const result: PaymentResultDto = {
        paymentId: payment.paymentId,
        orderId: payment.orderId,
        paymentStatus: payment.paymentStatus,
        paymentAmount: Number(payment.paymentAmount),
        transactionId: payment.transactionId,
        paymentTime: payment.paymentTime,
      };
      return ResultData.ok(result, '查询成功');
    } catch (error) {
      this.logger.error(`查询支付状态失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('查询失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取用户支付记录列表
   * @param userId 用户ID
   * @param pageNum 页码
   * @param pageSize 页大小
   */
  async getUserPaymentList(userId: number, pageNum: number = 1, pageSize: number = 10): Promise<ResultData> {
    try {
      this.logger.log(`获取用户支付记录: 用户=${userId}, 页码=${pageNum}`);
      const [payments, total] = await this.paymentRepository.findAndCount({
        where: { userId: userId },
        order: { createTime: 'DESC' },
        skip: (pageNum - 1) * pageSize,
        take: pageSize,
      });
      const paymentList: PaymentListDto[] = payments.map((payment) => ({
        paymentId: payment.paymentId,
        orderId: payment.orderId,
        userId: payment.userId,
        paymentMethod: payment.paymentMethod,
        paymentAmount: Number(payment.paymentAmount),
        paymentStatus: payment.paymentStatus,
        transactionId: payment.transactionId,
        paymentTime: payment.paymentTime,
        refundAmount: Number(payment.refundAmount),
        createTime: payment.createTime,
      }));
      return ResultData.ok(
        {
          list: paymentList,
          total: total,
          pageNum: pageNum,
          pageSize: pageSize,
        },
        '查询成功',
      );
    } catch (error) {
      this.logger.error(`获取用户支付记录失败: ${error.message}`);
      throw new HttpException('查询失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 申请退款
   * @param refundRequest 退款请求
   * @param userId 用户ID
   */
  async requestRefund(refundRequest: RefundRequestDto, userId: number): Promise<ResultData> {
    try {
      this.logger.log(`申请退款: 支付ID=${refundRequest.paymentId}, 用户=${userId}`);
      // 查找支付记录
      const payment = await this.paymentRepository.findOne({
        where: { paymentId: refundRequest.paymentId, userId: userId },
      });
      if (!payment) {
        throw new HttpException('支付记录不存在', HttpStatus.NOT_FOUND);
      }
      if (payment.paymentStatus !== '2') {
        throw new HttpException('只有支付成功的订单才能退款', HttpStatus.BAD_REQUEST);
      }
      // 检查退款金额
      const maxRefundAmount = Number(payment.paymentAmount) - Number(payment.refundAmount);
      if (refundRequest.refundAmount > maxRefundAmount) {
        throw new HttpException(`退款金额不能超过${maxRefundAmount}元`, HttpStatus.BAD_REQUEST);
      }
      // 根据支付方式处理退款
      // 特殊处理：如果paymentMethod是1但transactionId是BALANCE格式，说明数据不一致，按余额退款处理
      const isBalanceTransaction = payment.transactionId && payment.transactionId.startsWith('BALANCE_');
      
      if (payment.paymentMethod === '1' && !isBalanceTransaction) {
        // 微信支付退款
        await this.processWechatRefund(payment, refundRequest.refundAmount, refundRequest.refundReason);
      } else if (payment.paymentMethod === '2' || isBalanceTransaction) {
        // 余额支付退款（返还余额）
        if (isBalanceTransaction && payment.paymentMethod === '1') {
          this.logger.warn(`检测到数据不一致: PaymentMethod=1但TransactionId=${payment.transactionId}，按余额退款处理`);
        }
        await this.processBalanceRefund(payment, refundRequest.refundAmount);
      } else if (payment.paymentMethod === '3') {
        // 模拟支付退款，直接标记为成功
        this.logger.log(`模拟支付退款: PaymentId=${payment.paymentId}, Amount=${refundRequest.refundAmount}`);
      } else {
        throw new HttpException('不支持的支付方式退款', HttpStatus.BAD_REQUEST);
      }

      // 更新支付记录
      const newRefundAmount = Number(payment.refundAmount) + refundRequest.refundAmount;
      await this.paymentRepository.update(refundRequest.paymentId, {
        refundAmount: newRefundAmount,
        refundTime: new Date(),
        paymentStatus: newRefundAmount >= Number(payment.paymentAmount) ? '4' : '2', // 全额退款标记为已退款
        updateBy: userId.toString(),
      });
      // 如果是全额退款，更新订单状态
      if (newRefundAmount >= Number(payment.paymentAmount)) {
        await this.orderRepository.update(payment.orderId, {
          status: '5', // 已取消
          cancelReason: refundRequest.refundReason,
          updateBy: userId.toString(),
        });
        this.logger.log(`全额退款，订单状态更新为已取消: ${payment.orderId}`);
      }
      const result: RefundResultDto = {
        paymentId: refundRequest.paymentId,
        refundAmount: refundRequest.refundAmount,
        refundTime: new Date(),
        refundStatus: 'success',
      };
      return ResultData.ok(result, '退款申请成功');
    } catch (error) {
      this.logger.error(`申请退款失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('退款申请失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 查询用户余额
   * @param userId 用户ID
   * @returns 用户余额
   */
  async getUserBalance(userId: number): Promise<ResultData> {
    try {
      this.logger.log(`查询用户余额: ${userId}`);
      // 查询用户
      const user = await this.userRepository.findOne({
        where: { userId: userId },
      });
      if (!user) {
        this.logger.error(`用户不存在: ${userId}`);
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }
      return ResultData.ok({ balance: Number(user.balance) || 0 }, '查询成功');
    } catch (error) {
      this.logger.error(`查询用户余额失败: ${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('查询用户余额失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取用户余额变动记录列表
   * @param userId 用户ID
   * @param pageNum 页码
   * @param pageSize 页大小
   */
  async getUserBalanceChangeList(userId: number, pageNum: number = 1, pageSize: number = 10): Promise<ResultData> {
    try {
      this.logger.log(`获取用户余额变动记录: 用户=${userId}, 页码=${pageNum}, 页大小=${pageSize}`);

      const queryBuilder = this.paymentRepository
        .createQueryBuilder('payment')
        .where('payment.userId = :userId', { userId })
        .andWhere('payment.paymentStatus = :paymentStatus', { paymentStatus: '2' }) // 支付成功的记录
        .andWhere(
          new Brackets((qb) => {
            qb.where('payment.orderId LIKE :rechargePrefix', { rechargePrefix: 'RECHARGE_%' }) // 充值记录
              .orWhere('payment.paymentMethod = :paymentMethod', { paymentMethod: '2' }); // 余额支付的消费记录
          }),
        )
        .orderBy('payment.createTime', 'DESC')
        .skip((pageNum - 1) * pageSize)
        .take(pageSize);

      const [payments, total] = await queryBuilder.getManyAndCount();

      const balanceChangeList: BalanceChangeListDto[] = await Promise.all(
        payments.map(async (payment) => {
          const isRecharge = payment.orderId.startsWith('RECHARGE_');
          let orderInfo: BalanceChangeOrderInfoDto | undefined;

          if (!isRecharge && payment.paymentMethod === '2') {
            // 余额支付的消费记录
            const order = await this.orderRepository.findOne({
              where: { orderId: payment.orderId },
              relations: ['orderItems'], // 加载订单商品
            });

            if (order) {
              orderInfo = {
                orderId: order.orderId,
                items: order.orderItems
                  ? order.orderItems.map((item) => ({
                      productId: item.productId,
                      productName: item.productName,
                      productImage: item.productImage,
                      quantity: item.quantity,
                      price: Number(item.price),
                      totalPrice: Number(item.totalPrice),
                    }))
                  : [],
              };
            }
          }

          return {
            id: payment.paymentId.toString(),
            userId: payment.userId,
            type: isRecharge ? 'recharge' : 'consume',
            amount: isRecharge ? Number(payment.paymentAmount) : -Number(payment.paymentAmount),
            description: isRecharge ? '余额充值' : `订单消费: ${payment.orderId}`,
            time: payment.createTime,
            status: payment.paymentStatus,
            orderInfo: orderInfo,
          };
        }),
      );

      return ResultData.ok(
        {
          list: balanceChangeList,
          total: total,
          pageNum: pageNum,
          pageSize: pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
        '查询成功',
      );
    } catch (error) {
      this.logger.error(`获取用户余额变动记录失败: ${error.message}`);
      throw new HttpException('查询失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 生成时间戳
   */
  private generateTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  /**
   * 生成随机字符串
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 处理微信支付退款
   * @param payment 支付记录
   * @param refundAmount 退款金额
   * @param refundReason 退款原因
   */
  private async processWechatRefund(payment: PaymentEntity, refundAmount: number, refundReason?: string): Promise<void> {
    try {
      this.logger.log(`开始处理微信支付退款: OrderId=${payment.orderId}, RefundAmount=${refundAmount}`);

      // 生成退款单号
      const refundId = `REFUND_${this.generateTimestamp()}_${this.generateRandomString(6)}`;

      // 构建退款请求参数
      const refundParams = {
        out_trade_no: payment.orderId, // 原订单号
        out_refund_no: refundId, // 退款单号
        reason: refundReason || '订单退款',
        amount: {
          refund: Math.floor(refundAmount * 100), // 退款金额，单位：分
          total: Math.floor(Number(payment.paymentAmount) * 100), // 原订单金额，单位：分
          currency: 'CNY',
        },
      };

      // 获取私钥用于签名
      const rootDir = process.cwd();
      const relativePath = this.wechatConfig.pay.keyPath.replace(/^\.\//, '');
      const privateKeyPath = path.join(rootDir, relativePath);

      if (!fs.existsSync(privateKeyPath)) {
        throw new Error('商户私钥文件不存在，请配置正确的私钥路径');
      }

      const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

      // 构建请求签名
      const method = 'POST';
      const url = '/v3/refund/domestic/refunds';
      const requestTimestamp = Math.floor(Date.now() / 1000).toString();
      const requestNonce = this.generateNonceStr();
      const requestBody = JSON.stringify(refundParams);
      const message = `${method}\n${url}\n${requestTimestamp}\n${requestNonce}\n${requestBody}\n`;

      const sign = crypto.createSign('RSA-SHA256');
      sign.update(message);
      const signature = sign.sign(privateKey, 'base64');

      // 构建认证头
      const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.wechatConfig.pay.mchId}",nonce_str="${requestNonce}",timestamp="${requestTimestamp}",serial_no="${this.certSerialNo}",signature="${signature}"`;

      // 调用微信退款API
      this.logger.log(`调用微信退款API: ${JSON.stringify(refundParams)}`);
      const response = await axios.post('https://api.mch.weixin.qq.com/v3/refund/domestic/refunds', refundParams, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: authHeader,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      });

      this.logger.log(`微信退款API响应: ${JSON.stringify(response.data)}`);

      if (response.data.status === 'SUCCESS' || response.data.status === 'PROCESSING') {
        this.logger.log(`微信退款成功: RefundId=${refundId}, Status=${response.data.status}`);
      } else {
        throw new Error(`微信退款失败: ${response.data.status}`);
      }
    } catch (error) {
      this.logger.error(`微信退款处理失败: ${error.message}`, error.stack);
      if (error.response) {
        this.logger.error(`微信退款API错误: ${JSON.stringify(error.response.data)}`);
      }
      throw new HttpException(`微信退款失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 处理余额支付退款（返还余额）
   * @param payment 支付记录
   * @param refundAmount 退款金额
   */
  private async processBalanceRefund(payment: PaymentEntity, refundAmount: number): Promise<void> {
    try {
      this.logger.log(`开始处理余额退款: UserId=${payment.userId}, RefundAmount=${refundAmount}`);

      // 查找用户
      const user = await this.userRepository.findOne({
        where: { userId: payment.userId },
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 返还余额
      const currentBalance = Number(user.balance) || 0;
      user.balance = currentBalance + refundAmount;
      user.updateBy = 'system';
      user.updateTime = new Date();

      await this.userRepository.save(user);

      this.logger.log(`余额退款成功: UserId=${payment.userId}, RefundAmount=${refundAmount}, NewBalance=${user.balance}`);
    } catch (error) {
      this.logger.error(`余额退款处理失败: ${error.message}`, error.stack);
      throw new HttpException(`余额退款失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
