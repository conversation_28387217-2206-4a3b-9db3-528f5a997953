"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ReviewResponseDto {
}
exports.ReviewResponseDto = ReviewResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价ID' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "reviewId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户昵称', required: false }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户头像', required: false }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品ID' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品名称' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "productName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品主图' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "productImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格ID', required: false }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "specId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品规格名称', required: false }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "specName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评分 1-5' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价内容' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '评价图片', type: [String], required: false }),
    __metadata("design:type", Array)
], ReviewResponseDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否匿名评价，0-否 1-是' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "isAnonymous", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商家回复', required: false }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "reply", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '回复时间', required: false }),
    __metadata("design:type", Date)
], ReviewResponseDto.prototype, "replyTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '有用数量' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "likeCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ReviewResponseDto.prototype, "createTime", void 0);
//# sourceMappingURL=review-response.dto.js.map