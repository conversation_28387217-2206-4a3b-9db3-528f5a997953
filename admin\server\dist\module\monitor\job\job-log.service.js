"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const job_log_entity_1 = require("./entities/job-log.entity");
const result_1 = require("../../../common/utils/result");
const export_1 = require("../../../common/utils/export");
let JobLogService = class JobLogService {
    constructor(jobLogRepository) {
        this.jobLogRepository = jobLogRepository;
    }
    async list(query) {
        const entity = this.jobLogRepository.createQueryBuilder('entity');
        if (query.pageSize && query.pageNum) {
            entity.skip(query.pageSize * (query.pageNum - 1)).take(query.pageSize);
        }
        if (query.jobName) {
            entity.andWhere('entity.jobName LIKE :jobName', { jobName: `%${query.jobName}%` });
        }
        if (query.jobGroup) {
            entity.andWhere('entity.jobGroup = :jobGroup', { jobGroup: query.jobGroup });
        }
        if (query.status) {
            entity.andWhere('entity.status = :status', { status: query.status });
        }
        if (query.params?.beginTime && query.params?.endTime) {
            entity.andWhere('entity.createTime BETWEEN :start AND :end', { start: query.params.beginTime, end: query.params.endTime });
        }
        entity.orderBy('entity.createTime', 'DESC');
        const [list, total] = await entity.getManyAndCount();
        return result_1.ResultData.ok({
            list,
            total,
        });
    }
    async addJobLog(jobLog) {
        const log = this.jobLogRepository.create(jobLog);
        await this.jobLogRepository.save(log);
        return result_1.ResultData.ok();
    }
    async clean() {
        await this.jobLogRepository.clear();
        return result_1.ResultData.ok();
    }
    async export(res, body) {
        delete body.pageNum;
        delete body.pageSize;
        const list = await this.list(body);
        const options = {
            sheetName: '调度日志',
            data: list.data.list,
            header: [
                { title: '日志编号', dataIndex: 'jobLogId' },
                { title: '任务名称', dataIndex: 'jobName' },
                { title: '任务组名', dataIndex: 'jobGroup' },
                { title: '调用目标字符串', dataIndex: 'invokeTarget' },
                { title: '日志信息', dataIndex: 'jobMessage' },
                { title: '执行时间', dataIndex: 'createTime' },
            ],
            dictMap: {
                status: {
                    '0': '成功',
                    '1': '失败',
                },
                jobGroup: {
                    SYSTEM: '系统',
                    DEFAULT: '默认',
                },
            },
        };
        (0, export_1.ExportTable)(options, res);
    }
};
exports.JobLogService = JobLogService;
exports.JobLogService = JobLogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(job_log_entity_1.JobLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], JobLogService);
//# sourceMappingURL=job-log.service.js.map