"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryTimeSlotQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DeliveryTimeSlotQueryDto {
    constructor() {
        this.pageNum = 1;
        this.pageSize = 10;
    }
}
exports.DeliveryTimeSlotQueryDto = DeliveryTimeSlotQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    __metadata("design:type", Number)
], DeliveryTimeSlotQueryDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页条数', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页条数必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页条数不能小于1' }),
    (0, class_validator_1.Max)(100, { message: '每页条数不能大于100' }),
    __metadata("design:type", Number)
], DeliveryTimeSlotQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配送时间段', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '配送时间段必须是字符串' }),
    __metadata("design:type", String)
], DeliveryTimeSlotQueryDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否启用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '启用状态必须是整数' }),
    (0, class_validator_1.Min)(0, { message: '启用状态只能是0或1' }),
    (0, class_validator_1.Max)(1, { message: '启用状态只能是0或1' }),
    __metadata("design:type", Number)
], DeliveryTimeSlotQueryDto.prototype, "isActive", void 0);
//# sourceMappingURL=delivery-time-slot-query.dto.js.map